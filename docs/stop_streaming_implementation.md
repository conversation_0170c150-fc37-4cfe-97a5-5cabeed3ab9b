# Stop Streaming Implementation Guide

This document explains how to implement and use the stop streaming functionality for AI chat responses, similar to ChatGPT's stop button feature.

## Overview

The stop streaming functionality allows users to interrupt AI responses while they are being generated. When stopped, the partial response is saved and marked as incomplete, providing a better user experience and preventing unnecessary token usage.

## Backend Implementation

### 1. Streaming Session Management

Each streaming session is assigned a unique ID and tracked in Redis cache:

```python
# For authenticated users
streaming_session_id = f"stream_{user.id}_{ai_response_obj.uuid}"

# For anonymous users  
streaming_session_id = f"anon_stream_{ip_address}_{device_info}_{conversation_id}_{msg_id}"

# Store in cache with 5-minute timeout
cache.set(f"streaming_active:{streaming_session_id}", True, timeout=300)
```

### 2. Modified Streaming Generator

The streaming generator now checks for stop signals on each chunk:

```python
for chunk in ai_response:
    # Check if user requested to stop streaming
    if not cache.get(f"streaming_active:{streaming_session_id}"):
        logger.info(f"Streaming stopped by user for session {streaming_session_id}")
        stopped_by_user = True
        break
    
    # Process chunk...
```

### 3. Incomplete Response Handling

When a response is stopped, it's marked as incomplete in the metadata:

```python
if stopped_by_user:
    if not hasattr(ai_response_obj, 'metadata'):
        ai_response_obj.metadata = {}
    ai_response_obj.metadata["incomplete"] = True
    ai_response_obj.metadata["stopped_by_user"] = True
```

## API Endpoints

### 1. Start Chat (Modified)

**Endpoint:** `POST /api/llm-manager/v1/chats/`

**Response includes streaming session ID:**
```json
{
  "data": {
    "uuid": "message-uuid",
    "streaming_session_id": "stream_user_id_message_uuid",
    "content": "",
    "created_at": "2025-01-01T00:00:00Z"
  }
}
```

### 2. Stop Streaming (New)

**Endpoint:** `POST /api/llm-manager/v1/chats/stop-streaming/`

**Request:**
```json
{
  "streaming_session_id": "stream_user_id_message_uuid"
}
```

**Response:**
```json
{
  "message": "Streaming stopped successfully"
}
```

### 3. Anonymous Endpoints

- **Chat:** `POST /api/llm-manager/v1/anonymous/chats/`
- **Stop:** `POST /api/llm-manager/v1/anonymous/chats/stop-streaming/`

## Frontend Implementation

### 1. Basic Setup

```javascript
let currentStreamingSessionId = null;
let isStreaming = false;

const CHAT_ENDPOINT = '/api/llm-manager/v1/chats/';
const STOP_ENDPOINT = '/api/llm-manager/v1/chats/stop-streaming/';
```

### 2. Sending Messages

```javascript
async function sendMessage(message) {
    setStreamingState(true);
    
    const response = await fetch(CHAT_ENDPOINT, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`
        },
        body: JSON.stringify({
            message: {
                content: { parts: [message] }
            },
            model: 'gpt-4o-mini'
        })
    });
    
    const reader = response.body.getReader();
    // Handle streaming response...
}
```

### 3. Stopping Streams

```javascript
async function stopStreaming() {
    if (!currentStreamingSessionId || !isStreaming) return;
    
    await fetch(STOP_ENDPOINT, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`
        },
        body: JSON.stringify({
            streaming_session_id: currentStreamingSessionId
        })
    });
}
```

### 4. UI State Management

```javascript
function setStreamingState(streaming) {
    isStreaming = streaming;
    document.getElementById('sendBtn').disabled = streaming;
    document.getElementById('stopBtn').disabled = !streaming;
    document.getElementById('messageInput').disabled = streaming;
}
```

## Response Format

### Streaming Chunks

```json
{"data": {"chunk": "Hello"}}
{"data": {"chunk": " world"}}
{"data": {"chunk": "!"}}
```

### Completion Signals

```json
{"data": ["DONE"]}      // Normal completion
{"data": ["STOPPED"]}   // Stopped by user
{"data": ["ERROR"]}     // Error occurred
```

## Error Handling

### Backend Errors

- **400:** Missing streaming_session_id
- **404:** Session not found or already completed
- **500:** Internal server error

### Frontend Handling

```javascript
try {
    const response = await fetch(STOP_ENDPOINT, { /* ... */ });
    if (!response.ok) {
        console.error('Failed to stop streaming');
    }
} catch (error) {
    console.error('Error stopping streaming:', error);
}
```

## Best Practices

### 1. Session Cleanup

- Sessions automatically expire after 5 minutes
- Clean up sessions when streaming completes
- Handle browser refresh/close events

### 2. User Experience

- Show clear visual indicators for streaming state
- Disable input during streaming
- Mark incomplete responses visually
- Provide feedback when stopping

### 3. Performance

- Use appropriate cache timeouts
- Clean up expired sessions
- Handle network interruptions gracefully

### 4. Security

- Validate session ownership
- Rate limit stop requests
- Sanitize session IDs

## Testing

### Manual Testing

1. Start a chat conversation
2. Click stop button during response
3. Verify partial response is saved
4. Check response is marked as incomplete

### Automated Testing

```python
def test_stop_streaming():
    # Start streaming
    response = client.post('/api/llm-manager/v1/chats/', data)
    session_id = extract_session_id(response)
    
    # Stop streaming
    stop_response = client.post('/api/llm-manager/v1/chats/stop-streaming/', {
        'streaming_session_id': session_id
    })
    
    assert stop_response.status_code == 200
```

## Troubleshooting

### Common Issues

1. **Session not found:** Check session ID format and expiration
2. **Stop not working:** Verify cache configuration
3. **Incomplete responses not marked:** Check metadata handling

### Debug Logging

```python
logger.info(f"Streaming session {streaming_session_id} stopped by user request")
logger.info(f"Updated usage: stopped={stopped_by_user}")
```

## Example Implementation

See `docs/stop_streaming_example.html` for a complete working example with HTML/JavaScript frontend.
