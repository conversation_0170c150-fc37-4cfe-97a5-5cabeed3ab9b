## Best Practices for Code
1. Code Formatters: Black
2. Linters: Flake8
3. Static Code Analysis Tools: SonarQube
4. Security-Specific Tools: Safety or django-secure
5. Dependency Management Tools: Poetry
6. Test Coverage Tools: pytest-cov
7. Pre-commit Hooks

### Import Sequence
1. Standard library imports
2. Django imports
3. Django Rest Framework imports
4. Third party imports
5. Local imports

### How to Install or Uninstall Unfold
1. For Install From config/base.py, comment out unfold line
-  
2. For Uninstall From config/base.py, comment in unfold line

##  Why Unregister and Re-register Models?
1. Unregister third-party models to configure Unfold
2. Re-register third-party models to configure Unfold using BaseAdmin class 

# Use Django Extension
## Use Admin Generator, for generator
## Use clean_puc 

## Mypy for type checking   
- mypy.ini file for configuration
- run command : `mypy .` for type checking whole project
- command : `mypy file_name.py` for type checking
    
## Debug Toolbar for debugging
- Use Django Debug Toolbar for debugging purpose
- Check the `config/settings/local.py` file for configuration details
- Use `http://localhost:8000/__debug__/` for debugging
- Or check admin panel debug toolbar

# Development Email configuration
- Use mailpit for development email testing
- Use `8025/` the port for mailpit for example `http://localhost:8025/` 

# Production Email configuration
- Use any email service provider like SendGrid, Amazon SES, etc.
- Check the `.env.example` file for email configuration details

# Google Recaptcha Configuration
- Use Google Recaptcha for security
- Set site key and secret key from Google Recaptcha on the `.env` file
- Check the `.env.example` file for Google Recaptcha configuration details
- First Need RECAPTCHA_ENABLE=True in `.env` file then add the following details in `.env` file
- For the Credential, you need to create a project in Google Cloud Console and enable the Recaptcha Enterprise API.URL: https://console.cloud.google.com/apis/library/recaptchaenterprise.googleapis.com
    ```bash
    RECAPTCHA_ENABLE=False
    RECAPTCHA_BASE_URL=https://recaptchaenterprise.googleapis.com/v1/projects
    RECAPTCHA_PROJECT_ID="devxhub"
    RECAPTCHA_SITE_KEY="site_key"
    GOOGLE_RECAPTCHA_API_KEY="api_key"
    ```
- To implement the reCAPTCHA validation, simply add the following decorator to your view function:
    ```python
    @recaptcha(enabled=settings.RECAPTCHA_ENABLE)
    def view_function(request):
        pass
    ```
- This decorator checks the `X-Captcha-Token` from the request header and validates it with the Google reCAPTCHA API. To use it, ensure that you pass the token in the header using the key `X-Captcha-Token`.
    

# Pgbouncer Configuration
- `infra/pgbounce/userlist.txt` file for pgbouncer configuration with database, username, and password


# Social Login setup
### Login with Google
- Setup google [cloud console](https://console.cloud.google.com/). Collect client_id, client_secret and add callback url.
- Add a button for google login in the login page. Use Authorize url: 
```
https://accounts.google.com/o/oauth2/v2/auth?redirect_uri=http://localhost:3000/en/oauth/google/callback&prompt=consent&response_type=code&client_id=<client_id>&scope=openid%20email%20profile&access_type=offline
```
update `client_id` and `redirect_uri` collected from google cloud console.
- Login to django admin panel and add new **Social Application** for Google. Add the client_id, client_secret, sites, etc.  
- Add `GOOGLE_REDIRECT_URL` in `.env` file
- You with get a code from the callback url, use this code to get access token from backend.
- Token url: `{{domain}}/api/auth/v1/social/google/`, method: POST, data: `{"code": "code from previous step"}`
- You will get access token from the response, use this token to login user.

### Login with Facebook
- Setup facebook [developer app](https://developers.facebook.com/). Collect client_id, client_secret and add callback url.
- Add a button for facebook login in the login page. Use Authorize url: 
```
https://www.facebook.com/v20.0/dialog/oauth?client_id=<client_id>&redirect_uri=http://localhost:3000/en/oauth/facebook/callback&config_id=<config_id>
```
update `client_id`, `redirect_uri` and `config_id` collected from facebook developer app.
- Login to django admin panel and add new **Social Application** for Facebook. Add the client_id, client_secret, sites, etc.
- Add `FACEBOOK_REDIRECT_URL` in `.env` file
- You with get a code from the callback url, use this code to get access token from facebook.
- Token url: `{{domain}}/api/auth/v1/social/facebook/`, method: POST, data: `{"code": "code from previous step"}`
- You will get access token from the response, use this token to login user.


# Multi Language Support
- Use Django Internationalization for multi-language support
- Use `makemessages` for creating language files, for example `python manage.py makemessages -l fr`
- Use `compilemessages` for compiling language files, for example `python manage.py compilemessages`
- Use `django-admin compilemessages` for compiling language files,


# Admin Configuration Guide for New Applications

Enable the admin interface for new applications by following these steps:
add `.env` `ENABLE_UNFOLD=True` for enabling admin interface and `ENABLE_UNFOLD=False` for disabling admin interface, after disabling admin interface it will show default admin interface. **Note**: By default, the admin interface is enabled.


### Register Apps and Models

When creating a new application, it is necessary to configure the admin interface by adding the appropriate settings in the `config/settings/extend/unfold_setting.py` file. Specifically, you need to add the configuration under the SIDEBAR > navigation section.

Here's the required configuration structure:

```python
{
    "title": _("APP_NAME"),
    "collapsible": True,
    "items": [
        {
            "title": _("MODEL_NAME"),
            "link": reverse_lazy("admin:APP_NAME_MODEL_NAME_changelist"),
        },
        # Additional models can be added here
    ]
}
```

Key components:
- `APP_NAME`: The name of your application
- `MODEL_NAME`: The name of the model you want to display
- `admin:APP_NAME_MODEL_NAME_changelist`: The automatically generated admin URL pattern

**Note**: Each model within the application can be added as a separate item in the items list, following the same structure.

# Enable/Disable Features

- Enable/Disable Unfold: `ENABLE_UNFOLD=True` (Default is True) for admin interface.

- Set True enabling stripe payment gateway : `ENABLE_STRIPE=True` (Default is True)

- Set True for production and False for development: `ENABLE_SENTRY=False` (Default is False)

- Set True for production and False for development: `RECAPTCHA_ENABLE=False` (Default is False)

- Swagger API Documentation: `ENABLE_SWAGGER=True` (Default is True)

- Debug Tool Bar: `ENABLE_DEBUG_TOOLBAR=True` (Default is True) for debugging

- Use `ENABLE_OPENAI` for enabling openai, `ENABLE_OPENAI=True` (Default is False)

- `DJANGO_DEFAULT_EMAIL_SERVICE` for setting default email service provider like sendgrid, amazon_ses, smtp etc.

   ```bash
    DJANGO_DEFAULT_EMAIL_SERVICE=sendgrid
    ```
- `DJANGO_DEFAULT_FILE_STORAGE` for setting default file storage provider like s3, local etc. (Default is local)

   ```bash
    DJANGO_DEFAULT_FILE_STORAGE=s3
    ```
    
# How To Install and Enable Pre-Commit Hooks

## 1. Install Pre-Commit
To use pre-commit hooks, install `pre-commit` using pip:

```bash
pip install pre-commit
```

## 2. Enable Pre-Commit Hooks
Once the `.pre-commit-config.yaml` file is created, install the hooks locally:

```bash
pre-commit install
```

---

## 3. Run Pre-Commit Manually (Optional)
To check and apply all pre-commit hooks manually, run:

```bash
pre-commit run --all-files
```

---

## 4. Test Pre-Commit Hook
To test if the pre-commit hooks are working, make a commit:

```bash
git add .
git commit -m "Test pre-commit hook"
```

If there are any formatting or linting errors, pre-commit will automatically fix them. If a hook fails, you must fix the reported issues before committing again.

---

## 5. Update Pre-Commit Hooks (Optional)
To update all pre-commit hooks to their latest versions, run:

```bash
pre-commit autoupdate
```

---

## 6. Debugging Pre-Commit Issues
If pre-commit isn't working properly, try the following:

```bash
pre-commit clean
pre-commit reinstall
pre-commit run --all-files
```

## 7. Disable Pre-Commit

To completely uninstall pre-commit from the repository, run:

```bash
pre-commit uninstall
```

# AI Service Documentation
## **Getting Started**
### **1. Setting Up API Keys**
Ensure that the API keys for the AI providers are stored in your Django settings. For example:
```python
# settings.py
OPENAI_API_KEY = "your_openai_api_key"
DEEPSEEK_API_KEY = "your_deepseek_api_key"
```
### **2. Installing Dependencies**
Make sure the required dependencies are installed. For example:
```bash
pip install openai
```

## **Using the AI Service**
### **1. Creating a Handler**
To interact with an AI provider, **Create** the `AIHandlerFactory` to select the appropriate handler.
```python
from  apps.cross_cutting.ai_service  import  DeepSeekHandler, OpenAIHandler

class  AIHandlerFactory:
	@staticmethod
	def  get_handler(provider):
		if  provider  ==  "dxh-1":
			return  OpenAIHandler()
		elif  provider  ==  "dxh-2":
			return  DeepSeekHandler()
		else:
			raise  ValueError("Invalid AI provider selected")
```
 The factory selects the handler based on the provider name.
#### Example:
```python
from apps....service import AIHandlerFactory

openai_handler = AIHandlerFactory.get_handler("dxh-1")
deepseek_handler = AIHandlerFactory.get_handler("dxh-2")
```
### **2. Sending a Prompt**
To send a prompt to the AI, use the `send_text_request` method of the handler. The prompt must be in the standardized format:
```python
prompts = [
    {"role": "user", "content": "Tell me a joke about cats."}
]
```
#### Example:
```python
# Send a prompt to OpenAI
response = openai_handler.send_text_request(prompts)
# Send a prompt to DeepSeek
response = deepseek_handler.send_text_request(prompts)
```
### **3. Handling the Response**
The response from the AI is returned in a standardized format. You can access the content of the response as follows:
```python
ai_message = response.choices[0].message.content
```
#### Example:
```python
if response:
    print(f"AI Response: {response.choices[0].message.content}")
else:
    print("Error retrieving AI response.")
```
## **Standardized Prompt Format**
All prompts and responses must follow the standardized format to ensure consistency across different AI providers.
### **Prompt Format**
```python
    {"role": "user", "content": "Your prompt here."},
    {"role": "assistant", "content": "Previous AI response here."},
	...
```
- **`role`**: Can be `user` or `assistant` or `system`.
- **`content`**: The text content of the message.
 ### **Response Format**
The AI response is returned in the following format:
```python
{
	"choices": [
        {
            "message": {              
				 "role": "assistant",
                 "content": "The AI's response."
             }
		 }
	 ]
}
```

# Cloud Storage Provider Service Documentation
## **Overview**
The `CloudStorageProviderService` consists of the following components:
1. **`StorageProvider` (Abstract Base Class)**: Defines the interface for handling authentication with cloud storage providers.
2. **`GoogleStorageProvider`**: Implements the `StorageProvider` interface for Google Drive.
3.  **`CloudStorageProviderService`**: A service layer that provides a factory method to retrieve the appropriate storage provider based on the provider name.
## **Getting Started**
### **1. Setting Up Configuration**
Ensure that the required configuration values are stored in your Django settings. For example:
```python
# settings.py
SOCIAL_AUTH_GOOGLE_OAUTH2_KEY = "your_google_client_id"
SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET = "your_google_client_secret"
GOOGLE_REDIRECT_URL = "your_google_redirect_uri"
```
### **2. Installing Dependencies**
Make sure the required dependencies are installed. For example:
```bash
pip install requests google-auth
```
## **
### Set up Google Console

- Setup google [cloud console](https://console.cloud.google.com/). Collect client_id, client_secret and add callback url.
- - Add `Google Drive API` and `Google Picker API` in console .
- - Provide google picker api key to frontend

- Add a button for google login in the login page. Use Authorize url:

```
https://accounts.google.com/o/oauth2/v2/auth?redirect_uri=<your_callback_url>&prompt=consent&response_type=code&client_id=<client_id>&scope=openid%20email%20profile%20https://www.googleapis.com/auth/drive.file&access_type=offline
```
## **Using the Cloud Storage Provider Service**
### **1. Retrieving a Storage Provider**
To interact with a cloud storage provider, use the `CloudStorageProviderService` to retrieve the appropriate provider instance.
#### Example:
```python
from ...service import CloudStorageProviderService
# Retrieve the Google Drive provider
storage_provider = CloudStorageProviderService.get_provider("google_drive")
```
### **2. Exchanging an Authorization Code**
To exchange an authorization code for an access token, use the `exchange_code` method of the provider.
#### Example:
```python
authorization_code = "your_authorization_code"
try:
    token_data = storage_provider.exchange_code(authorization_code)
    print("Token Data:", token_data)
except Exception as e:
    print(f"Error exchanging code: {e}")
```
## **Example Workflow**
### **1. Initialize the Service**
```python
from ...service import CloudStorageProviderService
# Retrieve the Google Drive provider
storage_provider = CloudStorageProviderService.get_provider("google_drive")
```
### **2. Exchange the Authorization Code**
```python
authorization_code = "your_authorization_code"
try:
    token_data = storage_provider.exchange_code(authorization_code)
    print("Token Data:", token_data)
except Exception as e:
    print(f"Error exchanging code: {e}")
```
### **3. Create a model**
- Need to save the `access token `, `refresh token` , `expire_at` and `token type`
- Provide access token and Google picker api key for chose and pick file from drive



# Try to keep class name and file name should be same.

# Packages build : 

1. Go to the path `django-admin-boilerplate/packages/dxh-librarie`
2. Run the command `python setup.py bdist_wheel sdist`
3. Install all dependencies in the `requirements/local.txt` file then run local file 
3. Use `pip install .` 




# add the relation like this
```
"identity.Group",
groups = models.ManyToManyField(
        "identity.Group",
        related_name="group_users",
        verbose_name=_("Groups"),
        help_text=_("The groups this user belongs to."),
    )
```
