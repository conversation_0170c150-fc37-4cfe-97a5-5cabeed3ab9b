<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stop Streaming Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .chat-container {
            border: 1px solid #ddd;
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        .ai-message {
            background-color: #e9ecef;
            color: #333;
        }
        .incomplete {
            border-left: 4px solid #ffc107;
            background-color: #fff3cd;
        }
        .input-container {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        input[type="text"] {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .send-btn {
            background-color: #007bff;
            color: white;
        }
        .stop-btn {
            background-color: #dc3545;
            color: white;
        }
        .stop-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.streaming {
            background-color: #d4edda;
            color: #155724;
        }
        .status.stopped {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>AI Chat with Stop Streaming</h1>
    
    <div id="status" class="status" style="display: none;"></div>
    
    <div id="chatContainer" class="chat-container"></div>
    
    <div class="input-container">
        <input type="text" id="messageInput" placeholder="Type your message..." />
        <button id="sendBtn" class="send-btn" onclick="sendMessage()">Send</button>
        <button id="stopBtn" class="stop-btn" onclick="stopStreaming()" disabled>Stop</button>
    </div>
    
    <div>
        <h3>Instructions:</h3>
        <ul>
            <li>Type a message and click "Send" to start a conversation</li>
            <li>While the AI is responding, you can click "Stop" to interrupt the response</li>
            <li>Incomplete responses will be marked with a yellow border</li>
            <li>The partial response will be saved and displayed</li>
        </ul>
    </div>

    <script>
        let currentStreamingSessionId = null;
        let isStreaming = false;
        
        // Configuration - update these URLs to match your backend
        const API_BASE_URL = 'http://localhost:8000/api/llm-manager/v1';
        const CHAT_ENDPOINT = `${API_BASE_URL}/chats/`;
        const STOP_ENDPOINT = `${API_BASE_URL}/chats/stop-streaming/`;
        
        // For anonymous users, use these endpoints instead:
        // const CHAT_ENDPOINT = `${API_BASE_URL}/anonymous/chats/`;
        // const STOP_ENDPOINT = `${API_BASE_URL}/anonymous/chats/stop-streaming/`;
        
        function addMessage(content, isUser = false, isIncomplete = false) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
            
            if (isIncomplete) {
                messageDiv.classList.add('incomplete');
                content += ' [Response was stopped]';
            }
            
            messageDiv.textContent = content;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            
            return messageDiv;
        }
        
        function updateStatus(message, type = 'streaming') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            
            if (type !== 'streaming') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }
        }
        
        function setStreamingState(streaming) {
            isStreaming = streaming;
            document.getElementById('sendBtn').disabled = streaming;
            document.getElementById('stopBtn').disabled = !streaming;
            document.getElementById('messageInput').disabled = streaming;
        }
        
        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message) return;
            
            // Add user message to chat
            addMessage(message, true);
            messageInput.value = '';
            
            setStreamingState(true);
            updateStatus('AI is responding...', 'streaming');
            
            try {
                const response = await fetch(CHAT_ENDPOINT, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // Add authorization header if using authenticated endpoints
                        // 'Authorization': `Bearer ${your_access_token}`
                    },
                    body: JSON.stringify({
                        message: {
                            content: {
                                parts: [message]
                            }
                        },
                        model: 'gpt-4o-mini' // or your preferred model
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let aiMessageDiv = null;
                let fullResponse = '';
                let wasStoppedByUser = false;
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n').filter(line => line.trim());
                    
                    for (const line of lines) {
                        try {
                            const data = JSON.parse(line);
                            
                            if (data.data && data.data.streaming_session_id) {
                                // Store the streaming session ID for potential stop requests
                                currentStreamingSessionId = data.data.streaming_session_id;
                                console.log('Streaming session started:', currentStreamingSessionId);
                            }
                            
                            if (data.data && data.data.chunk) {
                                // Add AI response chunk
                                fullResponse += data.data.chunk;
                                
                                if (!aiMessageDiv) {
                                    aiMessageDiv = addMessage('');
                                }
                                
                                aiMessageDiv.textContent = fullResponse;
                            }
                            
                            if (data.data && Array.isArray(data.data)) {
                                if (data.data[0] === 'DONE') {
                                    updateStatus('Response completed', 'success');
                                } else if (data.data[0] === 'STOPPED') {
                                    wasStoppedByUser = true;
                                    updateStatus('Response stopped by user', 'stopped');
                                } else if (data.data[0] === 'ERROR') {
                                    updateStatus('Error occurred during streaming', 'error');
                                }
                            }
                        } catch (e) {
                            console.error('Error parsing JSON:', e);
                        }
                    }
                }
                
                // Mark message as incomplete if it was stopped
                if (wasStoppedByUser && aiMessageDiv) {
                    aiMessageDiv.classList.add('incomplete');
                    aiMessageDiv.textContent += ' [Response was stopped]';
                }
                
            } catch (error) {
                console.error('Error:', error);
                addMessage('Error: Failed to get AI response');
                updateStatus('Error occurred', 'error');
            } finally {
                setStreamingState(false);
                currentStreamingSessionId = null;
            }
        }
        
        async function stopStreaming() {
            if (!currentStreamingSessionId || !isStreaming) {
                return;
            }
            
            try {
                const response = await fetch(STOP_ENDPOINT, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // Add authorization header if using authenticated endpoints
                        // 'Authorization': `Bearer ${your_access_token}`
                    },
                    body: JSON.stringify({
                        streaming_session_id: currentStreamingSessionId
                    })
                });
                
                if (response.ok) {
                    console.log('Streaming stopped successfully');
                    updateStatus('Stopping response...', 'stopped');
                } else {
                    console.error('Failed to stop streaming');
                }
            } catch (error) {
                console.error('Error stopping streaming:', error);
            }
        }
        
        // Allow sending message with Enter key
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !isStreaming) {
                sendMessage();
            }
        });
    </script>
</body>
</html>
