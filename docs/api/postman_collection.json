{"info": {"_postman_id": "bc11d953-22ee-4459-b9ea-04b9cc8d39de", "name": "default-django-project", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "28739642", "_collection_link": "https://interstellar-escape-585239.postman.co/workspace/Backend~79604985-b284-4b40-a393-521ca156b8d8/collection/28739642-bc11d953-22ee-4459-b9ea-04b9cc8d39de?action=share&source=collection_link&creator=28739642"}, "item": [{"name": "identity", "item": [{"name": "Register", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"SecurePass123!@#\",\n    \"confirm_password\": \"SecurePass123!@#\",\n    \"username\": \"admin9\",\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON><PERSON>\",\n    \"language\": \"en\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/register/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "register", ""]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"code\": \"417742\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/verification/email/verify/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "verification", "email", "verify", ""]}}, "response": []}, {"name": "Verification by link", "request": {"method": "GET", "header": [], "url": {"raw": "{{domain}}/api/auth/v1/verify/email/link/?code=610714", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "verify", "email", "link", ""], "query": [{"key": "code", "value": "610714"}]}}, "response": []}, {"name": "validate email", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"Updated 1\",\n    \"last_name\": \"Name\",\n    \"phone\": \"+1987654321\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/validate-email?email=<EMAIL>", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "validate-email"], "query": [{"key": "email", "value": "<EMAIL>"}]}}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "pm.environment.set(\"AUTHORIZAT<PERSON>\", \"Bearer \" + jsonData.data.tokens.access);", "pm.environment.set(\"REFRESH_TOKEN\", \"\" + jsonData.data.tokens.refresh);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"SecurePass123!@#\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/login/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "login", ""]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTczNzk4NzkyNywiaWF0IjoxNzM3OTAxNTI3LCJqdGkiOiI1MDNhZjNhYTE2Nzk0MTFhODA2MTE3MWY4MTliMjBmZCIsInVzZXJfaWQiOjF9.Kd7eMgfIabgdeOGHT_w1iquRCNgL3Y1J633CQPBlm30\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/logout/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "logout", ""]}}, "response": []}, {"name": "auth me", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"Updated 1\",\n    \"last_name\": \"Name\",\n    \"phone\": \"+1987654321\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/me/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "me", ""]}}, "response": []}, {"name": "Send Verification Code (Email)", "request": {"method": "POST", "header": [{"key": "", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"type\": \"PASSWORD_RESET_OTP\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/verification/email/send/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "verification", "email", "send", ""]}}, "response": []}, {"name": "Forgot Password", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"captcha_token\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/password/forgot/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "password", "forgot", ""]}}, "response": []}, {"name": "Reset Password", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"code\": \"951859\",\n    \"new_password\": \"password123!@#\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/password/reset/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "password", "reset", ""]}}, "response": []}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTczODA1NDQ3NiwiaWF0IjoxNzM3OTY4MDc2LCJqdGkiOiJkZTIyYmIxMWZmYzM0ZDc2YTVjYWE4ZmRiZmIyNzQwZSIsInVzZXJfaWQiOjEwfQ.aB54kUTBpV5EQ7TR0Gq1pGCEdVOsDXRSlPr7V3E0o0c\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/token/refresh/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "token", "refresh", ""], "query": [{"key": "", "value": null, "disabled": true}]}}, "response": []}, {"name": "Change Password", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"old_password\": \"password123!@#\",\n    \"new_password\": \"SecurePass123!@#\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/password/change/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "password", "change", ""]}}, "response": []}, {"name": "Social Auth", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n\t\"provider\":\"google\",\n    \"code\": \"4/0ASVgi3L0OsENx0qW0knejARruSr3kFtem8mXc0Wn1qzOxqI_nJ1IBv-CpvjAS_u6f4_rAQ\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/social/callback/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "social", "callback", ""]}}, "response": []}, {"name": "Send Verification Code (Phone)", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+*************\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/verification/phone/send/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "verification", "phone", "send", ""]}}, "response": []}, {"name": "Verify by Phone", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"code\": \"476373\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/verification/phone/verify/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "verification", "phone", "verify", ""]}}, "response": []}, {"name": "MFA Setup", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/mfa/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "mfa", ""]}}, "response": []}, {"name": "Verify MFA", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"code\": \"482937\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/mfa/verify/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "mfa", "verify", ""]}}, "response": []}, {"name": "MFA Disable", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/auth/v1/mfa/disable/", "host": ["{{domain}}"], "path": ["api", "auth", "v1", "mfa", "disable", ""]}}, "response": []}]}, {"name": "core", "item": [{"name": "country", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/core/v1/countries/77c286d7-3753-4927-8e47-3dd872f3b162/", "host": ["{{domain}}"], "path": ["api", "core", "v1", "countries", "77c286d7-3753-4927-8e47-3dd872f3b162", ""]}}, "response": []}, {"name": "company", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}, {"key": "X-Request-ID", "value": "215d1116d770478bb99165cc2d39455e", "type": "text"}], "url": {"raw": "{{domain}}/api/core/v1/companies/3dc920ec-3fc9-4219-8576-160b9e5708b1/", "host": ["{{domain}}"], "path": ["api", "core", "v1", "companies", "3dc920ec-3fc9-4219-8576-160b9e5708b1", ""]}}, "response": []}, {"name": "state", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/core/v1/states/3dc920ec-3fc9-4219-8576-160b9e5708b1/", "host": ["{{domain}}"], "path": ["api", "core", "v1", "states", "3dc920ec-3fc9-4219-8576-160b9e5708b1", ""]}}, "response": []}, {"name": "contact info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/core/v1/contact-infos/", "host": ["{{domain}}"], "path": ["api", "core", "v1", "contact-infos", ""]}}, "response": []}, {"name": "system setting", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/core/v1/system-settings/", "host": ["{{domain}}"], "path": ["api", "core", "v1", "system-settings", ""]}}, "response": []}]}, {"name": "files", "item": [{"name": "Files Upload", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "files[]", "type": "file", "src": "/home/<USER>/Downloads/468410268_8515501131911854_8239694681509296449_n.jpg"}, {"key": "files[]", "type": "file", "src": "/home/<USER>/Downloads/468410268_8515501131911854_8239694681509296449_n.jpg"}]}, "url": {"raw": "{{domain}}/api/files/v1/upload/", "host": ["{{domain}}"], "path": ["api", "files", "v1", "upload", ""]}}, "response": []}, {"name": "List of uploaded files", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/files/v1/", "host": ["{{domain}}"], "path": ["api", "files", "v1", ""]}}, "response": []}, {"name": "Search Files Endpoint", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/files/v1/search/?q=file&type=jpg", "host": ["{{domain}}"], "path": ["api", "files", "v1", "search", ""], "query": [{"key": "q", "value": "file"}, {"key": "type", "value": "jpg"}]}}, "response": []}, {"name": "Get File Details Endpoint", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/files/v1/12/", "host": ["{{domain}}"], "path": ["api", "files", "v1", "12", ""]}}, "response": []}, {"name": "Download a file Endpoint", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/file-manager/v1/files/12/download/", "host": ["{{domain}}"], "path": ["api", "file-manager", "v1", "files", "12", "download", ""]}}, "response": []}, {"name": "Batch Download Files Endpoint", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"file_ids\": [12, 13]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/files/v1/batch/download/", "host": ["{{domain}}"], "path": ["api", "files", "v1", "batch", "download", ""]}}, "response": []}]}, {"name": "users", "item": [{"name": "List Users (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/users/v1/", "host": ["{{domain}}"], "path": ["api", "users", "v1", ""]}}, "response": []}, {"name": "Create User (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"username\": \"towfiq1\",\n    \"password\": \"SecurePass123!@#\",\n    \"first_name\": \"Towfiq1\",\n    \"last_name\": \"devxhub\",\n    \"phone\": \"+*************\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/users/v1/", "host": ["{{domain}}"], "path": ["api", "users", "v1", ""]}}, "response": []}, {"name": "Get User Details (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/users/v1/26/", "host": ["{{domain}}"], "path": ["api", "users", "v1", "26", ""]}}, "response": []}, {"name": "Update User (Admin)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"Towfiq12\",\n    \"last_name\": \"devxhub\",\n    \"phone\": \"+*************\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/users/v1/26/", "host": ["{{domain}}"], "path": ["api", "users", "v1", "26", ""]}}, "response": []}, {"name": "Delete User (Admin)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/users/v1/26/", "host": ["{{domain}}"], "path": ["api", "users", "v1", "26", ""]}}, "response": []}, {"name": "Request Account Deletion (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/users/v1/26/deletion/request/", "host": ["{{domain}}"], "path": ["api", "users", "v1", "26", "deletion", "request", ""]}}, "response": []}, {"name": "<PERSON><PERSON> Account <PERSON> (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/users/v1/26/deletion/cancel/", "host": ["{{domain}}"], "path": ["api", "users", "v1", "26", "deletion", "cancel", ""]}}, "response": []}, {"name": "Export User Data (Admin)", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/users/v1/26/export/", "host": ["{{domain}}"], "path": ["api", "users", "v1", "26", "export", ""]}}, "response": []}, {"name": "Get Preferences (Authenticated user)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/users/v1/preferences/", "host": ["{{domain}}"], "path": ["api", "users", "v1", "preferences", ""]}}, "response": []}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"<PERSON><PERSON><PERSON>\",\n    \"language\": \"fr\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/users/v1/users/update-profile/", "host": ["{{domain}}"], "path": ["api", "users", "v1", "users", "update-profile", ""]}}, "response": []}, {"name": "Update Preferences (Authenticated user)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"language\": \"en\",\n    \"timezone\": \"UTC\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/users/v1/preferences/", "host": ["{{domain}}"], "path": ["api", "users", "v1", "preferences", ""]}}, "response": []}, {"name": "Update Language (Authenticated user)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"language\": \"en\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/users/v1/preferences/language/", "host": ["{{domain}}"], "path": ["api", "users", "v1", "preferences", "language", ""]}}, "response": []}, {"name": "Update Timezone (Authenticated user)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"timezone\": \"UTC\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/users/v1/preferences/timezone/", "host": ["{{domain}}"], "path": ["api", "users", "v1", "preferences", "timezone", ""]}}, "response": []}, {"name": "Search Users (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/users/v1/search/?q=", "host": ["{{domain}}"], "path": ["api", "users", "v1", "search", ""], "query": [{"key": "q", "value": ""}]}}, "response": []}, {"name": "Filter Users (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/users/v1/filter/?status=True", "host": ["{{domain}}"], "path": ["api", "users", "v1", "filter", ""], "query": [{"key": "status", "value": "True"}, {"key": "role", "value": "3", "disabled": true}, {"key": "email_verified", "value": "True", "disabled": true}, {"key": "phone_verified", "value": "True", "disabled": true}, {"key": "date_joined_after", "value": "2025-01-03", "disabled": true}, {"key": "date_joined_before", "value": "2025-01-03", "disabled": true}]}}, "response": []}, {"name": "Get a list of group", "request": {"method": "GET", "header": []}, "response": []}, {"name": "Get Group Details", "request": {"method": "GET", "header": []}, "response": []}, {"name": "get group permission list", "request": {"method": "GET", "header": []}, "response": []}, {"name": "Get Group Uses", "request": {"method": "GET", "header": []}, "response": []}, {"name": "Get a permission list", "request": {"method": "GET", "header": []}, "response": []}, {"name": "Get permission details", "request": {"method": "GET", "header": []}, "response": []}]}, {"name": "products", "item": [{"name": "Create a Category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Electronics\",\n    \"description\": \"Category for all electronic products\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/products/v1/categories/", "host": ["{{domain}}"], "path": ["api", "products", "v1", "categories", ""]}}, "response": []}, {"name": "Edit a category", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Electronics 1\",\n    \"description\": \"Category for all electronic products\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/products/v1/categories/1/", "host": ["{{domain}}"], "path": ["api", "products", "v1", "categories", "1", ""]}}, "response": []}, {"name": "Get list of categories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/products/v1/categories/", "host": ["{{domain}}"], "path": ["api", "products", "v1", "categories", ""]}}, "response": []}, {"name": "Get category details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/products/v1/categories/1/", "host": ["{{domain}}"], "path": ["api", "products", "v1", "categories", "1", ""]}}, "response": []}, {"name": "Delete a specific category", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/products/v1/categories/2/", "host": ["{{domain}}"], "path": ["api", "products", "v1", "categories", "2", ""]}}, "response": []}, {"name": "Get a list of products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/products/v1/products", "host": ["{{domain}}"], "path": ["api", "products", "v1", "products"], "query": [{"key": "query", "value": "shoes", "disabled": true}, {"key": "category", "value": "Footwear", "disabled": true}, {"key": "min_price", "value": "50", "disabled": true}, {"key": "max_price", "value": "200", "disabled": true}]}}, "response": []}, {"name": "Get the details of a product", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/products/1/", "host": ["{{domain}}"], "path": ["api", "products", "1", ""]}}, "response": []}, {"name": "Create a new product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Running Shoes 1", "type": "text"}, {"key": "description", "value": "High-quality running shoes for athletes.", "type": "text"}, {"key": "price", "value": "120.00", "type": "text"}, {"key": "category", "value": "1", "type": "text"}, {"key": "available", "value": "true", "type": "text"}, {"key": "image", "type": "file", "src": "/home/<USER>/Downloads/468410268_8515501131911854_8239694681509296449_n.jpg"}]}, "url": {"raw": "{{domain}}/api/products/v1/products/", "host": ["{{domain}}"], "path": ["api", "products", "v1", "products", ""]}}, "response": []}, {"name": "Update a product", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Running Shoes", "type": "text"}, {"key": "description", "value": "High-quality running shoes for athletes.", "type": "text"}, {"key": "price", "value": "120.00", "type": "text"}, {"key": "category", "value": "1", "type": "text"}, {"key": "available", "value": "True", "type": "text"}, {"key": "image", "type": "file", "src": "/home/<USER>/Downloads/468410268_8515501131911854_8239694681509296449_n.jpg"}]}, "url": {"raw": "{{domain}}/api/products/v1/products/2/", "host": ["{{domain}}"], "path": ["api", "products", "v1", "products", "2", ""]}}, "response": []}, {"name": "Delete a product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{domain}}/api/products/v1/products/100/", "host": ["{{domain}}"], "path": ["api", "products", "v1", "products", "100", ""]}}, "response": []}]}, {"name": "notification", "item": [{"name": "Get Notifications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/notifications/v1/", "host": ["{{domain}}"], "path": ["api", "notifications", "v1", ""]}}, "response": []}, {"name": "Create Notification", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"type_code\": \"email_verification\",\n    \"context\": {\n        \"otp\": \"123456\"\n    }\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/notifications/v1/", "host": ["{{domain}}"], "path": ["api", "notifications", "v1", ""]}}, "response": []}, {"name": "Update Notification", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/notifications/v1/1/", "host": ["{{domain}}"], "path": ["api", "notifications", "v1", "1", ""]}}, "response": []}, {"name": "Update Notification Copy", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/notifications/v1/1/", "host": ["{{domain}}"], "path": ["api", "notifications", "v1", "1", ""]}}, "response": []}, {"name": "Retrieve notification preferences", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/notifications/v1/preferences/", "host": ["{{domain}}"], "path": ["api", "notifications", "v1", "preferences", ""]}}, "response": []}, {"name": "Update Notification Preferences", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"notification_type\": [\"email_verification\"],\n    \"email\": false,\n    \"push\": true,\n    \"sms\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/notifications/v1/preferences/", "host": ["{{domain}}"], "path": ["api", "notifications", "v1", "preferences", ""]}}, "response": []}, {"name": "Retrieve email templates (Admin Only)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/notifications/v1/templates/", "host": ["{{domain}}"], "path": ["api", "notifications", "v1", "templates", ""]}}, "response": []}, {"name": "Create a new email template (Admin Only)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Welcome Email\",\n    \"code\": \"welcome_email\",\n    \"subject\": \"Welcome to Our Platform\",\n    \"content\": \"Hello {{ name }}, welcome to our platform!\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/notifications/v1/templates/", "host": ["{{domain}}"], "path": ["api", "notifications", "v1", "templates", ""]}}, "response": []}]}, {"name": "conversation", "item": [{"name": "Open AI Chat API", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"conversation_id\": \"session_12345\",\n  \"model\": \"gpt-4o\",\n  \"action\": \"next\",\n  \"timezone\": \"Europe/Helsinki\",\n  \"timezone_offset_min\": -120,\n  \"websocket_request_id\": \"a2f2c7bf-f4ef-4909-b6ab-bf5705804bd9\",\n  \"parent_message_id\": \"aaa1fe93-a3b7-40e0-8cc2-0725ec3470ba\",\n  \"message\": {\n    \"author\": {\n      \"role\": \"user\"\n    },\n    \"create_time\": 1736179638.429,\n    \"id\": \"msg_003\",\n    \"content\": {\n      \"content_type\": \"text\",\n      \"parts\": [\"Hello\"]\n    },\n    \"metadata\": {\n      \"attachments\": []\n    }\n  }\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api/conversation/v1/chat/", "host": ["{{domain}}"], "path": ["api", "conversation", "v1", "chat", ""]}}, "response": []}]}, {"name": "miscellaneous", "item": [{"name": "site content", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AUTHORIZATION}}", "type": "text"}], "url": {"raw": "{{domain}}/api/miscellaneous/v1/site-content/faq/", "host": ["{{domain}}"], "path": ["api", "miscellaneous", "v1", "site-content", "faq", ""]}}, "response": []}]}]}