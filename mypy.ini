[mypy]
disallow_untyped_calls = False
disallow_untyped_decorators = False
disallow_incomplete_defs = False
ignore_missing_imports = True
no_implicit_optional = False
strict_optional = True
check_untyped_defs = True
warn_return_any = True
warn_unreachable = True
disable_error_code = var-annotated, attr-defined, arg-type,assignment,no-redef
exclude = ^(env/|venv/|.venv/).*$

plugins =
    mypy_django_plugin.main

[mypy_django_plugin]
ignore_missing_model_attributes = True

[mypy-*.migrations.*]
ignore_errors = True

[mypy.plugins.django-stubs]
django_settings_module = "config.settings"
