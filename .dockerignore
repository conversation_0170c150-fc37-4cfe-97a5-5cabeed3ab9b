# Ignore Python bytecode and cache
__pycache__/
*.py[cod]
*.pyo

# Ignore environment files
# .env
.env.local
.env.dev
.env.prod
.env.production
.env.staging
.env.test

# Ignore virtual environments
venv/
env/

# Ignore unnecessary migration cache files
**/migrations/*.pyc
**/migrations/__pycache__/

# Ignore database files
*.sqlite3

# Ignore static and media files (if they are managed outside the Docker image)
staticfiles/
media/

# Ignore logs
*.log

# Ignore compiled files
*.so

# Ignore IDE files and directories
.idea/
.vscode/

# Ignore OS files
.DS_Store
Thumbs.db

# Ignore other version control files
.git/
.gitignore
.github/

# Ignore everything in the infra folder
.dockerignore

# Allow specific files that are needed for the build
# Ignore everything in the infra folder except specific files
infra/
!infra/docker/
!infra/docker/entrypoint.dev.sh
!infra/docker/entrypoint.prod.sh

# Readme file
README.md

# Makefile
Makefile
makefile
