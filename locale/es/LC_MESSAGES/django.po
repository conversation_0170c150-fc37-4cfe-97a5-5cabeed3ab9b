# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-28 17:47+0600\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? "
"1 : 2;\n"

#: apps/core/apps.py:7
msgid "Core"
msgstr ""

#: apps/core/models/address.py:13 apps/core/models/company.py:70
#: apps/core/models/contact_info.py:12 apps/core/models/country.py:11
#: apps/core/models/element.py:12 apps/core/models/state.py:11
#: apps/core/models/system_setting.py:12 apps/core/models/timezone.py:12
#: apps/file_manager/models/file_upload.py:13
#: apps/identity/models/mfa_setup.py:16 apps/identity/models/user.py:15
#: apps/identity/models/user_session.py:15
#: apps/identity/models/verification_code.py:17
#: apps/identity/models/verification_token.py:19
#: apps/miscellaneous/models/site_content.py:13 apps/notification/models.py:68
#: apps/notification/models.py:95 apps/notification/models.py:138
#: apps/notification/models.py:165 apps/notification/models.py:210
#: apps/payment/models.py:44 apps/payment/models.py:109
#: apps/payment/models.py:150 apps/payment/models.py:179
#: apps/product/models/category.py:12 apps/product/models/product.py:13
#: apps/user/models/role.py:12 apps/user/models/user_preference.py:16
#: apps/user/models/user_role.py:14
msgid "Company"
msgstr ""

#: apps/core/models/address.py:20
msgid "Address Type"
msgstr ""

#: apps/core/models/address.py:21
msgid "Type of address, e.g., 'billing' or 'shipping'."
msgstr ""

#: apps/core/models/address.py:27 apps/core/models/company.py:52
#: apps/core/models/country.py:49 apps/core/models/state.py:29
#: apps/core/models/timezone.py:36 apps/identity/models/user.py:88
msgid "Country"
msgstr ""

#: apps/core/models/address.py:35 apps/core/models/state.py:34
msgid "State"
msgstr ""

#: apps/core/models/address.py:42 apps/core/models/company.py:60
msgid "City"
msgstr ""

#: apps/core/models/address.py:48
msgid "Address Line 1"
msgstr ""

#: apps/core/models/address.py:54
msgid "Address Line 2"
msgstr ""

#: apps/core/models/address.py:58
msgid "Postal Code"
msgstr ""

#: apps/core/models/address.py:64
msgid "Address"
msgstr ""

#: apps/core/models/address.py:65
msgid "Addresses"
msgstr ""

#: apps/core/models/company.py:10
msgid "Company Name"
msgstr ""

#: apps/core/models/company.py:16
msgid "Short Name"
msgstr ""

#: apps/core/models/company.py:21
msgid "Company Slogan"
msgstr ""

#: apps/core/models/company.py:27
msgid "Company Logo"
msgstr ""

#: apps/core/models/company.py:32
msgid "Website URL"
msgstr ""

#: apps/core/models/company.py:36
msgid "Company Email"
msgstr ""

#: apps/core/models/company.py:42 apps/core/models/contact_info.py:29
msgid "Mobile Number"
msgstr ""

#: apps/core/models/company.py:48 apps/core/models/contact_info.py:23
#: apps/identity/models/user.py:27
msgid "Phone Number"
msgstr ""

#: apps/core/models/company.py:56
msgid "State/Province"
msgstr ""

#: apps/core/models/company.py:65
msgid "Street Address"
msgstr ""

#: apps/core/models/company.py:71
msgid "Companies"
msgstr ""

#: apps/core/models/contact_info.py:17
msgid "Contact Name"
msgstr ""

#: apps/core/models/contact_info.py:34 apps/identity/models/user.py:20
msgid "Email Address"
msgstr ""

#: apps/core/models/contact_info.py:41
msgid "Contact Type"
msgstr ""

#: apps/core/models/contact_info.py:45
msgid "Is Default Contact"
msgstr ""

#: apps/core/models/contact_info.py:50
msgid "Contact info"
msgstr ""

#: apps/core/models/contact_info.py:51
msgid "Contact infos"
msgstr ""

#: apps/core/models/country.py:16
msgid "Country Name"
msgstr ""

#: apps/core/models/country.py:22
msgid "Country Code"
msgstr ""

#: apps/core/models/country.py:28
msgid "Native Name"
msgstr ""

#: apps/core/models/country.py:32
msgid "Phone Code"
msgstr ""

#: apps/core/models/country.py:38
msgid "Capital City"
msgstr ""

#: apps/core/models/country.py:44
msgid "Continent Code"
msgstr ""

#: apps/core/models/country.py:50
msgid "Countries"
msgstr ""

#: apps/core/models/element.py:17
msgid "Element Name"
msgstr ""

#: apps/core/models/element.py:23
msgid "Element Code"
msgstr ""

#: apps/core/models/element.py:29
msgid "Element Group"
msgstr ""

#: apps/core/models/element.py:34 apps/payment/models.py:89
msgid "Description"
msgstr ""

#: apps/core/models/element.py:39
msgid "Element"
msgstr ""

#: apps/core/models/element.py:40
msgid "Elements"
msgstr ""

#: apps/core/models/state.py:15
msgid "State Name"
msgstr ""

#: apps/core/models/state.py:21
msgid "State Code"
msgstr ""

#: apps/core/models/state.py:35
msgid "States"
msgstr ""

#: apps/core/models/system_setting.py:17
msgid "The name of the application."
msgstr ""

#: apps/core/models/system_setting.py:18
msgid "Application Name"
msgstr ""

#: apps/core/models/system_setting.py:23
msgid "Version of the application."
msgstr ""

#: apps/core/models/system_setting.py:24
msgid "Application Version"
msgstr ""

#: apps/core/models/system_setting.py:29
msgid "Default Date Format"
msgstr ""

#: apps/core/models/system_setting.py:34
msgid "Default DateTime Format"
msgstr ""

#: apps/core/models/system_setting.py:39
msgid "Time Format"
msgstr ""

#: apps/core/models/system_setting.py:44
msgid "Default currency for transactions."
msgstr ""

#: apps/core/models/system_setting.py:45
msgid "Default Currency"
msgstr ""

#: apps/core/models/system_setting.py:50
msgid "Default language for the system."
msgstr ""

#: apps/core/models/system_setting.py:51
msgid "Default Language"
msgstr ""

#: apps/core/models/system_setting.py:55
msgid "Number of decimal places for numeric values."
msgstr ""

#: apps/core/models/system_setting.py:56
msgid "Decimal Precision"
msgstr ""

#: apps/core/models/system_setting.py:61
msgid "Ascending"
msgstr ""

#: apps/core/models/system_setting.py:61
msgid "Descending"
msgstr ""

#: apps/core/models/system_setting.py:62
msgid "Default Ordering"
msgstr ""

#: apps/core/models/system_setting.py:67
msgid "Default timezone for the application."
msgstr ""

#: apps/core/models/system_setting.py:68
msgid "Default Timezone"
msgstr ""

#: apps/core/models/system_setting.py:72
msgid "Enable or disable maintenance mode."
msgstr ""

#: apps/core/models/system_setting.py:73
msgid "Maintenance Mode Enabled"
msgstr ""

#: apps/core/models/system_setting.py:77
msgid "Message displayed during maintenance."
msgstr ""

#: apps/core/models/system_setting.py:78
msgid "Maintenance Message"
msgstr ""

#: apps/core/models/system_setting.py:82
msgid "Session timeout duration in minutes."
msgstr ""

#: apps/core/models/system_setting.py:83
msgid "Session Timeout (Minutes)"
msgstr ""

#: apps/core/models/system_setting.py:87
msgid "Default number of items per page."
msgstr ""

#: apps/core/models/system_setting.py:88
msgid "Pagination Size"
msgstr ""

#: apps/core/models/system_setting.py:92
msgid "API rate limit (requests per hour)."
msgstr ""

#: apps/core/models/system_setting.py:93
msgid "API Rate Limit"
msgstr ""

#: apps/core/models/system_setting.py:97
msgid "Maximum upload size in bytes."
msgstr ""

#: apps/core/models/system_setting.py:98
msgid "Max Upload Size (Bytes)"
msgstr ""

#: apps/core/models/system_setting.py:102
msgid "Enable or disable caching."
msgstr ""

#: apps/core/models/system_setting.py:103
msgid "Caching Enabled"
msgstr ""

#: apps/core/models/system_setting.py:107
msgid "Default cache expiry time in seconds."
msgstr ""

#: apps/core/models/system_setting.py:108
msgid "Cache Expiry Time (Seconds)"
msgstr ""

#: apps/core/models/system_setting.py:112
msgid "Enable or disable logging."
msgstr ""

#: apps/core/models/system_setting.py:113
msgid "Logging Enabled"
msgstr ""

#: apps/core/models/system_setting.py:119
msgid "Debug"
msgstr ""

#: apps/core/models/system_setting.py:120
msgid "Info"
msgstr ""

#: apps/core/models/system_setting.py:121
msgid "Warning"
msgstr ""

#: apps/core/models/system_setting.py:122 apps/notification/models.py:202
msgid "Error"
msgstr ""

#: apps/core/models/system_setting.py:124
msgid "Log Level"
msgstr ""

#: apps/core/models/system_setting.py:129
msgid "URL for the terms of service."
msgstr ""

#: apps/core/models/system_setting.py:130
msgid "Terms of Service URL"
msgstr ""

#: apps/core/models/system_setting.py:135
msgid "URL for the privacy policy."
msgstr ""

#: apps/core/models/system_setting.py:136
msgid "Privacy Policy URL"
msgstr ""

#: apps/core/models/system_setting.py:142
msgid "Password Format"
msgstr ""

#: apps/core/models/system_setting.py:146
msgid "Allowed Login Attempts"
msgstr ""

#: apps/core/models/system_setting.py:151
#: apps/core/models/system_setting.py:152
msgid "System settings"
msgstr ""

#: apps/core/models/timezone.py:18
msgid "Timezone Name"
msgstr ""

#: apps/core/models/timezone.py:24
msgid "Timezone Code"
msgstr ""

#: apps/core/models/timezone.py:28
msgid "UTC Offset"
msgstr ""

#: apps/core/models/timezone.py:41 apps/user/models/user_preference.py:34
msgid "Timezone"
msgstr ""

#: apps/core/models/timezone.py:42
msgid "Timezones"
msgstr ""

#: apps/file_manager/models/file_upload.py:21
#: apps/identity/models/mfa_setup.py:23 apps/identity/models/user.py:96
#: apps/identity/models/user_session.py:22
#: apps/identity/models/verification_code.py:24
#: apps/identity/models/verification_token.py:26 apps/notification/models.py:44
#: apps/notification/models.py:108 apps/payment/models.py:21
#: apps/payment/models.py:60 apps/user/models/user_preference.py:23
#: apps/user/models/user_role.py:20
#: env/lib/python3.12/site-packages/unfold/contrib/guardian/templates/admin/guardian/model/obj_perms_manage_user.html:43
#: env/lib/python3.12/site-packages/unfold/contrib/guardian/templates/unfold/guardian/group_form.html:33
#: env/lib/python3.12/site-packages/unfold/contrib/guardian/templates/unfold/guardian/user_form.html:15
#: env/lib/python3.12/site-packages/unfold/contrib/guardian/templates/unfold/guardian/user_form.html:33
#: env/lib/python3.12/site-packages/unfold/templates/admin/object_history.html:38
#: env/lib/python3.12/site-packages/unfold/templates/admin/object_history.html:54
msgid "User"
msgstr ""

#: apps/file_manager/models/file_upload.py:23
#: venv/lib/python3.10/site-packages/django/db/models/fields/files.py:244
msgid "File"
msgstr ""

#: apps/file_manager/models/file_upload.py:28
msgid "File Type"
msgstr ""

#: apps/file_manager/models/file_upload.py:31
msgid "Original Name"
msgstr ""

#: apps/file_manager/models/file_upload.py:33
msgid "Size"
msgstr ""

#: apps/file_manager/models/file_upload.py:37
msgid "File upload"
msgstr ""

#: apps/file_manager/models/file_upload.py:38
msgid "File uploads"
msgstr ""

#: apps/identity/admin.py:67
msgid "Personal info"
msgstr ""

#: apps/identity/admin.py:77
msgid "Verification"
msgstr ""

#: apps/identity/admin.py:85
msgid "Security"
msgstr ""

#: apps/identity/admin.py:92
msgid "Access"
msgstr ""

#: apps/identity/admin.py:103
msgid "Important dates"
msgstr ""

#: apps/identity/admin.py:112
msgid "Additional info"
msgstr ""

#: apps/identity/admin.py:120 apps/identity/models/user.py:33
msgid "Profile Picture"
msgstr ""

#: apps/identity/api/v1/serializers/registration_serializer.py:13
msgid "Email is required."
msgstr ""

#: apps/identity/api/v1/serializers/registration_serializer.py:17
msgid "Username is required."
msgstr ""

#: apps/identity/api/v1/serializers/registration_serializer.py:21
msgid "First name is required."
msgstr ""

#: apps/identity/api/v1/serializers/registration_serializer.py:25
msgid "Last name is required."
msgstr ""

#: apps/identity/api/v1/serializers/registration_serializer.py:31
msgid "Password is required."
msgstr ""

#: apps/identity/api/v1/serializers/registration_serializer.py:37
msgid "Please confirm your password."
msgstr ""

#: apps/identity/api/v1/serializers/registration_serializer.py:44
msgid "Invalid language choice."
msgstr ""

#: apps/identity/api/v1/views/auth_view.py:43
#: apps/identity/api/v1/views/auth_view.py:83
#: apps/identity/api/v1/views/auth_view.py:127
#: apps/identity/api/v1/views/auth_view.py:258
#: apps/identity/api/v1/views/auth_view.py:310
#: apps/identity/api/v1/views/auth_view.py:349
msgid "Invalid input. Please check the provided details."
msgstr ""

#: apps/identity/api/v1/views/auth_view.py:55
msgid "Invalid credentials. Please check your email and password."
msgstr ""

#: apps/identity/api/v1/views/auth_view.py:60
#: apps/identity/api/v1/views/social_auth_view.py:42
#: apps/identity/api/v1/views/social_auth_view.py:78
msgid "Login successful."
msgstr ""

#: apps/identity/api/v1/views/auth_view.py:90
msgid "Refresh token is required."
msgstr ""

#: apps/identity/api/v1/views/auth_view.py:101
msgid "Logged out successfully"
msgstr ""

#: apps/identity/api/v1/views/auth_view.py:156
msgid ""
"Registration successful. Please check your email to verify your account."
msgstr ""

#: apps/identity/api/v1/views/auth_view.py:235
msgid "Profile retrieved successfully"
msgstr ""

#: apps/identity/api/v1/views/auth_view.py:273
msgid "Captcha validation failed"
msgstr ""

#: apps/identity/api/v1/views/auth_view.py:282
msgid "Password reset instructions sent to your email"
msgstr ""

#: apps/identity/api/v1/views/auth_view.py:287
msgid "User with this email does not exist"
msgstr ""

#: apps/identity/api/v1/views/auth_view.py:392
msgid "Token refreshed successfully"
msgstr ""

#: apps/identity/apps.py:8
msgid "Identity"
msgstr ""

#: apps/identity/models/mfa_setup.py:30
msgid "MFA Type"
msgstr ""

#: apps/identity/models/mfa_setup.py:32
msgid "Secret"
msgstr ""

#: apps/identity/models/mfa_setup.py:33
msgid "Backup Codes"
msgstr ""

#: apps/identity/models/mfa_setup.py:37
msgid "MFA Setup"
msgstr ""

#: apps/identity/models/mfa_setup.py:38
msgid "MFA Setups"
msgstr ""

#: apps/identity/models/user.py:37
msgid "Email Verified"
msgstr ""

#: apps/identity/models/user.py:42
msgid "Phone Verified"
msgstr ""

#: apps/identity/models/user.py:47
msgid "MFA Enabled"
msgstr ""

#: apps/identity/models/user.py:54 apps/notification/models.py:200
#: apps/payment/models.py:80 apps/payment/models.py:134
#: apps/payment/models.py:170
msgid "Status"
msgstr ""

#: apps/identity/models/user.py:59
msgid "Last Login IP"
msgstr ""

#: apps/identity/models/user.py:64
msgid "Last Login Date"
msgstr ""

#: apps/identity/models/user.py:69
msgid "Date of Birth"
msgstr ""

#: apps/identity/models/user.py:74
msgid "Bio"
msgstr ""

#: apps/identity/models/user.py:79
msgid "Groups"
msgstr ""

#: apps/identity/models/user.py:80
msgid "The groups this user belongs to."
msgstr ""

#: apps/identity/models/user.py:97
msgid "Users"
msgstr ""

#: apps/identity/models/user_session.py:24
#: apps/identity/models/verification_token.py:32
msgid "Token"
msgstr ""

#: apps/identity/models/user_session.py:25
msgid "Refresh Token"
msgstr ""

#: apps/identity/models/user_session.py:26
#: apps/identity/models/verification_code.py:43
#: apps/identity/models/verification_token.py:52
msgid "Expires At"
msgstr ""

#: apps/identity/models/user_session.py:27
msgid "IP Address"
msgstr ""

#: apps/identity/models/user_session.py:28
msgid "User Agent"
msgstr ""

#: apps/identity/models/user_session.py:32
msgid "User Session"
msgstr ""

#: apps/identity/models/user_session.py:33
msgid "User Sessions"
msgstr ""

#: apps/identity/models/verification_code.py:28
#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:59
msgid "Code"
msgstr ""

#: apps/identity/models/verification_code.py:33 apps/payment/models.py:27
msgid "Type"
msgstr ""

#: apps/identity/models/verification_code.py:37
#: apps/identity/models/verification_token.py:49
msgid "Is Used"
msgstr ""

#: apps/identity/models/verification_code.py:41
#: apps/identity/models/verification_token.py:56
msgid "Attempts"
msgstr ""

#: apps/identity/models/verification_code.py:47
msgid "User verification code"
msgstr ""

#: apps/identity/models/verification_code.py:48
msgid "User verification codes"
msgstr ""

#: apps/identity/models/verification_token.py:38
msgid "Token Type"
msgstr ""

#: apps/identity/models/verification_token.py:44
msgid "OTP"
msgstr ""

#: apps/identity/models/verification_token.py:61
msgid "Verification token"
msgstr ""

#: apps/identity/models/verification_token.py:62
msgid "Verification tokens"
msgstr ""

#: apps/miscellaneous/models/site_content.py:16 apps/notification/models.py:124
msgid "Title"
msgstr ""

#: apps/miscellaneous/models/site_content.py:21
msgid "Content Section"
msgstr ""

#: apps/miscellaneous/models/site_content.py:23 apps/notification/models.py:158
msgid "Content"
msgstr ""

#: apps/miscellaneous/models/site_content.py:27
msgid "Site Content"
msgstr ""

#: apps/miscellaneous/models/site_content.py:28
msgid "Site Contents"
msgstr ""

#: apps/notification/models.py:15 apps/notification/models.py:81
#: apps/notification/models.py:115
msgid "Notification Type"
msgstr ""

#: apps/notification/models.py:21
msgid "Notification Code"
msgstr ""

#: apps/notification/models.py:24 apps/notification/models.py:132
msgid "Created At"
msgstr ""

#: apps/notification/models.py:29
msgid "Notification type"
msgstr ""

#: apps/notification/models.py:30
msgid "Notification types"
msgstr ""

#: apps/notification/models.py:52
msgid "Notification Types"
msgstr ""

#: apps/notification/models.py:55
msgid "Receive Email Notifications"
msgstr ""

#: apps/notification/models.py:58
msgid "Receive Push Notifications"
msgstr ""

#: apps/notification/models.py:61
msgid "Receive SMS Notifications"
msgstr ""

#: apps/notification/models.py:73
msgid "Notification preference"
msgstr ""

#: apps/notification/models.py:74
msgid "Notification preferences"
msgstr ""

#: apps/notification/models.py:88
msgid "Email Subject"
msgstr ""

#: apps/notification/models.py:89
msgid "Email Body"
msgstr ""

#: apps/notification/models.py:100
msgid "Notification template"
msgstr ""

#: apps/notification/models.py:101
msgid "Notification templates"
msgstr ""

#: apps/notification/models.py:127
msgid "Template"
msgstr ""

#: apps/notification/models.py:130 apps/notification/models.py:203
#: apps/payment/models.py:172
msgid "Message"
msgstr ""

#: apps/notification/models.py:131
msgid "Read"
msgstr ""

#: apps/notification/models.py:138 apps/notification/models.py:189
msgid "Notification"
msgstr ""

#: apps/notification/models.py:139
msgid "Notifications"
msgstr ""

#: apps/notification/models.py:152
msgid "Template Name"
msgstr ""

#: apps/notification/models.py:155
msgid "Template Code"
msgstr ""

#: apps/notification/models.py:157
msgid "Subject"
msgstr ""

#: apps/notification/models.py:159
msgid "Active"
msgstr ""

#: apps/notification/models.py:170
msgid "Email template"
msgstr ""

#: apps/notification/models.py:171
msgid "Email templates"
msgstr ""

#: apps/notification/models.py:204 apps/payment/models.py:38
#: apps/payment/models.py:97 apps/payment/models.py:145
msgid "Meta Data"
msgstr ""

#: apps/notification/models.py:216
msgid "Notification log"
msgstr ""

#: apps/notification/models.py:217
msgid "Notification logs"
msgstr ""

#: apps/payment/apps.py:7 apps/payment/models.py:115 apps/payment/models.py:124
#: apps/payment/models.py:166
msgid "Payment"
msgstr ""

#: apps/payment/models.py:29
msgid "Provider"
msgstr ""

#: apps/payment/models.py:30
msgid "Token ID"
msgstr ""

#: apps/payment/models.py:32
msgid "Last 4 digits"
msgstr ""

#: apps/payment/models.py:35
msgid "Expiry Date"
msgstr ""

#: apps/payment/models.py:37
msgid "Default"
msgstr ""

#: apps/payment/models.py:50
msgid "Payment method"
msgstr ""

#: apps/payment/models.py:51
msgid "Payment methods"
msgstr ""

#: apps/payment/models.py:67
msgid "Payment Method"
msgstr ""

#: apps/payment/models.py:70 apps/payment/models.py:127
msgid "Amount"
msgstr ""

#: apps/payment/models.py:73
msgid "Currency"
msgstr ""

#: apps/payment/models.py:86
msgid "Transaction ID"
msgstr ""

#: apps/payment/models.py:92 apps/payment/models.py:140
msgid "Provider Response"
msgstr ""

#: apps/payment/models.py:95 apps/payment/models.py:143
msgid "Error Message"
msgstr ""

#: apps/payment/models.py:102
msgid "Refunded Amount"
msgstr ""

#: apps/payment/models.py:116
msgid "Payments"
msgstr ""

#: apps/payment/models.py:129
msgid "Reason"
msgstr ""

#: apps/payment/models.py:137
msgid "Refund ID"
msgstr ""

#: apps/payment/models.py:156
msgid "Refund"
msgstr ""

#: apps/payment/models.py:157
msgid "Refunds"
msgstr ""

#: apps/payment/models.py:168
#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/unfold/guardian/group_form.html:25
#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/unfold/guardian/group_form.html:49
#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/unfold/guardian/user_form.html:25
#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/unfold/guardian/user_form.html:49
#: venv/lib/python3.10/site-packages/unfold/templates/admin/object_history.html:42
#: venv/lib/python3.10/site-packages/unfold/templates/admin/object_history.html:58
msgid "Action"
msgstr ""

#: apps/payment/models.py:173
msgid "Metadata"
msgstr ""

#: apps/payment/models.py:185
msgid "Payment log"
msgstr ""

#: apps/payment/models.py:186
msgid "Payment logs"
msgstr ""

#: apps/product/apps.py:9 apps/product/models/product.py:50
msgid "Product"
msgstr ""

#: apps/product/models/category.py:18
msgid "Category Name"
msgstr ""

#: apps/product/models/category.py:23
msgid "Category Description"
msgstr ""

#: apps/product/models/category.py:28 apps/product/models/product.py:20
msgid "Category"
msgstr ""

#: apps/product/models/category.py:29
msgid "Categories"
msgstr ""

#: apps/product/models/product.py:25
msgid "Product Name"
msgstr ""

#: apps/product/models/product.py:28
msgid "Product Description"
msgstr ""

#: apps/product/models/product.py:34
msgid "Product Price"
msgstr ""

#: apps/product/models/product.py:39
msgid "Is Available"
msgstr ""

#: apps/product/models/product.py:45
msgid "Product Image"
msgstr ""

#: apps/product/models/product.py:51
msgid "Products"
msgstr ""

#: apps/user/models/role.py:18
msgid "Role Name"
msgstr ""

#: apps/user/models/role.py:23
msgid "Role Description"
msgstr ""

#: apps/user/models/role.py:28
msgid "User role"
msgstr ""

#: apps/user/models/role.py:29
msgid "User roles"
msgstr ""

#: apps/user/models/user_preference.py:29
msgid "Language"
msgstr ""

#: apps/user/models/user_preference.py:39
msgid "User preference"
msgstr ""

#: apps/user/models/user_preference.py:40
msgid "User preferences"
msgstr ""

#: apps/user/models/user_role.py:26
msgid "Role"
msgstr ""

#: apps/user/models/user_role.py:32
msgid "User role mapping"
msgstr ""

#: apps/user/models/user_role.py:33
msgid "User role mappings"
msgstr ""

#: common/models.py:21
msgid "Created by"
msgstr ""

#: common/models.py:29
msgid "Created at"
msgstr ""

#: common/models.py:37
msgid "Updated by"
msgstr ""

#: common/models.py:45
msgid "Updated at"
msgstr ""

#: common/models.py:53
msgid "Deleted by"
msgstr ""

#: common/models.py:61
msgid "Deleted at"
msgstr ""

#: common/models.py:68
msgid "Is deleted"
msgstr ""

#: common/models.py:73
#: venv/lib/python3.10/site-packages/fcm_django/models.py:27
msgid "Is active"
msgstr ""

#: config/settings/base.py:279
msgid "English"
msgstr ""

#: config/settings/base.py:280
msgid "Spanish"
msgstr ""

#: config/settings/base.py:281
msgid "French"
msgstr ""

#: config/settings/base.py:282
msgid "Turkish"
msgstr ""

#: config/settings/base.py:307
msgid "Bengali"
msgstr ""

#: config/settings/extend/unfold_setting.py:44
msgid "Development"
msgstr ""

#: config/settings/extend/unfold_setting.py:46
msgid "Staging"
msgstr ""

#: config/settings/extend/unfold_setting.py:48
msgid "Production"
msgstr ""

#: config/settings/extend/unfold_setting.py:44
msgid "Development"
msgstr ""

#: config/settings/extend/unfold_setting.py:46
msgid "Staging"
msgstr ""

#: config/settings/extend/unfold_setting.py:48
msgid "Production"
msgstr ""

#: venv/lib/python3.10/site-packages/_pytest/config/argparsing.py:474
#, python-format
msgid "ambiguous option: %(option)s could match %(matches)s"
msgstr ""

#: venv/lib/python3.10/site-packages/click/_termui_impl.py:556
#, python-brace-format
msgid "{editor}: Editing failed"
msgstr ""

#: venv/lib/python3.10/site-packages/click/_termui_impl.py:560
#, python-brace-format
msgid "{editor}: Editing failed: {e}"
msgstr ""

#: venv/lib/python3.10/site-packages/click/core.py:1124
msgid "Aborted!"
msgstr ""

#: venv/lib/python3.10/site-packages/click/core.py:1349
#: venv/lib/python3.10/site-packages/click/core.py:1379
#, python-brace-format
msgid "(Deprecated) {text}"
msgstr ""

#: venv/lib/python3.10/site-packages/click/core.py:1396
msgid "Options"
msgstr ""

#: venv/lib/python3.10/site-packages/click/core.py:1422
#, python-brace-format
msgid "Got unexpected extra argument ({args})"
msgid_plural "Got unexpected extra arguments ({args})"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/click/core.py:1438
msgid "DeprecationWarning: The command {name!r} is deprecated."
msgstr ""

#: venv/lib/python3.10/site-packages/click/core.py:1645
msgid "Commands"
msgstr ""

#: venv/lib/python3.10/site-packages/click/core.py:1677
msgid "Missing command."
msgstr ""

#: venv/lib/python3.10/site-packages/click/core.py:1755
msgid "No such command {name!r}."
msgstr ""

#: venv/lib/python3.10/site-packages/click/core.py:2313
msgid "Value must be an iterable."
msgstr ""

#: venv/lib/python3.10/site-packages/click/core.py:2334
#, python-brace-format
msgid "Takes {nargs} values but 1 was given."
msgid_plural "Takes {nargs} values but {len} were given."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/click/core.py:2783
#, python-brace-format
msgid "env var: {var}"
msgstr ""

#: venv/lib/python3.10/site-packages/click/core.py:2813
msgid "(dynamic)"
msgstr ""

#: venv/lib/python3.10/site-packages/click/core.py:2828
#, python-brace-format
msgid "default: {default}"
msgstr ""

#: venv/lib/python3.10/site-packages/click/core.py:2841
msgid "required"
msgstr ""

#: venv/lib/python3.10/site-packages/click/decorators.py:457
#, python-format
msgid "%(prog)s, version %(version)s"
msgstr ""

#: venv/lib/python3.10/site-packages/click/decorators.py:520
msgid "Show the version and exit."
msgstr ""

#: venv/lib/python3.10/site-packages/click/decorators.py:541
msgid "Show this message and exit."
msgstr ""

#: venv/lib/python3.10/site-packages/click/exceptions.py:49
#: venv/lib/python3.10/site-packages/click/exceptions.py:88
#, python-brace-format
msgid "Error: {message}"
msgstr ""

#: venv/lib/python3.10/site-packages/click/exceptions.py:80
#, python-brace-format
msgid "Try '{command} {option}' for help."
msgstr ""

#: venv/lib/python3.10/site-packages/click/exceptions.py:129
#, python-brace-format
msgid "Invalid value: {message}"
msgstr ""

#: venv/lib/python3.10/site-packages/click/exceptions.py:131
#, python-brace-format
msgid "Invalid value for {param_hint}: {message}"
msgstr ""

#: venv/lib/python3.10/site-packages/click/exceptions.py:187
msgid "Missing argument"
msgstr ""

#: venv/lib/python3.10/site-packages/click/exceptions.py:189
msgid "Missing option"
msgstr ""

#: venv/lib/python3.10/site-packages/click/exceptions.py:191
msgid "Missing parameter"
msgstr ""

#: venv/lib/python3.10/site-packages/click/exceptions.py:193
#, python-brace-format
msgid "Missing {param_type}"
msgstr ""

#: venv/lib/python3.10/site-packages/click/exceptions.py:200
#, python-brace-format
msgid "Missing parameter: {param_name}"
msgstr ""

#: venv/lib/python3.10/site-packages/click/exceptions.py:220
#, python-brace-format
msgid "No such option: {name}"
msgstr ""

#: venv/lib/python3.10/site-packages/click/exceptions.py:232
#, python-brace-format
msgid "Did you mean {possibility}?"
msgid_plural "(Possible options: {possibilities})"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/click/exceptions.py:270
msgid "unknown error"
msgstr ""

#: venv/lib/python3.10/site-packages/click/exceptions.py:277
msgid "Could not open file {filename!r}: {message}"
msgstr ""

#: venv/lib/python3.10/site-packages/click/parser.py:233
msgid "Argument {name!r} takes {nargs} values."
msgstr ""

#: venv/lib/python3.10/site-packages/click/parser.py:415
msgid "Option {name!r} does not take a value."
msgstr ""

#: venv/lib/python3.10/site-packages/click/parser.py:476
msgid "Option {name!r} requires an argument."
msgid_plural "Option {name!r} requires {nargs} arguments."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/click/shell_completion.py:326
msgid "Shell completion is not supported for Bash versions older than 4.4."
msgstr ""

#: venv/lib/python3.10/site-packages/click/shell_completion.py:333
msgid "Couldn't detect Bash version, shell completion is not supported."
msgstr ""

#: venv/lib/python3.10/site-packages/click/termui.py:158
msgid "Repeat for confirmation"
msgstr ""

#: venv/lib/python3.10/site-packages/click/termui.py:174
msgid "Error: The value you entered was invalid."
msgstr ""

#: venv/lib/python3.10/site-packages/click/termui.py:176
#, python-brace-format
msgid "Error: {e.message}"
msgstr ""

#: venv/lib/python3.10/site-packages/click/termui.py:187
msgid "Error: The two entered values do not match."
msgstr ""

#: venv/lib/python3.10/site-packages/click/termui.py:243
msgid "Error: invalid input"
msgstr ""

#: venv/lib/python3.10/site-packages/click/termui.py:773
msgid "Press any key to continue..."
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:267
#, python-brace-format
msgid ""
"Choose from:\n"
"\t{choices}"
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:299
msgid "{value!r} is not {choice}."
msgid_plural "{value!r} is not one of {choices}."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/click/types.py:393
msgid "{value!r} does not match the format {format}."
msgid_plural "{value!r} does not match the formats {formats}."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/click/types.py:415
msgid "{value!r} is not a valid {number_type}."
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:471
#, python-brace-format
msgid "{value} is not in the range {range}."
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:612
msgid "{value!r} is not a valid boolean."
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:636
msgid "{value!r} is not a valid UUID."
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:826
msgid "file"
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:828
msgid "directory"
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:830
msgid "path"
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:881
msgid "{name} {filename!r} does not exist."
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:890
msgid "{name} {filename!r} is a file."
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:898
msgid "{name} {filename!r} is a directory."
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:907
msgid "{name} {filename!r} is not readable."
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:916
msgid "{name} {filename!r} is not writable."
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:925
msgid "{name} {filename!r} is not executable."
msgstr ""

#: venv/lib/python3.10/site-packages/click/types.py:992
#, python-brace-format
msgid "{len_type} values are required, but {len_value} was given."
msgid_plural "{len_type} values are required, but {len_value} were given."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/contrib/messages/apps.py:16
msgid "Messages"
msgstr ""

#: venv/lib/python3.10/site-packages/django/contrib/sitemaps/apps.py:8
msgid "Site Maps"
msgstr ""

#: venv/lib/python3.10/site-packages/django/contrib/staticfiles/apps.py:9
msgid "Static Files"
msgstr ""

#: venv/lib/python3.10/site-packages/django/contrib/syndication/apps.py:7
msgid "Syndication"
msgstr ""

#. Translators: String used to replace omitted page numbers in elided page
#. range generated by paginators, e.g. [1, 2, '…', 5, 6, 7, '…', 9, 10].
#: venv/lib/python3.10/site-packages/django/core/paginator.py:30
msgid "…"
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/paginator.py:32
msgid "That page number is not an integer"
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/paginator.py:33
msgid "That page number is less than 1"
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/paginator.py:34
msgid "That page contains no results"
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:22
msgid "Enter a valid value."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:70
msgid "Enter a valid domain name."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:155
#: venv/lib/python3.10/site-packages/django/forms/fields.py:768
msgid "Enter a valid URL."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:216
msgid "Enter a valid integer."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:227
msgid "Enter a valid email address."
msgstr ""

#. Translators: "letters" means latin letters: a-z and A-Z.
#: venv/lib/python3.10/site-packages/django/core/validators.py:310
msgid ""
"Enter a valid “slug” consisting of letters, numbers, underscores or hyphens."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:318
msgid ""
"Enter a valid “slug” consisting of Unicode letters, numbers, underscores, or "
"hyphens."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:330
#: venv/lib/python3.10/site-packages/django/core/validators.py:339
#: venv/lib/python3.10/site-packages/django/core/validators.py:353
#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2215
#, python-format
msgid "Enter a valid %(protocol)s address."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:332
msgid "IPv4"
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:341
#: venv/lib/python3.10/site-packages/django/utils/ipv6.py:30
msgid "IPv6"
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:355
msgid "IPv4 or IPv6"
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:396
msgid "Enter only digits separated by commas."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:402
#, python-format
msgid "Ensure this value is %(limit_value)s (it is %(show_value)s)."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:437
#, python-format
msgid "Ensure this value is less than or equal to %(limit_value)s."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:446
#, python-format
msgid "Ensure this value is greater than or equal to %(limit_value)s."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:455
#, python-format
msgid "Ensure this value is a multiple of step size %(limit_value)s."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:462
#, python-format
msgid ""
"Ensure this value is a multiple of step size %(limit_value)s, starting from "
"%(offset)s, e.g. %(offset)s, %(valid_value1)s, %(valid_value2)s, and so on."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:494
#, python-format
msgid ""
"Ensure this value has at least %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at least %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:512
#, python-format
msgid ""
"Ensure this value has at most %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at most %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:535
#: venv/lib/python3.10/site-packages/django/forms/fields.py:359
#: venv/lib/python3.10/site-packages/django/forms/fields.py:398
msgid "Enter a number."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:537
#, python-format
msgid "Ensure that there are no more than %(max)s digit in total."
msgid_plural "Ensure that there are no more than %(max)s digits in total."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:542
#, python-format
msgid "Ensure that there are no more than %(max)s decimal place."
msgid_plural "Ensure that there are no more than %(max)s decimal places."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:547
#, python-format
msgid ""
"Ensure that there are no more than %(max)s digit before the decimal point."
msgid_plural ""
"Ensure that there are no more than %(max)s digits before the decimal point."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:618
#, python-format
msgid ""
"File extension “%(extension)s” is not allowed. Allowed extensions are: "
"%(allowed_extensions)s."
msgstr ""

#: venv/lib/python3.10/site-packages/django/core/validators.py:680
msgid "Null characters are not allowed."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/base.py:1572
#: venv/lib/python3.10/site-packages/django/forms/models.py:908
#: venv/lib/python3.10/site-packages/unfold/contrib/inlines/admin.py:103
msgid "and"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/base.py:1574
#, python-format
msgid "%(model_name)s with this %(field_labels)s already exists."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/constraints.py:22
#, python-format
msgid "Constraint “%(name)s” is violated."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:134
#, python-format
msgid "Value %(value)r is not a valid choice."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:135
msgid "This field cannot be null."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:136
msgid "This field cannot be blank."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:137
#, python-format
msgid "%(model_name)s with this %(field_label)s already exists."
msgstr ""

#. Translators: The 'lookup_type' is one of 'date', 'year' or
#. 'month'. Eg: "Title must be unique for pub_date year"
#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:141
#, python-format
msgid ""
"%(field_label)s must be unique for %(date_field_label)s %(lookup_type)s."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:180
#, python-format
msgid "Field of type: %(field_type)s"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1156
#, python-format
msgid "“%(value)s” value must be either True or False."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1157
#, python-format
msgid "“%(value)s” value must be either True, False, or None."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1159
msgid "Boolean (Either True or False)"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1209
#, python-format
msgid "String (up to %(max_length)s)"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1211
msgid "String (unlimited)"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1320
msgid "Comma-separated integers"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1421
#, python-format
msgid ""
"“%(value)s” value has an invalid date format. It must be in YYYY-MM-DD "
"format."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1425
#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1560
#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD) but it is an invalid "
"date."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1429
msgid "Date (without time)"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1556
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in YYYY-MM-DD HH:MM[:ss[."
"uuuuuu]][TZ] format."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1564
#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD HH:MM[:ss[.uuuuuu]]"
"[TZ]) but it is an invalid date/time."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1569
msgid "Date (with time)"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1696
#, python-format
msgid "“%(value)s” value must be a decimal number."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1698
msgid "Decimal number"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1859
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in [DD] [[HH:]MM:]ss[."
"uuuuuu] format."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1863
msgid "Duration"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1915
msgid "Email address"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:1940
msgid "File path"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2018
#, python-format
msgid "“%(value)s” value must be a float."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2020
msgid "Floating point number"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2060
#, python-format
msgid "“%(value)s” value must be an integer."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2062
msgid "Integer"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2158
msgid "Big (8 byte) integer"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2175
msgid "Small integer"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2183
msgid "IPv4 address"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2214
msgid "IP address"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2305
#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2306
#, python-format
msgid "“%(value)s” value must be either None, True or False."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2308
msgid "Boolean (Either True, False or None)"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2359
msgid "Positive big integer"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2374
msgid "Positive integer"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2389
msgid "Positive small integer"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2405
#, python-format
msgid "Slug (up to %(max_length)s)"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2441
msgid "Text"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2521
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in HH:MM[:ss[.uuuuuu]] "
"format."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2525
#, python-format
msgid ""
"“%(value)s” value has the correct format (HH:MM[:ss[.uuuuuu]]) but it is an "
"invalid time."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2529
#: venv/lib/python3.10/site-packages/unfold/contrib/filters/forms.py:174
#: venv/lib/python3.10/site-packages/unfold/contrib/filters/forms.py:190
#: venv/lib/python3.10/site-packages/unfold/widgets.py:467
msgid "Time"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2637
msgid "URL"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2661
msgid "Raw binary data"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2726
#, python-format
msgid "“%(value)s” is not a valid UUID."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/__init__.py:2728
msgid "Universally unique identifier"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/files.py:420
msgid "Image"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/json.py:24
msgid "A JSON object"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/json.py:26
msgid "Value must be valid JSON."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/related.py:932
#, python-format
msgid "%(model)s instance with %(field)s %(value)r does not exist."
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/related.py:934
msgid "Foreign Key (type determined by related field)"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/related.py:1228
msgid "One-to-one relationship"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/related.py:1285
#, python-format
msgid "%(from)s-%(to)s relationship"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/related.py:1287
#, python-format
msgid "%(from)s-%(to)s relationships"
msgstr ""

#: venv/lib/python3.10/site-packages/django/db/models/fields/related.py:1335
msgid "Many-to-many relationship"
msgstr ""

#. Translators: If found as last label character, these punctuation
#. characters will prevent the default label_suffix to be appended to the label
#: venv/lib/python3.10/site-packages/django/forms/boundfield.py:185
msgid ":?.!"
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:95
msgid "This field is required."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:308
msgid "Enter a whole number."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:479
#: venv/lib/python3.10/site-packages/django/forms/fields.py:1260
msgid "Enter a valid date."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:502
#: venv/lib/python3.10/site-packages/django/forms/fields.py:1261
msgid "Enter a valid time."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:529
msgid "Enter a valid date/time."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:563
msgid "Enter a valid duration."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:564
#, python-brace-format
msgid "The number of days must be between {min_days} and {max_days}."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:633
msgid "No file was submitted. Check the encoding type on the form."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:634
msgid "No file was submitted."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:635
msgid "The submitted file is empty."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:637
#, python-format
msgid "Ensure this filename has at most %(max)d character (it has %(length)d)."
msgid_plural ""
"Ensure this filename has at most %(max)d characters (it has %(length)d)."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:642
msgid "Please either submit a file or check the clear checkbox, not both."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:710
msgid ""
"Upload a valid image. The file you uploaded was either not an image or a "
"corrupted image."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:882
#: venv/lib/python3.10/site-packages/django/forms/fields.py:968
#: venv/lib/python3.10/site-packages/django/forms/models.py:1592
#, python-format
msgid "Select a valid choice. %(value)s is not one of the available choices."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:970
#: venv/lib/python3.10/site-packages/django/forms/fields.py:1089
#: venv/lib/python3.10/site-packages/django/forms/models.py:1590
msgid "Enter a list of values."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:1090
msgid "Enter a complete value."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:1329
msgid "Enter a valid UUID."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/fields.py:1359
msgid "Enter a valid JSON."
msgstr ""

#. Translators: This is the default suffix added to form field labels
#: venv/lib/python3.10/site-packages/django/forms/forms.py:94
msgid ":"
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/forms.py:230
#, python-format
msgid "(Hidden field %(name)s) %(error)s"
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/formsets.py:61
#, python-format
msgid ""
"ManagementForm data is missing or has been tampered with. Missing fields: "
"%(field_names)s. You may need to file a bug report if the issue persists."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/formsets.py:65
#, python-format
msgid "Please submit at most %(num)d form."
msgid_plural "Please submit at most %(num)d forms."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/forms/formsets.py:70
#, python-format
msgid "Please submit at least %(num)d form."
msgid_plural "Please submit at least %(num)d forms."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/forms/formsets.py:484
#: venv/lib/python3.10/site-packages/django/forms/formsets.py:491
msgid "Order"
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/formsets.py:499
#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_preview.html:26
#: venv/lib/python3.10/site-packages/unfold/templates/admin/submit_line.html:55
msgid "Delete"
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/models.py:901
#, python-format
msgid "Please correct the duplicate data for %(field)s."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/models.py:906
#, python-format
msgid "Please correct the duplicate data for %(field)s, which must be unique."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/models.py:913
#, python-format
msgid ""
"Please correct the duplicate data for %(field_name)s which must be unique "
"for the %(lookup)s in %(date_field)s."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/models.py:922
msgid "Please correct the duplicate values below."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/models.py:1359
msgid "The inline value did not match the parent instance."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/models.py:1450
msgid "Select a valid choice. That choice is not one of the available choices."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/models.py:1594
#, python-format
msgid "“%(pk)s” is not a valid value."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/utils.py:227
#, python-format
msgid ""
"%(datetime)s couldn’t be interpreted in time zone %(current_timezone)s; it "
"may be ambiguous or it may not exist."
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/widgets.py:461
msgid "Clear"
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/widgets.py:462
msgid "Currently"
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/widgets.py:463
#: venv/lib/python3.10/site-packages/unfold/templates/admin/edit_inline/stacked.html:57
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/app_list_default.html:42
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/edit_inline/tabular_title.html:23
msgid "Change"
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/widgets.py:800
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/boolean.html:8
msgid "Unknown"
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/widgets.py:801
msgid "Yes"
msgstr ""

#: venv/lib/python3.10/site-packages/django/forms/widgets.py:802
msgid "No"
msgstr ""

#. Translators: Please do not add spaces around commas.
#: venv/lib/python3.10/site-packages/django/template/defaultfilters.py:873
msgid "yes,no,maybe"
msgstr ""

#: venv/lib/python3.10/site-packages/django/template/defaultfilters.py:903
#: venv/lib/python3.10/site-packages/django/template/defaultfilters.py:920
#, python-format
msgid "%(size)d byte"
msgid_plural "%(size)d bytes"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/template/defaultfilters.py:922
#, python-format
msgid "%s KB"
msgstr ""

#: venv/lib/python3.10/site-packages/django/template/defaultfilters.py:924
#, python-format
msgid "%s MB"
msgstr ""

#: venv/lib/python3.10/site-packages/django/template/defaultfilters.py:926
#, python-format
msgid "%s GB"
msgstr ""

#: venv/lib/python3.10/site-packages/django/template/defaultfilters.py:928
#, python-format
msgid "%s TB"
msgstr ""

#: venv/lib/python3.10/site-packages/django/template/defaultfilters.py:930
#, python-format
msgid "%s PB"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dateformat.py:74
msgid "p.m."
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dateformat.py:75
msgid "a.m."
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dateformat.py:80
msgid "PM"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dateformat.py:81
msgid "AM"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dateformat.py:153
msgid "midnight"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dateformat.py:155
msgid "noon"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:7
msgid "Monday"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:8
msgid "Tuesday"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:9
msgid "Wednesday"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:10
msgid "Thursday"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:11
msgid "Friday"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:12
msgid "Saturday"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:13
msgid "Sunday"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:16
msgid "Mon"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:17
msgid "Tue"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:18
msgid "Wed"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:19
msgid "Thu"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:20
msgid "Fri"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:21
msgid "Sat"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:22
msgid "Sun"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:25
msgid "January"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:26
msgid "February"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:27
msgid "March"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:28
msgid "April"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:29
msgid "May"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:30
msgid "June"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:31
msgid "July"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:32
msgid "August"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:33
msgid "September"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:34
msgid "October"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:35
msgid "November"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:36
msgid "December"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:39
msgid "jan"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:40
msgid "feb"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:41
msgid "mar"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:42
msgid "apr"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:43
msgid "may"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:44
msgid "jun"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:45
msgid "jul"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:46
msgid "aug"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:47
msgid "sep"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:48
msgid "oct"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:49
msgid "nov"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:50
msgid "dec"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:53
msgctxt "abbrev. month"
msgid "Jan."
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:54
msgctxt "abbrev. month"
msgid "Feb."
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:55
msgctxt "abbrev. month"
msgid "March"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:56
msgctxt "abbrev. month"
msgid "April"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:57
msgctxt "abbrev. month"
msgid "May"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:58
msgctxt "abbrev. month"
msgid "June"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:59
msgctxt "abbrev. month"
msgid "July"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:60
msgctxt "abbrev. month"
msgid "Aug."
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:61
msgctxt "abbrev. month"
msgid "Sept."
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:62
msgctxt "abbrev. month"
msgid "Oct."
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:63
msgctxt "abbrev. month"
msgid "Nov."
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:64
msgctxt "abbrev. month"
msgid "Dec."
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:67
msgctxt "alt. month"
msgid "January"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:68
msgctxt "alt. month"
msgid "February"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:69
msgctxt "alt. month"
msgid "March"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:70
msgctxt "alt. month"
msgid "April"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:71
msgctxt "alt. month"
msgid "May"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:72
msgctxt "alt. month"
msgid "June"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:73
msgctxt "alt. month"
msgid "July"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:74
msgctxt "alt. month"
msgid "August"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:75
msgctxt "alt. month"
msgid "September"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:76
msgctxt "alt. month"
msgid "October"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:77
msgctxt "alt. month"
msgid "November"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/dates.py:78
msgctxt "alt. month"
msgid "December"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/ipv6.py:8
msgid "This is not a valid IPv6 address."
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/text.py:75
#, python-format
msgctxt "String to return when truncating text"
msgid "%(truncated_text)s…"
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/text.py:286
msgid "or"
msgstr ""

#. Translators: This string is used as a separator between list elements
#: venv/lib/python3.10/site-packages/django/utils/text.py:305
#: venv/lib/python3.10/site-packages/django/utils/timesince.py:135
msgid ", "
msgstr ""

#: venv/lib/python3.10/site-packages/django/utils/timesince.py:8
#, python-format
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/utils/timesince.py:9
#, python-format
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/utils/timesince.py:10
#, python-format
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/utils/timesince.py:11
#, python-format
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/utils/timesince.py:12
#, python-format
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/utils/timesince.py:13
#, python-format
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/django/views/csrf.py:29
msgid "Forbidden"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/csrf.py:30
msgid "CSRF verification failed. Request aborted."
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/csrf.py:34
msgid ""
"You are seeing this message because this HTTPS site requires a “Referer "
"header” to be sent by your web browser, but none was sent. This header is "
"required for security reasons, to ensure that your browser is not being "
"hijacked by third parties."
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/csrf.py:40
msgid ""
"If you have configured your browser to disable “Referer” headers, please re-"
"enable them, at least for this site, or for HTTPS connections, or for “same-"
"origin” requests."
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/csrf.py:45
msgid ""
"If you are using the <meta name=\"referrer\" content=\"no-referrer\"> tag or "
"including the “Referrer-Policy: no-referrer” header, please remove them. The "
"CSRF protection requires the “Referer” header to do strict referer checking. "
"If you’re concerned about privacy, use alternatives like <a "
"rel=\"noreferrer\" …> for links to third-party sites."
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/csrf.py:54
msgid ""
"You are seeing this message because this site requires a CSRF cookie when "
"submitting forms. This cookie is required for security reasons, to ensure "
"that your browser is not being hijacked by third parties."
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/csrf.py:60
msgid ""
"If you have configured your browser to disable cookies, please re-enable "
"them, at least for this site, or for “same-origin” requests."
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/csrf.py:66
msgid "More information is available with DEBUG=True."
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/generic/dates.py:44
msgid "No year specified"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/generic/dates.py:64
#: venv/lib/python3.10/site-packages/django/views/generic/dates.py:115
#: venv/lib/python3.10/site-packages/django/views/generic/dates.py:214
msgid "Date out of range"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/generic/dates.py:94
msgid "No month specified"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/generic/dates.py:147
msgid "No day specified"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/generic/dates.py:194
msgid "No week specified"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/generic/dates.py:349
#: venv/lib/python3.10/site-packages/django/views/generic/dates.py:380
#, python-format
msgid "No %(verbose_name_plural)s available"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/generic/dates.py:652
#, python-format
msgid ""
"Future %(verbose_name_plural)s not available because %(class_name)s."
"allow_future is False."
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/generic/dates.py:692
#, python-format
msgid "Invalid date string “%(datestr)s” given format “%(format)s”"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/generic/detail.py:56
#, python-format
msgid "No %(verbose_name)s found matching the query"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/generic/list.py:70
msgid "Page is not “last”, nor can it be converted to an int."
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/generic/list.py:77
#, python-format
msgid "Invalid page (%(page_number)s): %(message)s"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/generic/list.py:169
#, python-format
msgid "Empty list and “%(class_name)s.allow_empty” is False."
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/static.py:49
msgid "Directory indexes are not allowed here."
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/static.py:51
#, python-format
msgid "“%(path)s” does not exist"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/static.py:68
#: venv/lib/python3.10/site-packages/django/views/templates/directory_index.html:8
#: venv/lib/python3.10/site-packages/django/views/templates/directory_index.html:11
#, python-format
msgid "Index of %(directory)s"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/templates/default_urlconf.html:7
#: venv/lib/python3.10/site-packages/django/views/templates/default_urlconf.html:204
msgid "The install worked successfully! Congratulations!"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/templates/default_urlconf.html:206
#, python-format
msgid ""
"View <a href=\"https://docs.djangoproject.com/en/%(version)s/releases/\" "
"target=\"_blank\" rel=\"noopener\">release notes</a> for Django %(version)s"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/templates/default_urlconf.html:208
#, python-format
msgid ""
"You are seeing this page because <a href=\"https://docs.djangoproject.com/en/"
"%(version)s/ref/settings/#debug\" target=\"_blank\" "
"rel=\"noopener\">DEBUG=True</a> is in your settings file and you have not "
"configured any URLs."
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/templates/default_urlconf.html:217
msgid "Django Documentation"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/templates/default_urlconf.html:218
msgid "Topics, references, &amp; how-to’s"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/templates/default_urlconf.html:226
msgid "Tutorial: A Polling App"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/templates/default_urlconf.html:227
msgid "Get started with Django"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/templates/default_urlconf.html:235
msgid "Django Community"
msgstr ""

#: venv/lib/python3.10/site-packages/django/views/templates/default_urlconf.html:236
msgid "Connect, get help, or contribute"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/admin.py:73
#, python-format
msgid ""
"A device failed to un/subscribe to topic. %(count)d device was marked as "
"inactive."
msgid_plural ""
"Some devices failed to un/subscribe to topic. %(count)d devices were marked "
"as inactive."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/fcm_django/admin.py:81
#, python-format
msgid "A message failed to send. %(count)d device was marked as inactive."
msgid_plural ""
"Some messages failed to send. %(count)d devices were marked as inactive."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/fcm_django/admin.py:107
#, python-format
msgid "%(response)s (Registration ID/Tokens: %(reg_id)s)"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/admin.py:169
msgid "Send test notification"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/admin.py:174
msgid "Send test notification in bulk"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/admin.py:219
msgid "Subscribe to test topic"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/admin.py:224
msgid "Subscribe to test topic in bulk"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/admin.py:229
msgid "Unsubscribe to test topic"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/admin.py:234
msgid "Unsubscribe to test topic in bulk"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/admin.py:249
msgid "Send message test topic"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/admin.py:254
msgid "Enable selected devices"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/admin.py:259
msgid "Disable selected devices"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/fields.py:50
msgid "Enter a valid hexadecimal number"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/models.py:24
msgid "Name"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/models.py:29
msgid "Inactive devices will not be sent notifications"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/models.py:36
msgid "fcmdevice"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/models.py:39
msgid "Creation date"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/models.py:272
msgid "Device ID"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/models.py:276
msgid "Unique device identifier"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/models.py:280
msgid "Registration token"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/models.py:288
#: venv/lib/python3.10/site-packages/fcm_django/models.py:390
msgid "FCM device"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/models.py:391
msgid "FCM devices"
msgstr ""

#: venv/lib/python3.10/site-packages/fcm_django/settings.py:8
msgid "FCM Django"
msgstr ""

#: venv/lib/python3.10/site-packages/httplib2/__init__.py:408
#, python-format
msgid "Content purported to be compressed with %s but failed to decompress."
msgstr ""

#: venv/lib/python3.10/site-packages/httplib2/__init__.py:572
#, python-format
msgid "Unsupported value for qop: %s."
msgstr ""

#: venv/lib/python3.10/site-packages/httplib2/__init__.py:576
#: venv/lib/python3.10/site-packages/httplib2/__init__.py:654
#, python-format
msgid "Unsupported value for algorithm: %s."
msgstr ""

#: venv/lib/python3.10/site-packages/httplib2/__init__.py:649
msgid "The challenge doesn't contain a server nonce, or this one is empty."
msgstr ""

#: venv/lib/python3.10/site-packages/httplib2/__init__.py:659
#, python-format
msgid "Unsupported value for pw-algorithm: %s."
msgstr ""

#: venv/lib/python3.10/site-packages/httplib2/__init__.py:1468
msgid "Redirected but the response is missing a Location: header."
msgstr ""

#: venv/lib/python3.10/site-packages/kombu/transport/qpid.py:1311
#, python-format
msgid "Attempting to connect to qpid with SASL mechanism %s"
msgstr ""

#: venv/lib/python3.10/site-packages/kombu/transport/qpid.py:1316
#, python-format
msgid "Connected to qpid with SASL mechanism %s"
msgstr ""

#: venv/lib/python3.10/site-packages/kombu/transport/qpid.py:1334
#, python-format
msgid "Unable to connect to qpid with SASL mechanism %s"
msgstr ""

#: venv/lib/python3.10/site-packages/modeltranslation/widgets.py:32
#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_list.html:74
msgid "None"
msgstr ""

#: venv/lib/python3.10/site-packages/mypy/main.py:405
#, python-format
msgid "%(prog)s: error: %(message)s\n"
msgstr ""

#: venv/lib/python3.10/site-packages/timezone_field/rest_framework.py:10
msgid "A valid timezone is required."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/admin.py:124
#: venv/lib/python3.10/site-packages/unfold/templatetags/unfold_list.py:329
msgid "Select record"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/admin.py:156
#: venv/lib/python3.10/site-packages/unfold/admin.py:177
msgid "Select value"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/admin.py:546
msgid "Select action"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/filters/admin.py:59
#: venv/lib/python3.10/site-packages/unfold/contrib/filters/admin.py:117
msgid "All"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/filters/admin.py:83
#: venv/lib/python3.10/site-packages/unfold/contrib/filters/admin.py:106
#: venv/lib/python3.10/site-packages/unfold/contrib/filters/admin.py:123
#: venv/lib/python3.10/site-packages/unfold/contrib/filters/admin.py:150
#: venv/lib/python3.10/site-packages/unfold/contrib/filters/admin.py:167
#: venv/lib/python3.10/site-packages/unfold/contrib/filters/templates/unfold/filters/filters_date_range.html:5
#: venv/lib/python3.10/site-packages/unfold/contrib/filters/templates/unfold/filters/filters_datetime_range.html:5
#: venv/lib/python3.10/site-packages/unfold/contrib/filters/templates/unfold/filters/filters_numeric_range.html:5
#: venv/lib/python3.10/site-packages/unfold/contrib/filters/templates/unfold/filters/filters_numeric_single.html:5
#: venv/lib/python3.10/site-packages/unfold/contrib/filters/templates/unfold/filters/filters_numeric_slider.html:7
#, python-format
msgid "By %(filter_title)s"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/filters/forms.py:77
msgid "Value"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/filters/forms.py:93
#: venv/lib/python3.10/site-packages/unfold/contrib/filters/forms.py:133
msgid "From"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/filters/forms.py:100
#: venv/lib/python3.10/site-packages/unfold/contrib/filters/forms.py:143
msgid "To"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/filters/forms.py:169
msgid "Date from"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/filters/forms.py:185
msgid "Date to"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/filters/templates/unfold/filters/filters_numeric_slider.html:30
msgid "Not enough data."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/array.html:30
msgid "Add new item"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:7
msgid "Paragraph"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:11
msgid "Underlined"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:15
msgid "Bold"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:19
msgid "Italic"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:23
msgid "Strike"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:27
#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:98
msgid "Link"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:35
#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:39
#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:43
#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:47
msgid "Heading"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:55
msgid "Quote"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:63
msgid "Unordered list"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:67
msgid "Ordered list"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:71
msgid "Indent increase"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:75
msgid "Indent decrease"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:83
msgid "Undo"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:87
msgid "Redo"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:95
msgid "Enter an URL"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/forms/templates/unfold/forms/helpers/toolbar.html:102
msgid "Unlink"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/admin/guardian/model/change_form.html:8
#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/admin/guardian/model/obj_perms_manage.html:22
#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/admin/guardian/model/obj_perms_manage_group.html:25
#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/admin/guardian/model/obj_perms_manage_user.html:26
msgid "Object permissions"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/admin/guardian/model/obj_perms_manage.html:10
#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/admin/guardian/model/obj_perms_manage_group.html:13
#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/admin/guardian/model/obj_perms_manage_user.html:14
#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/export.html:17
#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import.html:28
#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_form.html:11
#: venv/lib/python3.10/site-packages/unfold/templates/admin/app_index.html:15
#: venv/lib/python3.10/site-packages/unfold/templates/admin/auth/user/change_password.html:13
#: venv/lib/python3.10/site-packages/unfold/templates/admin/base.html:23
#: venv/lib/python3.10/site-packages/unfold/templates/admin/change_form.html:18
#: venv/lib/python3.10/site-packages/unfold/templates/admin/change_list.html:31
#: venv/lib/python3.10/site-packages/unfold/templates/admin/delete_confirmation.html:17
#: venv/lib/python3.10/site-packages/unfold/templates/admin/delete_selected_confirmation.html:17
#: venv/lib/python3.10/site-packages/unfold/templates/admin/object_history.html:9
#: venv/lib/python3.10/site-packages/unfold/templates/registration/password_change_done.html:12
#: venv/lib/python3.10/site-packages/unfold/templates/registration/password_change_form.html:12
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/site_icon.html:6
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/site_icon.html:8
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/site_icon.html:10
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/site_logo.html:5
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/site_logo.html:7
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/site_logo.html:9
msgid "Home"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/admin/guardian/model/obj_perms_manage_group.html:39
#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/admin/guardian/model/obj_perms_manage_user.html:40
#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_list.html:10
#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_list.html:44
msgid "Object"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/admin/guardian/model/obj_perms_manage_group.html:42
#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/unfold/guardian/group_form.html:15
msgid "Group"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/admin/guardian/model/obj_perms_manage_group.html:49
#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/admin/guardian/model/obj_perms_manage_user.html:50
#: venv/lib/python3.10/site-packages/unfold/templates/admin/pagination.html:38
#: venv/lib/python3.10/site-packages/unfold/templates/admin/submit_line.html:10
msgid "Save"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/unfold/guardian/group_form.html:7
msgid "Group permissions"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/unfold/guardian/group_form.html:51
#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/unfold/guardian/user_form.html:51
msgid "Edit"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/unfold/guardian/group_form.html:67
msgid "Manage group"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/unfold/guardian/user_form.html:7
msgid "User permissions"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/guardian/templates/unfold/guardian/user_form.html:67
msgid "Manage user"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/change_form.html:8
#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/change_list_export_item.html:4
#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/export.html:28
msgid "Export"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/change_list_import_item.html:4
#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import.html:39
msgid "Import"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/export.html:41
#, python-format
msgid ""
"\n"
"                    Export %(len)s selected item.\n"
"                "
msgid_plural ""
"\n"
"                    Export %(len)s selected items.\n"
"                "
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/export.html:60
msgid "This exporter will export the following fields"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/export.html:79
#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_form.html:21
msgid "Submit"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_confirm.html:10
msgid ""
"Below is a preview of data to be imported. If you are satisfied with the "
"results, click 'Confirm import'"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_confirm.html:15
msgid "Confirm import"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_errors.html:19
msgid "Line number"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_preview.html:22
msgid "New"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_preview.html:24
msgid "Skipped"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_preview.html:28
msgid "Update"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_validation.html:6
msgid "Some rows failed to validate"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_validation.html:10
msgid ""
"Please correct these errors in your data where possible, then reupload it "
"using the form above."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_validation.html:22
#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_validation.html:40
msgid "Row"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_validation.html:26
#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_validation.html:44
msgid "Errors"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/import_validation.html:70
msgid "Non field specific"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/resource_fields_list.html:6
msgid "This exporter will export the following fields: "
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/import_export/templates/admin/import_export/resource_fields_list.html:8
msgid "This importer will import the following fields: "
msgstr ""

#. Translators: Model verbose name and instance representation,
#. suitable to be an item in a list.
#: venv/lib/python3.10/site-packages/unfold/contrib/inlines/admin.py:94
#, python-format
msgid "%(class_name)s %(instance)s"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/inlines/admin.py:106
#, python-format
msgid ""
"Deleting %(class_name)s %(instance)s would require deleting the following "
"protected related objects: %(related_objects)s"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history.html:8
msgid ""
"Choose a date from the list below to revert to a previous version of this "
"object."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history.html:16
msgid "This object doesn't have a change history."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_form.html:22
#: venv/lib/python3.10/site-packages/unfold/templates/admin/change_form_object_tools.html:6
#: venv/lib/python3.10/site-packages/unfold/templates/admin/object_history.html:21
msgid "History"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_form.html:26
#: venv/lib/python3.10/site-packages/unfold/templates/admin/edit_inline/stacked.html:59
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/app_list_default.html:38
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/edit_inline/tabular_title.html:25
msgid "View"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_form.html:29
#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/submit_line.html:9
msgid "Revert"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_form.html:48
msgid ""
"Press the 'Revert' button below to revert to this version of the object."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_form.html:52
msgid "Press the 'Change History' button below to edit the history."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_list.html:20
#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_list.html:56
#: venv/lib/python3.10/site-packages/unfold/templates/admin/object_history.html:34
#: venv/lib/python3.10/site-packages/unfold/templates/admin/object_history.html:50
msgid "Date/time"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_list.html:24
#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_list.html:60
msgid "Comment"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_list.html:28
#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_list.html:64
msgid "Changed by"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_list.html:32
#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_list.html:78
msgid "Change reason"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_list.html:36
#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/object_history_list.html:82
msgid "Changes"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/submit_line.html:15
msgid "Change History"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/contrib/simple_history/templates/simple_history/submit_line.html:20
#: venv/lib/python3.10/site-packages/unfold/templates/admin/submit_line.html:34
msgid "Close"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/forms.py:55
msgid "Select action to run"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/forms.py:113
msgid ""
"Raw passwords are not stored, so there is no way to see this user’s "
"password, but you can change the password using <a href=\"{}\" class=\"text-"
"primary-600 dark:text-primary-500\">this form</a>."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/actions.html:22
msgid "Run the selected action"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/actions.html:23
msgid "Run"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/actions.html:43
msgid "Click here to select the objects across all pages"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/actions.html:44
#, python-format
msgid "Select all %(total_count)s %(module_name)s"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/actions.html:50
msgid "Clear selection"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/app_index.html:5
#: venv/lib/python3.10/site-packages/unfold/templates/admin/base_site.html:3
#: venv/lib/python3.10/site-packages/unfold/templates/admin/index.html:7
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/layouts/base.html:11
msgid "Django site admin"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/app_index.html:30
#: venv/lib/python3.10/site-packages/unfold/templates/admin/base_site.html:6
#: venv/lib/python3.10/site-packages/unfold/templates/admin/index.html:12
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/layouts/base.html:6
msgid "Django administration"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/app_list.html:12
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/app_list_default.html:9
#, python-format
msgid "Models in the %(name)s application"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/app_list.html:47
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/app_list.html:98
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/app_list_default.html:59
msgid "You don’t have permission to view or edit anything."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/auth/user/add_form.html:7
msgid ""
"First, enter a username and password. Then, you’ll be able to edit more user "
"options."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/auth/user/add_form.html:9
msgid "Enter a username and password."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/auth/user/change_password.html:25
#: venv/lib/python3.10/site-packages/unfold/templates/admin/auth/user/change_password.html:55
#: venv/lib/python3.10/site-packages/unfold/templates/registration/password_change_done.html:15
#: venv/lib/python3.10/site-packages/unfold/templates/registration/password_change_form.html:15
#: venv/lib/python3.10/site-packages/unfold/templates/registration/password_change_form.html:46
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/account_links.html:23
msgid "Change password"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/auth/user/change_password.html:44
#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/change_form.html:32
#: venv/lib/python3.10/site-packages/unfold/templates/admin/change_list_results.html:90
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/add_link.html:5
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/add_link.html:8
#, python-format
msgid "Add %(name)s"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/change_form_object_tools.html:12
#: venv/lib/python3.10/site-packages/unfold/templates/admin/edit_inline/stacked.html:71
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/edit_inline/tabular_title.html:33
msgid "View on site"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/change_list.html:72
msgid "Filters"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/change_list_results.html:28
msgid "Select all rows"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/change_list_results.html:42
msgid "Toggle sorting"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/change_list_results.html:50
msgid "Remove from sorting"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/change_list_results.html:56
#, python-format
msgid "Sorting priority: %(priority_number)s"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/change_list_results.html:98
msgid "No results found"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/change_list_results.html:102
msgid ""
"This page yielded into no results. Create a new item or reset your filters."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/change_list_results.html:116
msgid "Reset filters"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/delete_confirmation.html:37
#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would result in deleting "
"related objects, but your account doesn't have permission to delete the "
"following types of objects:"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/delete_confirmation.html:53
#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would require deleting the "
"following protected related objects:"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/delete_confirmation.html:69
#, python-format
msgid ""
"Are you sure you want to delete the %(object_name)s \"%(escaped_object)s\"? "
"All of the following related items will be deleted:"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/delete_confirmation.html:76
#: venv/lib/python3.10/site-packages/unfold/templates/admin/delete_selected_confirmation.html:71
msgid "Objects"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/delete_selected_confirmation.html:27
msgid "Delete multiple objects"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/delete_selected_confirmation.html:37
#, python-format
msgid ""
"Deleting the selected %(objects_name)s would result in deleting related "
"objects, but your account doesn't have permission to delete the following "
"types of objects:"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/delete_selected_confirmation.html:50
#, python-format
msgid ""
"Deleting the selected %(objects_name)s would require deleting the following "
"protected related objects:"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/delete_selected_confirmation.html:64
#, python-format
msgid ""
"Are you sure you want to delete the selected %(objects_name)s? All of the "
"following objects and their related items will be deleted:"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/filter.html:5
#, python-format
msgid " By %(filter_title)s "
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/includes/object_delete_summary.html:5
msgid "Summary"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/login.html:31
msgid "Welcome back to"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/login.html:42
#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/login.html:63
msgid "Log in"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/login.html:72
msgid "Forgotten your password or username?"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/login.html:84
msgid "Return to site"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/object_history.html:81
msgid "entry"
msgid_plural "entries"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/object_history.html:84
msgid ""
"This object doesn’t have a change history. It probably wasn’t added via this "
"admin site."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/pagination.html:31
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/welcomemsg.html:26
msgid "Show all"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/search_form.html:7
msgid "Type to search"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/submit_line.html:23
msgid "Save and continue editing"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/submit_line.html:25
msgid "Save and view"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/submit_line.html:40
msgid "Save and add another"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/admin/submit_line.html:46
msgid "Save as new"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/registration/logged_out.html:11
msgid "You have been successfully logged out from the administration"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/registration/logged_out.html:16
msgid "Thanks for spending some quality time with the web site today."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/registration/logged_out.html:20
msgid "Log in again"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/registration/password_change_done.html:26
msgid "Your password was changed."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/registration/password_change_form.html:35
msgid ""
"Please enter your old password, for security’s sake, and then enter your new "
"password twice so we can verify you typed it in correctly."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/account_links.html:17
msgid "View site"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/account_links.html:32
msgid "Log out"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/actions_row.html:4
msgid "More actions"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/app_list.html:68
msgid "All applications"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/app_list_default.html:31
msgid "Add"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/boolean.html:10
msgid "True"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/boolean.html:12
msgid "False"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/change_list_filter.html:22
msgid "Apply Filters"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/change_list_filter_actions.html:8
msgid "Hide counts"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/change_list_filter_actions.html:12
msgid "Show counts"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/change_list_filter_actions.html:19
msgid "Clear all filters"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/delete_submit_line.html:5
msgid "No, take me back"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/delete_submit_line.html:9
msgid "Yes, I’m sure"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/display_header.html:10
msgid "Record picture"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/edit_inline/tabular_delete.html:4
msgid "Remove"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/edit_inline/tabular_heading.html:21
msgid "Delete?"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/history.html:9
msgid "Recent actions"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/history.html:28
msgid "Unknown content"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/messages/errornote.html:6
msgid "Please correct the error below."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/messages/errornote.html:8
msgid "Please correct the errors below."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/search.html:18
msgid "Search apps and models..."
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/search.html:18
msgid "Filter navigation items"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/tab_list.html:24
msgid "General"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/theme_switch.html:15
msgid "Dark"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/theme_switch.html:22
msgid "Light"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/theme_switch.html:29
msgid "System"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/welcomemsg.html:26
#, python-format
msgid "%(counter)s result"
msgid_plural "%(counter)s results"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/welcomemsg.html:26
#, python-format
msgid "%(full_result_count)s total"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/helpers/welcomemsg.html:34
msgid "Welcome,"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/widgets/clearable_file_input.html:6
msgid "Image preview"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/widgets/clearable_file_input.html:24
#: venv/lib/python3.10/site-packages/unfold/templates/unfold/widgets/clearable_file_input_small.html:17
msgid "Choose file to upload"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/widgets/related_widget_wrapper.html:16
#, python-format
msgid "Change selected %(model)s"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/widgets/related_widget_wrapper.html:26
#, python-format
msgid "Add another %(model)s"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/widgets/related_widget_wrapper.html:35
#, python-format
msgid "View selected %(model)s"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templates/unfold/widgets/related_widget_wrapper.html:45
#, python-format
msgid "Delete selected %(model)s"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/templatetags/unfold_list.py:113
msgid "Select all objects on this page for an action"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/widgets.py:462
msgid "Date"
msgstr ""

#: venv/lib/python3.10/site-packages/unfold/widgets.py:556
msgid "Select currency"
msgstr ""
