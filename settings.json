{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true, "source.fixAll": true}, "python.formatting.provider": "black", "python.linting.flake8Enabled": true, "files.trimTrailingWhitespace": true, "python.linting.mypyEnabled": true, "python.linting.pylintEnabled": false, "python.analysis.typeCheckingMode": "basic", "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.pytestArgs": ["--maxfail=2", "--disable-warnings"], "python.envFile": "/.env"}