services:
  django:
    build:
      context: ../../
      dockerfile: infra/docker/Dockerfile.dev
    volumes:
      - ../../:/app
    ports:
      - "${FORWARD_DJANGO_PORT:-8000}:8000"
      - "${FORWARD_DJANGO_SOCKET_PORT:-8001}:8001"
    depends_on:
      postgres:
        condition: service_healthy
        restart: true
      valkey:
        condition: service_healthy
        restart: true
    networks:
      - pioo
    restart: on-failure

  postgres:
    image: postgres:17
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}" ]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - pioo
    restart: unless-stopped

  pgadmin:
    image: dpage/pgadmin4
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_DEFAULT_EMAIL:-<EMAIL>}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_DEFAULT_PASSWORD:-1Password!}
      - PGADMIN_CONFIG_SERVER_MODE=${PGADMIN_CONFIG_SERVER_MODE:-True}
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ../pgadmin/servers.json:/pgadmin4/servers.json
    ports:
      - "${FORWARD_PGADMIN_PORT:-5050}:80"
    depends_on:
      - postgres
    networks:
      - pioo
    restart: unless-stopped

  pgbouncer:
    image: edoburu/pgbouncer
    volumes:
      - ../pgbouncer/userlist.txt:/etc/pgbouncer/userlist.txt
    environment:
      PG_CONFIG: |
        [pgbouncer]
        listen_addr = 0.0.0.0
        listen_port = 5432
        auth_type = md5
        auth_file = /etc/pgbouncer/userlist.txt
        pool_mode = transaction
        max_client_conn = 100
        max_db_connections = 100
        default_pool_size = 40
        reserve_pool_size = 5
        reserve_pool_timeout = 2
        log_connections = 1
        log_disconnections = 1
    env_file:
      - ../../.env
    depends_on:
      - postgres
    networks:
      - pioo
    restart: unless-stopped
  valkey:
    image: valkey/valkey:alpine
    healthcheck:
      test: [ "CMD", "valkey-cli", "ping" ]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
    volumes:
      - valkey_data:/data
    networks:
      - pioo
    restart: unless-stopped

  mailpit:
    image: 'axllent/mailpit:latest'
    ports:
      - '${FORWARD_MAILPIT_PORT:-8025}:8025'
    networks:
      - pioo
    restart: unless-stopped

networks:
  pioo:
    driver: bridge

volumes:
  postgres_data:
  pgadmin_data:
  valkey_data:
