services:
  web:
    image: caddy:latest
    ports:
      - '${FORWARD_API_PORT:-80}:80'
    volumes:
      - ../caddy/Caddyfile.local:/etc/caddy/Caddyfile
      - caddy_data:/data
      - caddy_config:/config
      - static_data:/var/www/staticfiles
      - media_data:/var/www/media
    depends_on:
      - django
    networks:
      - pioo
    restart: unless-stopped

  django:
    image: "dxhltd/pioo-backend:${DOCKER_IMAGE_TAG:-local}"
    volumes:
      - static_data:/app/staticfiles
      - media_data:/app/media
    depends_on:
      postgres:
        condition: service_healthy
        restart: true
      valkey:
        condition: service_healthy
        restart: true
    networks:
      - pioo
    restart: on-failure

  postgres:
    image: postgres:latest
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}" ]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - pioo
    restart: unless-stopped

  pgadmin:
    image: dpage/pgadmin4
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_DEFAULT_EMAIL:-<EMAIL>}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_DEFAULT_PASSWORD:-1Password!}
      - PGADMIN_CONFIG_SERVER_MODE=${PGADMIN_CONFIG_SERVER_MODE:-True}
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ../pgadmin/servers.json:/pgadmin4/servers.json
    ports:
      - "${FORWARD_PGADMIN_PORT:-5050}:80"
    depends_on:
      - postgres
    networks:
      - pioo
    restart: unless-stopped

  valkey:
    image: valkey/valkey:alpine
    healthcheck:
      test: [ "CMD", "valkey-cli", "ping" ]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
    volumes:
      - valkey_data:/data
    networks:
      - pioo
    restart: unless-stopped

  mailpit:
    image: 'axllent/mailpit:latest'
    ports:
      - '${FORWARD_MAILPIT_PORT:-8025}:8025'
    networks:
      - pioo
    restart: unless-stopped

networks:
  pioo:
    driver: bridge

volumes:
  caddy_data:
  caddy_config:
  static_data:
  media_data:
  postgres_data:
  pgadmin_data:
  valkey_data:
