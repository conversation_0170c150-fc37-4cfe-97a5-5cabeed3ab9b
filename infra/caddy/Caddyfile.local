:80 {
    # Enable gzip compression for responses
    encode gzip

    # Proxy all requests to the Django app
    reverse_proxy django:8000

    # Serve static files
    handle_path /static/* {
        root * /var/www/staticfiles
        file_server
    }

    # Serve media files
    handle_path /media/* {
        root * /var/www/media
        file_server
    }

    # Security headers
    header {
        -Server
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    }

    # Log requests
    log {
        output stdout
        format console
    }
}