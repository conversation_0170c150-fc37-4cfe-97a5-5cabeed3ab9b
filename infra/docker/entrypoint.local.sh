#!/bin/bash

# Strict mode settings
set -euo pipefail
IFS=$'\n\t'

# Auto-detect CPU cores for optimal Gunicorn workers
CPU_CORES=$(grep -c ^processor /proc/cpuinfo || echo 2)
RAM_TOTAL=$(awk '/MemTotal/ {print int($2 / 1024)}' /proc/meminfo)  # Get RAM in MB

export GUNICORN_WORKERS="${GUNICORN_WORKERS:-$((CPU_CORES))}"
export GUNICORN_THREADS="${GUNICORN_THREADS:-2}"
export GUNICORN_TIMEOUT="${GUNICORN_TIMEOUT:-120}"
export GUNICORN_WORKER_CONNECTIONS="${GUNICORN_WORKER_CONNECTIONS:-100}"
export WSGI_PORT="${WSGI_PORT:-8000}"
export ASGI_PORT="${ASGI_PORT:-8001}"
export MAX_STARTUP_RETRY="${MAX_STARTUP_RETRY:-5}"
export STARTUP_RETRY_INTERVAL="${STARTUP_RETRY_INTERVAL:-5}"
export GRACEFUL_TIMEOUT="${GRACEFUL_TIMEOUT:-30}"

# Process ID tracking
declare -a PIDS=()

log() { echo "$(date +'%Y-%m-%d %H:%M:%S') [INFO] $*" >&2; }
log_error() { echo "$(date +'%Y-%m-%d %H:%M:%S') [ERROR] $*" >&2; }
log_warning() { echo "$(date +'%Y-%m-%d %H:%M:%S') [WARNING] $*" >&2; }

# Check if required services are available
check_service() {
    local host=$1 port=$2 service=$3
    for i in $(seq 1 "$MAX_STARTUP_RETRY"); do
        if command -v nc >/dev/null; then
            if nc -z "$host" "$port"; then
                log "$service is up on $host:$port"
                return 0
            fi
        elif command -v curl >/dev/null; then
            if curl --silent --head http://"$host":"$port" >/dev/null; then
                log "$service is up on $host:$port"
                return 0
            fi
        fi
        log_warning "Waiting for $service ($host:$port)... attempt $i/$MAX_STARTUP_RETRY"
        sleep "$STARTUP_RETRY_INTERVAL"
    done
    log_error "$service failed to start!"
    return 1
}

# Graceful shutdown handler
cleanup() {
    log "Initiating graceful shutdown..."
    for pid in "${PIDS[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            kill -TERM "$pid" 2>/dev/null || true
        fi
    done

    timeout "$GRACEFUL_TIMEOUT" wait "${PIDS[@]}" || log_warning "Some processes did not exit cleanly"

    # Force kill any remaining processes
    for pid in "${PIDS[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            log_warning "Force killing process $pid"
            kill -9 "$pid" 2>/dev/null || true
        fi
    done

    log "Shutdown complete"
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT SIGQUIT

# Apply database migrations
log "Applying database migrations..."
python manage.py migrate --noinput

# Collect static files
log "Collecting static files..."
python manage.py collectstatic --noinput --clear

# Ensure Django superuser exists
if [[ -n "${DJANGO_SUPERUSER_USERNAME:-}" && -n "${DJANGO_SUPERUSER_EMAIL:-}" && -n "${DJANGO_SUPERUSER_PASSWORD:-}" ]]; then
    log "Ensuring Django superuser exists..."
    python manage.py shell <<EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username="${DJANGO_SUPERUSER_USERNAME}").exists():
    User.objects.create_superuser("${DJANGO_SUPERUSER_USERNAME}", "${DJANGO_SUPERUSER_EMAIL}", "${DJANGO_SUPERUSER_PASSWORD}")
EOF
fi

# Start Gunicorn (WSGI for Django)
log "Starting Gunicorn (WSGI) with $GUNICORN_WORKERS workers and $GUNICORN_THREADS threads..."
exec gunicorn config.wsgi:application \
    --bind "0.0.0.0:$WSGI_PORT" \
    --workers "$GUNICORN_WORKERS" \
    --threads "$GUNICORN_THREADS" \
    --timeout "$GUNICORN_TIMEOUT" \
    --worker-connections "$GUNICORN_WORKER_CONNECTIONS" \
    --access-logfile '-' \
    --error-logfile '-' \
    --capture-output \
    --enable-stdio-inheritance &  
PIDS+=($!)

# Start Uvicorn (ASGI for Django Channels)
if [[ "${ENABLE_UVICORN:-false}" == "true" ]]; then
    log "Starting Uvicorn (ASGI)..."
    exec uvicorn config.asgi:application \
        --host 0.0.0.0 \
        --port "$ASGI_PORT" \
        --log-level info &
    PIDS+=($!)
fi

# Start Celery worker (if enabled)
log "Starting Celery worker..."
exec celery -A config worker --loglevel=info &
PIDS+=($!)

log "Starting Celery beat..."
exec celery -A config beat --loglevel=info &
PIDS+=($!)

# Health check for Gunicorn and Uvicorn
check_service "localhost" "$WSGI_PORT" "Gunicorn"
if [[ "${ENABLE_UVICORN:-false}" == "true" ]]; then
    check_service "localhost" "$ASGI_PORT" "Uvicorn"
fi

# Wait for all processes and handle failures
while true; do
    for pid in "${PIDS[@]}"; do
        if ! kill -0 "$pid" 2>/dev/null; then
            log_error "Process $pid has died unexpectedly"
            cleanup
            exit 1
        fi
    done
    sleep 1
done