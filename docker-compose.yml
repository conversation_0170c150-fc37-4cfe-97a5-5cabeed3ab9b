version: '3'
 
services:
  mysql:
    image: mysql:8.0.19
    command: mysqld --default-authentication-plugin=mysql_native_password
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: admin
      MYSQL_DATABASE: admin
      MYSQL_USER: root
      MYSQL_PASSWORD: admin
    volumes:
      - ./mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

  postgres:
    image: postgres:latest
    restart: always
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin
      POSTGRES_DB: admin
    volumes:
      - ./postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
      
  # adminer:
  #   image: adminer
  #   ports:
  #     - '8080:8080'