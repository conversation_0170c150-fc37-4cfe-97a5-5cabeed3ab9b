import os
import sys

def create_project_structure(app_name, model_name):
    BASE_DIR = os.path.join("apps", app_name)

    DIRECTORIES = [
        BASE_DIR,
        os.path.join(BASE_DIR, "api"),
        os.path.join(BASE_DIR, "api", "v1"),
        os.path.join(BASE_DIR, "api", "v1", "serializers"),
        os.path.join(BASE_DIR, "api", "v1", "views"),
        os.path.join(BASE_DIR, "assets"),
        os.path.join(BASE_DIR, "fixtures"),
        os.path.join(BASE_DIR, "management"),
        os.path.join(BASE_DIR, "management", "commands"),
        os.path.join(BASE_DIR, "models"),
        os.path.join(BASE_DIR, "repositories"),
        os.path.join(BASE_DIR, "services"),
        os.path.join(BASE_DIR, "tests"),  # Add tests folder
    ]

    FILES = {
        "__init__.py": "",
        f"{app_name}/__init__.py": "",
        f"{app_name}/helpers.py": "from django.utils.translation import gettext_lazy as _\n\n# Helper functions here",
        f"{app_name}/enums.py": '''from dxh_common.utils.enums import BaseEnum\n\nclass Type(BaseEnum):\n    PRIVATE = True\n    PUBLIC = False\n''',
        f"{app_name}/admin.py": f'''from django.contrib import admin
from dxh_common.admin import BaseAdmin
from config.admin import *  # noqa: F403, F401
from config.unfold import *  # noqa: F403, F401
from apps.{app_name}.models.{model_name} import {model_name.capitalize()}

class {model_name.capitalize()}Admin(BaseAdmin):
    list_display = ("name",)
    search_fields = ("name",)

admin.site.register({model_name.capitalize()}, {model_name.capitalize()}Admin)
''',
        f"{app_name}/api/__init__.py": "",
        f"{app_name}/api/v1/__init__.py": "",
        f"{app_name}/api/v1/serializers/__init__.py": f"from apps.{app_name}.api.v1.serializers.{model_name}_serializer import {model_name.capitalize()}Serializer\n",
        f"{app_name}/api/v1/serializers/{model_name}_serializer.py": f'''from dxh_common.base.base_serializer import BaseModelSerializer
from apps.{app_name}.models import {model_name.capitalize()}

class {model_name.capitalize()}Serializer(BaseModelSerializer):
    class Meta:
        model = {model_name.capitalize()}
        fields = ["id", "name"]
''',
        f"{app_name}/api/v1/views/__init__.py": f"from apps.{app_name}.api.v1.views.{model_name}_view import {model_name.capitalize()}ListView\n",
        f"{app_name}/api/v1/views/{model_name}_view.py": f'''from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger

from apps.{app_name}.services.{model_name}_service import {model_name.capitalize()}Service
from apps.{app_name}.api.v1.serializers import {model_name.capitalize()}Serializer

logger = Logger(__name__)

class {model_name.capitalize()}ListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.service = {model_name.capitalize()}Service()

    def get(self, request, id):
        logger.info({{"event": "{model_name.capitalize()}ListView:get", "message": "Fetching data", "id": id}})

        try:
            obj = self.service.get(id=id)
            if not obj:
                return Response({{"message": "Not found", "errors": {{"id": f"No {model_name} found with ID {id}."}}}}, status=status.HTTP_404_NOT_FOUND)

            return Response({{"message": "Success", "data": {model_name.capitalize()}Serializer(obj).data}}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({{"event": "{model_name.capitalize()}ListView:get", "message": "Unexpected error", "error": str(e), "id": id}})
            raise e
''',
        f"{app_name}/api/v1/urls.py": f'''from django.urls import path
from apps.{app_name}.api.v1 import views

urlpatterns = [
    path('{model_name}/<int:id>/', views.{model_name.capitalize()}ListView.as_view(), name='{model_name}-view'),
]
''',
        f"{app_name}/apps.py": f'''from django.apps import AppConfig
from dxh_libraries.translation import gettext_lazy as _

class {app_name.capitalize()}Config(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "apps.{app_name}"
    verbose_name = _("{app_name.capitalize()}")
''',
        f"{app_name}/models/__init__.py": f"from apps.{app_name}.models.{model_name} import {model_name.capitalize()}\n",
        f"{app_name}/models/{model_name}.py": f'''from dxh_common.base.base_model import BaseModel
from django.db import models
from dxh_libraries.translation import gettext_lazy as _

class {model_name.capitalize()}(BaseModel):
    name = models.CharField(max_length=255, verbose_name=_("Name"))
''',
        f"{app_name}/repositories/__init__.py": f"from apps.{app_name}.repositories.{model_name}_repository import {model_name.capitalize()}Repository\n",
        f"{app_name}/repositories/{model_name}_repository.py": f'''from dxh_common.base.base_repository import BaseRepository
from apps.{app_name}.models import {model_name.capitalize()}

class {model_name.capitalize()}Repository(BaseRepository):
    def __init__(self):
        super().__init__({model_name.capitalize()})
''',
        f"{app_name}/services/__init__.py": f"from apps.{app_name}.services.{model_name}_service import {model_name.capitalize()}Service\n",
        f"{app_name}/services/{model_name}_service.py": f'''from apps.{app_name}.repositories.{model_name}_repository import {model_name.capitalize()}Repository
from dxh_common.base.base_service import BaseService

class {model_name.capitalize()}Service(BaseService):
    def __init__(self):
        super().__init__({model_name.capitalize()}Repository())
''',
        f"{app_name}/urls.py": f'''from django.urls import path, include

app_name = "{app_name}"
api_url = "apps.{app_name}.api.v1.urls"

urlpatterns = [
    path("v1/", include(api_url)),
]
''',
        f"{app_name}/assets/.gitkeep": "",
        f"{app_name}/fixtures/.gitkeep": "",
        f"{app_name}/management/commands/.gitkeep": "",
        f"{app_name}/tests/__init__.py": "",
    }

    os.makedirs(BASE_DIR, exist_ok=True)

    for directory in DIRECTORIES:
        os.makedirs(directory, exist_ok=True)

    for filepath, content in FILES.items():
        with open(os.path.join("apps", filepath), "w", encoding="utf-8") as f:
            f.write(content)

    print(f"App '{app_name}' created successfully with model '{model_name}'.")


if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python create_app_structure.py <app_name> <model_name>")
        sys.exit(1)

    app_name = sys.argv[1]
    model_name = sys.argv[2]

    create_project_structure(app_name, model_name)
