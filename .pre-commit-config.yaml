default_language_version:
    python: python3.11
default_stages: [pre-commit]
fail_fast: true

repos:
# Basic pre-commit hooks
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v5.0.0
  hooks:
  - id: check-xml
  - id: check-yaml
  - id: check-added-large-files
  - id: check-ast
  - id: debug-statements
  - id: no-commit-to-branch
    args: ['--branch', 'development', '--branch', 'staging', '--branch', 'production']

# Dependencies checker
- repo: https://github.com/Lucas-C/pre-commit-hooks-safety
  rev: v1.4.0
  hooks:
  - id: python-safety-dependencies-check
    files: common.txt, local.txt

# Flake8
- repo: https://github.com/pycqa/flake8
  rev: 7.1.1
  hooks:
    - id: flake8
      args: ["--config=.flake8"]

# Black for Python Code Formatter
- repo: https://github.com/psf/black
  rev: 25.1.0
  hooks:
    - id: black
      language_version: python3.11
      types: [python]
      pass_filenames: true
      always_run: true
      args: ["--config=pyproject.toml"]

# # isort For sorting imports
# - repo: https://github.com/pycqa/isort
#   rev: 6.0.0
#   hooks:
#     - id: isort
#       args: [--sp, pyproject.toml]

# - repo: https://github.com/astral-sh/ruff-pre-commit
#   rev: v0.4.4
#   hooks:
#     - id: ruff
#       name: ruff
#       entry: ruff check --fix
#       language: python
#     - id: ruff-format
#       name: ruff-format
#       entry: ruff format
#       language: python

# Codespell for spell checking
# - repo: https://github.com/codespell-project/codespell
#   rev: v2.2.4
#   hooks:
#   - id: codespell
#     exclude: >
#       (?x)^(
#           .*\test_*.py
#       )$

# Pytest for testing
# - repo: local
#   hooks:
#   - id: pytest-check
#     stages: [commit]
#     types: [python]
#     name: pytest-check
#     entry: pytest
#     language: system
#     pass_filenames: false
#     always_run: true