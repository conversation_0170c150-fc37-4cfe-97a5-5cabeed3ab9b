from django.contrib import admin
from django.contrib.auth import forms as admin_forms
from django.contrib.auth import get_user_model

User = get_user_model()


class BaseAdmin(admin.ModelAdmin):
    """
    A reusable base admin class for shared configurations across admin models.
    Override or extend as needed for consistency.
    """

    # Add shared configurations or utilities here for future use.
    pass


class UserAdminChangeForm(admin_forms.UserChangeForm):
    class Meta(admin_forms.UserChangeForm.Meta):
        model = User


class UserAdminCreationForm(admin_forms.UserCreationForm):
    """
    Form for User Creation in the Admin Area.
    To change user signup, see UserSignupForm and UserSocialSignupForm.
    """

    class Meta(admin_forms.UserCreationForm.Meta):
        model = User
        error_messages = {
            "username": {"unique": "This username has already been taken."},
        }
