from unfold.admin import ModelAdmin
from unfold.forms import UserChangeForm, UserCreationForm
class BaseAdmin(ModelAdmin):
    """
    A base admin class to serve as a common configuration 
    for all admin classes in the application.
    """
    # Future customizations for admin classes can be added here.
    pass


class UserAdminChangeForm(UserChangeForm):
    """
    A base form class to serve as a common configuration 
    for all user admin forms in the application.
    """
    # Future customizations for user admin forms can be added here.
    pass

class UserAdminCreationForm(UserCreationForm):
    """
    A base form class to serve as a common configuration 
    for all user creation forms in the application.
    """
    # Future customizations for user creation forms can be added here.
    pass
