import datetime
import uuid
import re
from django.core.exceptions import ValidationError
from common.constants import PASSWORD_LENGTH
from apps.core.models.system_setting import SystemSetting

def validate_positive(value):
    """Ensure the value is positive."""
    if value < 0:
        raise ValidationError(f"{value} is not a positive number.")

def validate_email(value):
    """Validate that a value is a properly formatted email address."""
    email_regex = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
    if not re.match(email_regex, value):
        raise ValidationError(f"{value} is not a valid email address.")


def validate_password_strength(password):
    try:
        system_setting = SystemSetting.objects.get()
    except SystemSetting.DoesNotExist:
        raise ValidationError("System settings are not configured.")

    if len(password) < PASSWORD_LENGTH:
        raise ValidationError("Password must be at least 8 characters long.")

    if system_setting.password_format:
        if 'uppercase' in system_setting.password_format and not re.search(r'[A-Z]', password):
            raise ValidationError("Password must contain at least one uppercase letter.")

        if 'lowercase' in system_setting.password_format and not re.search(r'[a-z]', password):
            raise ValidationError("Password must contain at least one lowercase letter.")

        if 'number' in system_setting.password_format and not re.search(r'[0-9]', password):
            raise ValidationError("Password must contain at least one number.")

        if 'special' in system_setting.password_format and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            raise ValidationError("Password must contain at least one special character.")

def validate_password(password: str, confirm_password: str) -> bool:
    """
        Validate a password and its confirmation.
    """

    if password != confirm_password:
        return False

    return True

def is_valid_url(url: str) -> bool:
    """
    Validate a URL.
    """
    url_regex = r"^(https?|ftp)://[^\s/$.?#].[^\s]*$"
    return bool(re.match(url_regex, url))


def is_valid_uuid(value: str) -> bool:
    """
    Validate a UUID.
    """
    try:
        uuid_obj = uuid.UUID(value, version=4)
        return str(uuid_obj) == value  # Ensures correct format

    except (ValueError, TypeError):
        return False

def is_valid_date(date_str: str) -> bool:
    """
    Validate if the string is a valid date in YYYY-MM-DD format.
    """
    try:
        datetime.datetime.strptime(date_str, "%Y-%m-%d")
        return True

    except ValueError:
        return False