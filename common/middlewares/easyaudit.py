import json
import contextlib
from asgiref.local import Local
from django.utils.timezone import now
from django.http.request import HttpRequest
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from rest_framework.authentication import get_authorization_header
from rest_framework_simplejwt.authentication import J<PERSON><PERSON><PERSON><PERSON>ication
from rest_framework.authentication import TokenAuthentication
from rest_framework_simplejwt.tokens import AccessToken
from easyaudit.models import LoginEvent
from common.logger import Logger

_thread_locals = Local()
User = get_user_model()
logger = Logger(__name__)


def get_current_request():
    return getattr(_thread_locals, "request", None)


def get_current_user():
    request = get_current_request()
    return (
        getattr(request, "user", AnonymousUser())
        if request
        else AnonymousUser()
    )


def set_current_user(user):
    """Set the user in thread-local storage."""
    try:
        _thread_locals.request.user = user
    except AttributeError:
        _thread_locals.request = HttpRequest()
        _thread_locals.request.user = user


def clear_request():
    """Clear the stored request from thread-local storage."""
    with contextlib.suppress(AttributeError):
        del _thread_locals.request


class CustomLoginEventMiddleware:

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request: HttpRequest):
        """Handles the request synchronously."""

        if any(path in request.path for path in ["login", "logout"]):
            self.set_request_user(request)
            response = self.get_response(request)
            self.log_login_event(request, response)

            clear_request()
            return response

        return self.get_response(request)

    async def __acall__(self, request: HttpRequest):
        """Handles the request asynchronously."""

        if any(path in request.path for path in ["login", "logout"]):
            self.set_request_user(request)
            response = await self.get_response(request)
            self.log_login_event(request, response)

            clear_request()
            return response

        return await self.get_response(request)

    def set_request_user(self, request: HttpRequest):
        """Set the authenticated user for API requests."""

        _thread_locals.request = request
        if request.user and request.user.is_authenticated:
            return

        user = self.get_authenticated_request_user(request)
        request.user = user

    def get_authenticated_request_user(self, request: HttpRequest):
        """Extract user from JWT or Token authentication."""
        auth_header = get_authorization_header(request).decode("utf-8")

        if auth_header:
            auth_type = auth_header.split(" ")[0].lower()

            if auth_type == "bearer":
                jwt_authenticator = JWTAuthentication()
                user, _ = jwt_authenticator.authenticate(request) or (
                    AnonymousUser(),
                    None,
                )
                return user

            if auth_type == "token":
                token_authenticator = TokenAuthentication()
                user, _ = token_authenticator.authenticate(request) or (
                    AnonymousUser(),
                    None,
                )
                return user

        return AnonymousUser()

    def get_user_from_token(self, access_token):
        """Decode JWT access token and return the authenticated user."""
        try:
            token = AccessToken(access_token)
            user_id = token["user_id"]
            user = User.objects.get(id=user_id)

            return user

        except Exception as e:
            logger.error(f"Token Authentication Error: {e}")
            return AnonymousUser()

    def log_login_event(self, request: HttpRequest, response):
        """Log request details and response status."""

        if hasattr(response, "content"):
            try:
                response_data = json.loads(response.content.decode("utf-8"))

                user = request.user
                try:
                    tokens = response_data.get("data", {}).get("tokens")
                    access_token = tokens.get("access") if tokens else None
                except (AttributeError, KeyError):
                    access_token = None

                if access_token:
                    user = self.get_user_from_token(access_token=access_token)
                else:
                    user = self.get_authenticated_request_user(request)

                if isinstance(user, AnonymousUser):
                    user = None

                request.user = user
                event_type = None

                if request.path.endswith("login/"):
                    if response.status_code == 200 and user:
                        event_type = LoginEvent.LOGIN
                    else:
                        event_type = LoginEvent.FAILED

                elif (
                    request.path.endswith("logout/")
                    and response.status_code == 200
                ):
                    event_type = LoginEvent.LOGOUT

                if event_type is not None:
                    LoginEvent.objects.create(
                        login_type=event_type,
                        username=(
                            request.user.username
                            if request.user
                            else request.POST.get("username", "")
                        ),
                        user=request.user,
                        remote_ip=request.META.get("REMOTE_ADDR", ""),
                        datetime=now(),
                    )
                    logger.info(
                        f"Login event recorded: {event_type} for user {request.user}"
                    )

            except json.JSONDecodeError:
                logger.error("Failed to parse response content as JSON.")
