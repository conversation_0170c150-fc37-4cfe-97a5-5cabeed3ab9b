import logging
import traceback
from typing import Optional, Dict, Any


class Logger:
    _instance: Optional["Logger"] = None
    logger: logging.Logger

    def __new__(cls, logger_name: str = __name__):
        """
        Implement the Singleton pattern for the Logger class.
        :param logger_name: Name of the logger, defaults to the module's __name__.
        """
        if cls._instance is None:
            cls._instance = super(Logger, cls).__new__(cls)
            cls._instance.logger = logging.getLogger(logger_name)
            cls._instance._configure_logger()
        return cls._instance

    def _configure_logger(self):
        """
        Configure the logger with default handlers and formatters if not already set.
        """
        if not self.logger.hasHandlers():
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.DEBUG)

    def info(self, message: str, context: Optional[Dict[str, Any]] = None, exc_info: Optional[bool] = False):
        """
        Log an informational message.
        :param message: The log message.
        :param context: Additional context as a dictionary (optional).
        :param exc_info: Whether to include exception information (optional).
        """
        self.logger.info(self._format_message(message, context), exc_info=exc_info)

    def debug(self, message: str, context: Optional[Dict[str, Any]] = None, exc_info: Optional[bool] = False):
        """
        Log a debug message.
        :param message: The log message.
        :param context: Additional context as a dictionary (optional).
        :param exc_info: Whether to include exception information (optional).
        """
        self.logger.debug(self._format_message(message, context), exc_info=exc_info)

    def warning(self, message: str, context: Optional[Dict[str, Any]] = None, exc_info: Optional[bool] = False):
        """
        Log a warning message.
        :param message: The log message.
        :param context: Additional context as a dictionary (optional).
        :param exc_info: Whether to include exception information (optional).
        """
        self.logger.warning(self._format_message(message, context), exc_info=exc_info)

    def error(self, message: str, context: Optional[Dict[str, Any]] = None, exc_info: Optional[bool] = False):
        """
        Log an error message.
        :param message: The log message.
        :param context: Additional context as a dictionary (optional).
        :param exc_info: Whether to include exception information (optional).
        """
        self.logger.error(self._format_message(message, context), exc_info=exc_info)

    def critical(self, message: str, context: Optional[Dict[str, Any]] = None, exc_info: Optional[bool] = False):
        """
        Log a critical message.
        :param message: The log message.
        :param context: Additional context as a dictionary (optional).
        :param exc_info: Whether to include exception information (optional).
        """
        self.logger.critical(self._format_message(message, context), exc_info=exc_info)

    def exception(self, message: str, context: Optional[Dict[str, Any]] = None):
        """
        Log an exception with a traceback.
        :param message: The log message.
        :param context: Additional context as a dictionary (optional).
        """
        self.logger.error(
            self._format_message(message, context, traceback.format_exc()),
            exc_info=True
        )

    def _format_message(self, message: str, context: Optional[Dict[str, Any]] = None, exception_trace: Optional[str] = None) -> str:
        """
        Format the log message with additional context and exception details.
        :param message: The log message.
        :param context: Additional context as a dictionary (optional).
        :param exception_trace: Optional exception traceback.
        :return: Formatted log message as a string.
        """
        log_entry = {"message": message}

        if context:
            log_entry.update(context)
        if exception_trace:
            log_entry["exception"] = exception_trace

        return str(log_entry)
