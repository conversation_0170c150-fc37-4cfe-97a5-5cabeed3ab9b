from django.db import models, DatabaseError, IntegrityError
from typing import Type, List, Dict, Any, Optional


class RepositoryError(Exception):
    """Custom exception for repository errors."""
    def __init__(self, message: str):
        super().__init__(message)


class BaseRepository:
    def __init__(self, model: Type[models.Model]):
        """
        Initialize the repository with the given model.
        :param model: The Django model to operate on.
        """
        if not issubclass(model, models.Model):
            raise ValueError("The model must be a subclass of Django's Model.")
        self.model = model

    def get(self, **filters) -> Optional[models.Model]:
        """
        Retrieve a single object matching the filters.
        :param filters: Query filters.
        :return: Model instance or None.
        """
        try:
            return self.model.objects.filter(**filters).first()
        except DatabaseError as e:
            raise RepositoryError(f"Error retrieving data: {e}")

    def filter(self, **filters) -> models.QuerySet:
        """
        Retrieve all objects matching the filters.
        :param filters: Query filters.
        :return: QuerySet.
        """
        try:
            return self.model.objects.filter(**filters)
        except DatabaseError as e:
            raise RepositoryError(f"Error retrieving data: {e}")

    def get_all(self) -> models.QuerySet:
        """
        Retrieve all objects of the model.
        :return: QuerySet containing all objects.
        """
        try:
            return self.model.objects.all()
        except DatabaseError as e:
            raise RepositoryError(f"Error retrieving all data: {e}")

    def create(self, **data) -> models.Model:
        """
        Create a new object in the database.
        :param data: Fields for the new object.
        :return: The created object.
        """
        try:
            return self.model.objects.create(**data)
        except DatabaseError as e:
            raise RepositoryError(f"Error creating data: {e}")

    def get_or_create(self, **data) -> tuple[models.Model, bool]:
        """
        Retrieve an existing object or create a new one.

        :param data: Fields for searching or creating the object.
        :return: Tuple containing (model instance, created status).
        :raises RepositoryError: If a database error occurs.
        """
        try:
            instance, created = self.model.objects.get_or_create(**data)
            return instance, created
        except IntegrityError as e:
            raise RepositoryError(f"Integrity error while creating data: {e}")
        except DatabaseError as e:
            raise RepositoryError(f"Database error while creating data: {e}")

    def update(self, instance: models.Model, **data) -> models.Model:
        """
        Update an existing object.
        :param instance: The model instance to update.
        :param data: Fields to update.
        :return: The updated object.
        """
        try:
            for field, value in data.items():
                setattr(instance, field, value)
            instance.save()
            return instance
        except DatabaseError as e:
            raise RepositoryError(f"Error updating data: {e}")

    def delete(self, instance: models.Model) -> None:
        """
        Delete an object from the database.
        :param instance: The model instance to delete.
        """
        try:
            instance.delete()
        except DatabaseError as e:
            raise RepositoryError(f"Error deleting data: {e}")

    def bulk_create(self, objects: List[Dict[str, Any]]) -> List[models.Model]:
        """
        Bulk insert multiple objects into the database.
        :param objects: A list of dictionaries representing objects to insert.
        :return: List of created objects.
        """
        try:
            instances = [self.model(**obj) for obj in objects]
            return self.model.objects.bulk_create(instances)
        except DatabaseError as e:
            raise RepositoryError(f"Error in bulk create: {e}")

    def bulk_update(self, instances: List[models.Model], fields: List[str]) -> None:
        """
        Bulk update multiple objects in the database.
        :param instances: List of model instances to update.
        :param fields: List of fields to update on each instance.
        :return: None
        """
        try:
            self.model.objects.bulk_update(instances, fields)
        except DatabaseError as e:
            raise RepositoryError(f"Error in bulk update: {e}")

    def raw_query(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """
        Execute a raw SQL query.
        :param query: Raw SQL query string.
        :param params: Optional parameters for the query.
        :return: List of dictionaries representing query results.
        """
        try:
            with self.model.objects.raw(query, params=params) as cursor:
                return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]
        except DatabaseError as e:
            raise RepositoryError(f"Error executing raw query: {e}")
