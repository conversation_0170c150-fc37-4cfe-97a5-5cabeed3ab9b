# --------------------------------------------------------
# COMMON CONSTANTS FOR ALL APPS
# --------------------------------------------------------

from enum import Enum


# --------------------------------------------------------
# BASE ENUM CLASS
# --------------------------------------------------------

class BaseEnum(Enum):
    """
    A base Enum class with a method to generate choices 
    for Django model fields or APIs.
    """
    @classmethod
    def choices(cls):
        return [(key.value, key.name.replace("_", " ").title()) for key in cls]

# --------------------------------------------------------
# DEFAULT TEMPLATES
# --------------------------------------------------------

DEFAULT_TEMPLATES = {
    'email_verification': {
        'name': 'Email Verification',
        'code': 'email_verification',
        'subject': 'Verify Your Email',
        'content': (
            'Your verification code is: {{ otp }}\n'
            'Valid for {{ expiry }} minutes.'
        ),
    },
    'password_reset': {
        'name': 'Password Reset',
        'code': 'password_reset',
        'subject': 'Reset Your Password',
        'content': (
            'Your password reset code is: {{ otp }}\n'
            'Valid for {{ expiry }} minutes.'
        ),
    },
    'email_verification_link': {
        'name': 'Email Verification Link',
        'code': 'email_verification_link',
        'subject': 'Verify Your Email',
        'content': (
            'Click this link to verify your email:\n'
            '{{verification_url}}'
        ),
    },
}

# --------------------------------------------------------
# ENUM DEFINITIONS
# --------------------------------------------------------

# Authentication Types
class AuthenticationType(BaseEnum):
    EMAIL = "email"
    PHONE = "phone"
    SOCIAL = "social"

# Verification Types
class VerificationType(BaseEnum):
    EMAIL = "email"
    PHONE = "phone"
    PASSWORD = "password"

# Social Providers
class SocialProvider(BaseEnum):
    GOOGLE = "google"
    FACEBOOK = "facebook"

# Multi-Factor Authentication (MFA) Types
class MFAType(BaseEnum):
    TOTP = "totp"
    SMS = "sms"

# Token Types
class TokenType(BaseEnum):
    ACCESS = "access"
    REFRESH = "refresh"

# User Status Choices
class UserStatus(BaseEnum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    BLOCKED = "blocked"
    SUSPENDED = "suspended"
    PENDING = "pending"
    DEACTIVATED = "deactivated"
    ARCHIVED = "archived"
    UNVERIFIED = "unverified"
    VERIFIED = "verified"

class SubscriptionType(BaseEnum):
    FREE = "free"
    STARTER = "starter"
    REGULAR = "regular"
    PRO = "pro"

# Verification Token Types
class VerificationTokenType(BaseEnum):
    EMAIL_OTP = "email otp"
    PHONE_OTP = "phone otp"
    PASSWORD_RESET_OTP = "password reset otp"
    PASSWORD_RESET_LINK = "password reset link"

# Validity Periods
class ValidityPeriod(BaseEnum):
    OTP = 3  # Minutes
    LINK = 3  # Minutes

# Notification Log Status Choices
class StatusChoicesNotificationLog(BaseEnum):
    SENT = "sent"
    FAILED = "failed"
    PENDING = "pending"

# Payment Status Choices
class StatusChoicesPayment(BaseEnum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    REFUNDED = "refunded"
    CANCELLED = "cancelled"

# Refund Status Choices
class StatusChoicesRefund(BaseEnum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

# Payment Method Types
class PaymentMethodType(BaseEnum):
    CARD = "card"
    BANK = "bank"
    WALLET = "wallet"

# Constants for OTP and Password Length
OTP_LENGTH = 6
PASSWORD_LENGTH = 8

# URLs for verifying tokens from social providers
GOOGLE_TOKENINFO_URL = 'https://www.googleapis.com/oauth2/v3/tokeninfo' 
FACEBOOK_GRAPH_URL = 'https://graph.facebook.com/me'

# Languages
class Language(BaseEnum):
    ENGLISH = "en"
    SPANISH = "es"
    FRENCH = "fr"
    TURKISH = "tr"
    
# Element Group
class ElementGroup(BaseEnum):
    LANGUAGE = "language"
    GENDER = "gender"
    CURRENCY = "currency"
    
# File Type
class FileType(BaseEnum):
    PDF = "pdf"
    JPEG = "jpg"
    PNG = "png"
    DOC = "doc"
    OTHER = "other"
    
# --------------------------------------------------------
# CHOICES
# --------------------------------------------------------
# Time Formate
TIME_FORMATS = (
    ('hh:mm:ss A', 'HH:MM:SS AM/PM (e.g., 08:15:30 PM)'),
    ('hh:mm A', 'HH:MM AM/PM (e.g., 08:15 PM)'),
    ('HH:mm:ss', 'HH:MM:SS (24-hour format, e.g., 20:15:30)'),
    ('HH:mm', 'HH:MM (24-hour format, e.g., 20:15)'),
)

# Date formate
DATE_FORMATS = (
    ('MM/DD/YYYY', 'MM/DD/YYYY (e.g., 04/03/2024)'),
    ('DD.MM.YYYY', 'DD.MM.YYYY (e.g., 03.04.2024)'),
    ('DD/MM/YYYY', 'DD/MM/YYYY (e.g., 03/04/2024)'),
    ('MMM/DD/YYYY', 'MMM/DD/YYYY (e.g., Apr/03/2024)'),
    ('DD.MMM.YYYY', 'DD.MMM.YYYY (e.g., 03.Apr.2024)'),
    ('DD/MMM/YYYY', 'DD/MMM/YYYY (e.g., 03/Apr/2024)'),
)

# Date Time Formate
DATE_TIME_FORMATS = (
    ('MM/DD/YYYY hh:mm:ss A',
        'MM/DD/YYYY HH:MM:SS AM/PM (e.g., 04/03/2024 08:15:30 PM)'),
    ('MM/DD/YYYY hh:mm A',
        'MM/DD/YYYY HH:MM AM/PM (e.g., 04/03/2024 08:15 PM)'),
    ('DD.MM.YYYY HH:mm:ss', 
        'DD.MM.YYYY HH:MM:SS (24-hour format, e.g., 03.04.2024 20:15:30)'),
    ('DD.MM.YYYY HH:mm', 
        'DD.MM.YYYY HH:MM (24-hour format, e.g., 03.04.2024 20:15)'),
    ('DD.MM.YYYY hh:mm:ss A', 
        'DD.MM.YYYY HH:MM:SS AM/PM (12-hour format, e.g., 03.04.2024 08:15:30 PM)'),
    ('DD.MM.YYYY hh:mm A', 
        'DD.MM.YYYY HH:MM AM/PM (12-hour format, e.g., 03.04.2024 08:15 PM)'),
    ('DD/MM/YYYY HH:mm:ss', 'DD/MM/YYYY HH:MM:SS (e.g., 03/04/2024 20:15:30)'),
    ('DD/MM/YYYY HH:mm', 'DD/MM/YYYY HH:MM (e.g., 03/04/2024 20:15)'),
    ('MMM/DD/YYYY hh:mm:ss A',
        'MMM/DD/YYYY HH:MM:SS AM/PM (e.g., Apr/03/2024 08:15:30 PM)'),
    ('MMM/DD/YYYY hh:mm A',
        'MMM/DD/YYYY HH:MM AM/PM (e.g., Apr/03/2024 08:15 PM)'),
    ('DD.MMM.YYYY HH:mm:ss', 
        'DD.MMM.YYYY HH:MM:SS (24-hour format, e.g., 03.Apr.2024 20:15:30)'),
    ('DD.MMM.YYYY HH:mm', 
        'DD.MMM.YYYY HH:MM (24-hour format, e.g., 03.Apr.2024 20:15)'),
    ('DD/MMM/YYYY HH:mm:ss', 'DD/MMM/YYYY HH:MM:SS (e.g., 03/Apr/2024 20:15:30)'),
    ('DD/MMM/YYYY HH:mm', 'DD/MMM/YYYY HH:MM (e.g., 03/Apr/2024 20:15)'),
)

# --------------------------------------------------------
# CONSTANTS
# --------------------------------------------------------

UPLOAD_PATH = "uploads/%Y/%m/%d/"
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

PRIVACY_POLICY = 'privacy_policy'
ABOUT_US = 'about_us'
FAQ = 'faq'

SECTION_CHOICES = [
    (PRIVACY_POLICY, 'Privacy Policy'),
    (ABOUT_US, 'About Us'),
    (FAQ, 'FAQ'),
]

LICENSE_KEY='X-License-Key'

class RollChoice(BaseEnum):
    USER = 'user'
    ASSISTANT = 'assistant'

class PROVIDER(BaseEnum):
    GOOGLE_DRIVE = 'google_drive'
    ONEDRIVE = 'onedrive'
    DROPBOX = 'dropbox'

# Theme
class Theme(BaseEnum):
    LIGHT = 'light'
    DARK = 'dark'
