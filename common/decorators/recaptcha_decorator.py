
from functools import wraps
from apps.cross_cutting.recaptcha_service import ReCA<PERSON><PERSON><PERSON>Service
from rest_framework import status
from rest_framework.response import Response


def recaptcha(enabled=True):
    """
    Decorator to enforce ReCAPTCHA validation on a view method.

    :param enabled: Boolean flag to enable or disable ReCAPTCHA validation.
    :return: Decorated function that validates ReCAPTC<PERSON> before proceeding.
    """
    captcha_service = ReCAPTCHAService()

    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(self, request, *args, **kwargs):
            if enabled:
                captcha_token = request.headers.get("X-Captcha-Token")
                if not captcha_token:
                    return Response(
                        {"message": "Missing ReCAPTCHA token."},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                captcha_response = captcha_service.validate_token(captcha_token)
                if captcha_response.get("status") != "success":
                    return Response(
                        {"message": captcha_response.get("message")},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            return view_func(self, request, *args, **kwargs)

        return wrapped_view

    return decorator