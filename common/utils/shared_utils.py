import uuid
import random
import string
import babel
import os

from datetime import datetime
from django.utils.text import slugify
from common.constants import OTP_LENGTH


def generate_unique_id():
    """Generate a unique identifier."""
    return str(uuid.uuid4())


def format_datetime(dt):
    """Format a datetime object to a human-readable string."""
    if not isinstance(dt, datetime):
        return None
    return dt.strftime('%Y-%m-%d %H:%M:%S')


def generate_choices(choices_dict):
    """
    Convert a dictionary of constants into Django model choices.
    :param choices_dict: A dictionary where keys are internal values
    and values are human-readable labels.
    :return: A list of tuples suitable for Django model `choices`.
    """
    return [(key, value) for key, value in choices_dict.items()]

def currency_format(value, language):
    """
    Format a currency value to a human-readable string.
    :param value: The currency value to be formatted.
    :param language: The language to be used for formatting.
    :return: A human-readable string representing the formatted currency value.
    """
    currency = babel.numbers.format_currency(value, '', locale=language)
    return currency.strip()

def generate_slug(text):
    """
    Generate a URL-friendly slug from a string.
    :param text: The text to slugify.
    :return: A slugified version of the text.
    """
    return slugify(text)

def generate_otp(length=OTP_LENGTH):
    """
    Generates a random OTP code.
    """
    digits = string.digits
    otp = "".join(random.choice(digits) for _ in range(length))
    return otp

def generate_filename(extension: str = "") -> str:
    """Generate a unique filename using UUID"""
    filename = str(uuid.uuid4())
    return f"{filename}{extension}" if extension else filename

def get_file_extension(file):
    """Returns the file extension from a file object."""
    filename = file.name
    return os.path.splitext(filename)[1]

def get_file_path(instance, filename):
    """Generate a unique file path for a file."""
    return f"{instance.__class__.__name__.lower()}s/{generate_filename(get_file_extension(filename))}"
def str_to_bool(value: str) -> bool:
    """Convert a string to a boolean."""
    return value.lower() in ("true", "1", "y", "yes")
