from django.http import HttpResponse


def set_cookie(response: HttpResponse, key: str, value: str, max_age: int = None, 
               secure: bool = True, httponly: bool = True, samesite: str = 'Lax', 
               path: str = '/', domain: str = None):
    """
    Helper method to set a cookie on an HTTP response.

    Parameters:
        response (HttpResponse): The response object to which the cookie will be added.
        key (str): The name of the cookie.
        value (str): The value of the cookie.
        max_age (int, optional): The maximum age of the cookie in seconds. Defaults to None.
        secure (bool, optional): Ensures the cookie is sent only over HTTPS. Defaults to True.
        httponly (bool, optional): Prevents JavaScript access to the cookie. Defaults to True.
        samesite (str, optional): Controls whether the cookie is sent with cross-site requests. Defaults to 'Lax'.
                                   Options are 'Lax', 'Strict', or 'None'.
        path (str, optional): The path for which the cookie is valid. Defaults to '/'.
        domain (str, optional): The domain for which the cookie is valid. Defaults to None.

    Returns:
        None
    """
    response.set_cookie(
        key,
        value,
        max_age=max_age,
        secure=secure,
        httponly=httponly,
        samesite=samesite,
        path=path,
        domain=domain,
    )


def get_cookie(request, key: str) -> str:
    """
    Helper method to retrieve a cookie value from a request.

    Parameters:
        request (HttpRequest): The incoming HTTP request.
        key (str): The name of the cookie to retrieve.

    Returns:
        str: The value of the cookie, or None if the cookie does not exist.
    """
    return request.COOKIES.get(key)


def delete_cookie(response: HttpResponse, key: str, path: str = '/', domain: str = None):
    """
    Helper method to delete a cookie from an HTTP response.

    Parameters:
        response (HttpResponse): The response object from which the cookie will be removed.
        key (str): The name of the cookie to delete.
        path (str, optional): The path for which the cookie was set. Defaults to '/'.
        domain (str, optional): The domain for which the cookie was set. Defaults to None.

    Returns:
        None
    """
    response.delete_cookie(key, path=path, domain=domain)
