import json
from uuid import uuid4
from datetime import datetime
from typing import Dict, Any
from channels.generic.websocket import AsyncWebsocketConsumer
from common.logger import Logger

logger = Logger(__name__)

class BaseWebSocketConsumer(AsyncWebsocketConsumer):
    """
    A reusable base WebSocket consumer that can be extended in different apps.
    It ensures a standard response format and centralized logging.
    """

    async def connect(self):
        """Handles WebSocket connection."""
        self.scope["user"] = self.scope.get("user", None)
        await self.accept()
        logger.info("WebSocket connection established.", {"user": str(self.scope["user"])})

        await self.send_success("WebSocket connection established.", action="connection.ack")

    async def disconnect(self, close_code: int):
        """Handles WebSocket disconnection."""
        logger.info("WebSocket disconnected.", {"user": str(self.scope["user"]), "close_code": close_code})

    async def receive(self, text_data: str):
        """Handles incoming WebSocket messages."""
        try:
            data = json.loads(text_data)
            logger.info("WebSocket request received.", {"request": data})

            action = data.get("action")
            payload = data.get("payload", {})

            if not action:
                return await self.send_error("No action specified.")

            response = await self.route_action(action, payload)
            await self.send_json(response)

        except Exception as e:
            logger.error("Error processing WebSocket request.", {"error": str(e)})
            await self.send_error("An error occurred while processing your request.", details=str(e))

    async def route_action(self, action: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Routes actions to the appropriate method. This method should be overridden in child consumers."""
        return self.response_error(f"Unknown action: {action}", action=action)

    async def send_json(self, data: Dict[str, Any]):
        """Sends a JSON response over WebSocket."""
        await self.send(text_data=json.dumps(data))

    async def send_success(self, message: str, action: str = "success", data: Dict[str, Any] = None):
        """Sends a success response in a standardized format."""
        response = self.response_success(message, action, data)
        await self.send_json(response)

    async def send_error(self, message: str, action: str = "error", details: str = ""):
        """Sends an error response in a standardized format."""
        response = self.response_error(message, action, details)
        await self.send_json(response)

    def response_success(self, message: str, action: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generates a standardized success response."""
        return {
            "status": "success",
            "message": message,
            "action": action,
            "data": data or {},
            "meta": self._generate_meta(),
        }

    def response_error(self, message: str, action: str, details: str = "") -> Dict[str, Any]:
        """Generates a standardized error response."""
        return {
            "status": "error",
            "message": message,
            "action": action,
            "errors": {"details": details},
            "meta": self._generate_meta(),
        }

    def _generate_meta(self) -> Dict[str, Any]:
        """Generates metadata for responses."""
        return {
            "response_id": str(uuid4()),
            "timestamp": datetime.utcnow().isoformat() + "Z",
        }
