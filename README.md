# Devxhub Django Admin Boilerplate

A production-ready Django admin boilerplate with comprehensive Docker support and development tools. This project provides a robust foundation for building and deploying Django-based applications with automated workflows and environment management.

## 🛠 Features

- Multiple environment support (Development, Production, Local)
- Docker and Docker Compose integration
- Automated build and deployment workflows
- Health monitoring and dependency management
- Database backup and restore functionality
- Comprehensive logging and debugging tools

## 📋 Prerequisites

- [Docker](https://www.docker.com/get-started) (20.10.0 or higher)
- [Docker Compose](https://docs.docker.com/compose/install/) (2.0.0 or higher)
- Make (GNU Make 4.0 or higher)
  - macOS/Linux: Pre-installed
  - Windows: Install via [Chocolatey](https://chocolatey.org/): `choco install make`

## 📂 Project Structure

```
.
├── infra/
│   ├── compose/                  # Docker Compose configurations
│   │   ├── compose.dev.yml      # Development environment
│   │   ├── compose.prod.yml     # Production environment
│   │   └── compose.local.yml    # Local production-like environment
│   │
│   └── docker/                  # Dockerfile definitions
│       ├── Dockerfile.dev       # Development build
│       ├── Dockerfile.prod      # Production build
│       └── Dockerfile.local     # Local production-like build
│
├── .env                         # Environment variables
├── .env.example                 # Environment template
├── Makefile                     # Automation commands
└── README.md                    # Project documentation
```

## 🚀 Getting Started

### Initial Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/devxhub/pioo-backend.git
   cd pioo-backend
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

### Environment Management

#### Development Environment
```bash
make dev           # Start development environment
make stop-dev      # Stop development services
make clean-dev     # Clean development environment
make restart-dev   # Restart development services
```

#### Production Environment
```bash
make prod          # Start production environment
make stop-prod     # Stop production services
make clean-prod    # Clean production environment
make restart-prod  # Restart production services
```

#### Local Production-like Environment
```bash
make local         # Start local production-like environment
make stop-local    # Stop local services
make clean-local   # Clean local environment
make restart-local # Restart local services
```

### Docker Image Management

#### Building Images
```bash
make build         # Build production image
make build-local   # Build local production-like image
make rebuild       # Rebuild development images (no cache)
```

#### Publishing Images
```bash
make push          # Build & push production image
make push-local    # Build & push local image
```

#### Pulling Images
```bash
make pull          # Pull production image
make pull-local    # Pull local image
```

### Maintenance and Monitoring

#### Health Checks
```bash
make health-check-dev    # Check development services health
make health-check-prod   # Check production services health
make health-check-local  # Check local services health
```

#### Dependencies
```bash
make deps-check    # Check for outdated dependencies
make deps-update   # Update project dependencies
```

#### Database Management
```bash
make backup        # Create database backup
make restore       # Restore database from backup
```

#### Logging and Debugging
```bash
make logs          # View logs (interactive environment selection)
make ps            # Show running containers
make shell         # Open Django shell in container
```

## 🔍 Common Tasks

### Viewing Available Commands
```bash
make help          # Show all available commands with descriptions
```

### Stopping All Services
```bash
make stop          # Stop all environments
```

### Complete Cleanup
```bash
make clean         # Clean all environments
```

## 🎯 Development Best Practices

1. **Environment Management**
   - Always use the appropriate environment for your task
   - Development: `make dev` for active development
   - Production: `make prod` for deployment
   - Local: `make local` for production-like testing

2. **Health Monitoring**
   - Regularly check service health with `make health-check-*`
   - Monitor logs with `make logs`

3. **Database Operations**
   - Create regular backups with `make backup`
   - Test restore functionality in development

4. **Dependency Management**
   - Check for updates with `make deps-check`
   - Update carefully with `make deps-update`

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 🆘 Support

If you encounter any issues or need assistance:
1. Check the documentation
2. Run `make help` for command information
3. Open an issue in the repository
4. Contact the maintainers

## Stop and Clear Docker

```base
docker stop $(docker ps -aq)
docker rm $(docker ps -aq)
docker rmi $(docker images -q) -f
docker volume rm $(docker volume ls -q)
docker network prune -f
docker builder prune -a -f
docker system prune -a --volumes -f && docker builder prune -a -f
```

tree -I "migrations|__pycache__|fonts|tests|collectstatic|templates|static"
find . -type d -name "__pycache__" -exec rm -r {} +

find . -path "*/migrations/*.py" -not -name "__init__.py" -delete
find . -path "*/migrations/*.pyc" -delete

## Load Data

```base
make shell
python manage.py superuser
python manage.py company
python manage.py load_countries
python manage.py load_timezones
python manage.py add_notification_types
python manage.py add_notification_templates
python manage.py populate_llm_models
python manage.py create_plans

python manage.py create_default_templates
python manage.py create_preferences

python manage.py loaddata apps/core/fixtures/core.json
python manage.py loaddata apps/file_manager/fixtures/file_manager.json
python manage.py loaddata apps/identity/fixtures/identity.json
python manage.py loaddata apps/cms/fixtures/cms.json
python manage.py loaddata apps/miscellaneous/fixtures/miscellaneous.json
python manage.py loaddata apps/notification/fixtures/notification.json
python manage.py loaddata apps/payment/fixtures/payment.json
python manage.py loaddata apps/user/fixtures/user.json
python manage.py loaddata apps/setting/fixtures/setting.json

```

## translate language .po file
```base
make shell
python locale/translate.py
Enter the path to the .po file: locale/fr/LC_MESSAGES/django.po
Enter the target language code (e.g., fr, es, tr): fr
```



# Core
# Plug and Play


```base
python create_package.py dxh_payments paypal stripe subscription
python create_app_structure.py demo company
```
