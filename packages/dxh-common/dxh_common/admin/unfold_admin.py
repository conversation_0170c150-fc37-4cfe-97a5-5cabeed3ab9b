from typing import List
from django import forms
from django.db import models
from dxh_libraries.unfold import (
    ModelAdmin,
    UserChangeForm,
    UserCreationForm,
    UnfoldAdminSelectWidget,
)
from dxh_libraries.allauth import SocialApp, providers

class BaseAdmin(ModelAdmin):
    """
    A base admin class to serve as a common configuration
    for all admin classes in the application.
    """

    # Future customizations for admin classes can be added here.
    base_fields = ('created_by', 'updated_by', 'deleted_by', 'deleted_at', 'is_deleted', 'is_active')
    list_per_page = 100   # Default number of items per page in list view

    def get_fieldsets(self, request, obj=None):
        """
        Merge the child model's fieldsets with BaseAdmin fields.
        Ensures that base fields always appear at the bottom.
        """
        excluding_apps = ['easyaudit']

        if self.model._meta.app_label in excluding_apps:
            return super().get_fieldsets(request, obj)
            
        fields = self.model._meta.get_fields()
        editable_fields = [
            field.name for field in fields
            if field.editable and field.name != 'id' and field.name not in self.__class__.base_fields
        ]

        valid_base_fields = [field for field in self.__class__.base_fields if hasattr(self.model, field)]

        default_fieldsets = ((None, {"fields": editable_fields}),)

        try:
            if hasattr(self, "fieldsets") and self.fieldsets:
                return tuple(self.fieldsets) + ((None, {"fields": valid_base_fields}),)

            return default_fieldsets + ((None, {"fields": valid_base_fields}),)

        except Exception:
            return default_fieldsets
        
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)

        relational_fields = [
            field.name
            for field in self.model._meta.get_fields()
            if isinstance(field, (models.ForeignKey, models.OneToOneField))
        ]

        # Apply `select_related` to avoid multiple queries
        return qs.select_related(*relational_fields)
    
    def get_autocomplete_fields(self, request):
        return [
            field.name
            for field in self.model._meta.get_fields()
            if isinstance(field, (models.ForeignKey, models.OneToOneField))
        ]

class UserAdminChangeForm(UserChangeForm):
    """
    A base form class to serve as a common configuration
    for all user admin forms in the application.
    """

    # Future customizations for user admin forms can be added here.
    pass


class UserAdminCreationForm(UserCreationForm):
    """
    A base form class to serve as a common configuration
    for all user creation forms in the application.
    """

    # Future customizations for user creation forms can be added here.
    pass



class SocialAppForm(forms.ModelForm):
    class Meta:
        model = SocialApp
        exclude: List[str] = []

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["provider"] = forms.ChoiceField(
            choices=providers.registry.as_choices(),
            widget=UnfoldAdminSelectWidget(),  
        )