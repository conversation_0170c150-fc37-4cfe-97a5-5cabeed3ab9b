import environ
from typing import Type

from dxh_common.admin.unfold_admin import (
    BaseAdmin as UnfoldBaseAdmin,
    UserAdminChangeForm as UnfoldUserAdminChangeForm,
    UserAdminCreationForm as UnfoldUserAdminCreationForm,
    SocialAppForm as UnfoldSocialAppForm,   
)
from dxh_common.admin.base_admin import (
    BaseAdmin as ModelBaseAdmin,
    UserAdminChangeForm as ModelUserAdminChangeForm,
    UserAdminCreationForm as ModelUserAdminCreationForm,
    SocialAppForm as ModelSocialAppForm,
)

env = environ.Env()

ENABLE_UNFOLD = env.bool("ENABLE_UNFOLD", default=False)

BaseAdmin: Type[ModelBaseAdmin]
UserAdminChangeForm: Type[ModelUserAdminChangeForm]
UserAdminCreationForm: Type[ModelUserAdminCreationForm]

if ENABLE_UNFOLD:
    BaseAdmin = UnfoldBaseAdmin
    UserAdminChangeForm = UnfoldUserAdminChangeForm
    UserAdminCreationForm = UnfoldUserAdminCreationForm
    SocialAppForm = UnfoldSocialAppForm
else:
    BaseAdmin = ModelBaseAdmin
    UserAdminChangeForm = ModelUserAdminChangeForm
    UserAdminCreationForm = ModelUserAdminCreationForm
    SocialAppForm = ModelSocialAppForm
