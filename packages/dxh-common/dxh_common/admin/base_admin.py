from django.contrib import admin
from django.db import models
from django.contrib.auth import forms as admin_forms
from django.contrib.auth import get_user_model
from dxh_libraries.allauth import SocialAppForm as AllauthSocialAppForm

User = get_user_model()


class BaseAdmin(admin.ModelAdmin):
    """
    A reusable base admin class for shared configurations across admin models.
    Override or extend as needed for consistency.
    """

    # Future customizations for admin classes can be added here.
    base_fields = ('created_by', 'updated_by', 'deleted_by', 'deleted_at', 'is_deleted', 'is_active')
    list_per_page = 100 
    
    def get_fieldsets(self, request, obj=None):
        """
        Merge the child model's fieldsets with BaseAdmin fields.
        Ensures that base fields always appear at the bottom.
        """
        excluding_apps = ['easyaudit']

        if self.model._meta.app_label in excluding_apps:
            return super().get_fieldsets(request, obj)
            
        fields = self.model._meta.get_fields()
        editable_fields = [
            field.name for field in fields
            if field.editable and field.name != 'id' and field.name not in self.__class__.base_fields
        ]

        valid_base_fields = [field for field in self.__class__.base_fields if hasattr(self.model, field)]

        default_fieldsets = ((None, {"fields": editable_fields}),)

        try:
            if hasattr(self, "fieldsets") and self.fieldsets:
                return tuple(self.fieldsets) + ((None, {"fields": valid_base_fields}),)

            return default_fieldsets + ((None, {"fields": valid_base_fields}),)

        except Exception:
            return default_fieldsets
         
    def get_queryset(self, request):
        qs = super().get_queryset(request)

        relational_fields = [
            field.name
            for field in self.model._meta.get_fields()
            if isinstance(field, (models.ForeignKey, models.OneToOneField))
        ]

        # Apply `select_related` to avoid multiple queries
        return qs.select_related(*relational_fields)
    
    def get_autocomplete_fields(self, request):
        return [
            field.name
            for field in self.model._meta.get_fields()
            if isinstance(field, (models.ForeignKey, models.OneToOneField))
        ]

class UserAdminChangeForm(admin_forms.UserChangeForm):
    class Meta:
        model = User
        fields = '__all__'


class UserAdminCreationForm(admin_forms.UserCreationForm):
    """
    Form for User Creation in the Admin Area.
    To change user signup, see UserSignupForm and UserSocialSignupForm.
    """

    class Meta:
        model = User
        fields = '__all__'
        error_messages = {
            "username": {"unique": "This username has already been taken."},
        }



class SocialAppForm(AllauthSocialAppForm):
    """
    Form for SocialApp in the Admin Area.
    """
    pass 