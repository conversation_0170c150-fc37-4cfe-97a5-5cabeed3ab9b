from enum import Enum
from typing import List, Tuple


# --------------------------------------------------------
# BASE ENUM CLASS
# --------------------------------------------------------

class BaseEnum(Enum):
    """
    A base Enum class with a method to generate choices
    for Django model fields or APIs.
    """

    @classmethod
    def choices(cls) -> List[Tuple[str, str]]:
        return [(key.value, key.name.replace("_", " ").title()) for key in cls]


# --------------------------------------------------------
# ENUM DEFINITIONS
# --------------------------------------------------------

class AuthenticationType(BaseEnum):
    EMAIL = "email"
    PHONE = "phone"
    SOCIAL = "social"


class VerificationType(BaseEnum):
    EMAIL = "email"
    PHONE = "phone"
    PASSWORD = "password"


class SocialProvider(BaseEnum):
    GOOGLE = "google"
    FACEBOOK = "facebook"


class MFAType(BaseEnum):
    TOTP = "totp"
    SMS = "sms"


class TokenType(BaseEnum):
    ACCESS = "access"
    REFRESH = "refresh"


class UserStatus(BaseEnum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    BLOCKED = "blocked"
    SUSPENDED = "suspended"
    PENDING = "pending"
    DEACTIVATED = "deactivated"
    ARCHIVED = "archived"
    UNVERIFIED = "unverified"
    VERIFIED = "verified"


class VerificationCodeType(BaseEnum):
    EMAIL_OTP = "email_otp"
    PHONE_OTP = "phone_otp"
    PASSWORD_RESET_OTP = "password_reset_otp"
    PASSWORD_RESET_LINK = "password_reset_link"

class VerificationTokenType(BaseEnum):
    PASSWORD_RESET = "password_reset"
    ACCOUNT_RECOVERY = "account_recovery"

class ValidityPeriod(BaseEnum):
    OTP = 10  # Minutes
    LINK = 60  # Minutes


class StatusChoicesNotificationLog(BaseEnum):
    SENT = "sent"
    FAILED = "failed"
    PENDING = "pending"


class StatusChoicesPayment(BaseEnum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    REFUNDED = "refunded"
    CANCELLED = "cancelled"


class StatusChoicesRefund(BaseEnum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class PaymentMethodType(BaseEnum):
    CARD = "card"
    BANK = "bank"
    WALLET = "wallet"


class Language(BaseEnum):
    ENGLISH = "en"
    SPANISH = "es"
    FRENCH = "fr"
    TURKISH = "tr"


class ElementGroup(BaseEnum):
    LANGUAGE = "language"
    GENDER = "gender"
    CURRENCY = "currency"


class FileType(BaseEnum):
    PDF = "pdf"
    JPEG = "jpg"
    PNG = "png"
    DOC = "doc"
    OTHER = "other"
