import os
import uuid
import babel
import random
import string
from ipware import get_client_ip as ipware_get_client_ip
from datetime import datetime
from django.utils.text import slugify
from dxh_common.utils.constants import OTP_LENGTH


def generate_unique_id():
    return str(uuid.uuid4())


def format_datetime(dt):
    if not isinstance(dt, datetime):
        return None
    return dt.strftime('%Y-%m-%d %H:%M:%S')


def generate_choices(choices_dict):
    return [(key, value) for key, value in choices_dict.items()]


def currency_format(value, language):
    currency = babel.numbers.format_currency(value, '', locale=language)
    return currency.strip()


def generate_slug(text):
    return slugify(text)


def generate_otp(length=OTP_LENGTH):
    digits = string.digits
    otp = "".join(random.choice(digits) for _ in range(length))
    return otp


def generate_filename(extension: str = "") -> str:
    filename = str(uuid.uuid4())
    return f"{filename}{extension}" if extension else filename


def get_file_extension(file):
    filename = file.name
    return os.path.splitext(filename)[1]


def get_file_path(instance, filename):
    return f"{instance.__class__.__name__.lower()}s/{generate_filename(get_file_extension(filename))}"


def str_to_bool(value: str | None) -> bool:
    if value is None:
        return False
    return value.strip().lower() in {
        "true",
        "1",
        "y",
        "yes",
        "on",
        "t",
        "enable",
    }


def get_client_ip(request):
    client_ip, is_routable = ipware_get_client_ip(request)
    if client_ip:
        return client_ip.strip()

    return None