from typing import Optional, Literal
from django.http import HttpResponse, HttpRequest


def set_cookie(
    response: HttpResponse,
    key: str,
    value: str,
    max_age: Optional[int] = None,
    secure: bool = True,
    httponly: bool = True,
    samesite: Optional[Literal["Lax", "Strict", "None", False]] = "Lax",
    path: str = "/",
    domain: Optional[str] = None,
) -> None:
    response.set_cookie(
        key,
        value,
        max_age=max_age,
        secure=secure,
        httponly=httponly,
        samesite=samesite,
        path=path,
        domain=domain,
    )

def get_cookie(request: HttpRequest, key: str) -> Optional[str]:
    return request.COOKIES.get(key)

def delete_cookie(
    response: HttpResponse,
    key: str,
    path: str = "/",
    domain: Optional[str] = None,
) -> None:
    response.delete_cookie(key, path=path, domain=domain)
