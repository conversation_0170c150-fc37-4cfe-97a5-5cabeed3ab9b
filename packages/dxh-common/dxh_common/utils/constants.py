# --------------------------------------------------------
# COMMON CONSTANTS FOR ALL APPS
# --------------------------------------------------------

from enum import Enum
from typing import List, Tuple


# --------------------------------------------------------
# DEFAULT TEMPLATES
# --------------------------------------------------------

DEFAULT_TEMPLATES = {
    'email_verification': {
        'name': 'Email Verification',
        'code': 'email_verification',
        'subject': 'Verify Your Email',
        'content': (
            'Your verification code is: {{ otp }}\n'
            'Valid for {{ expiry }} minutes.'
        ),
    },
    'password_reset': {
        'name': 'Password Reset',
        'code': 'password_reset',
        'subject': 'Reset Your Password',
        'content': (
            'Your password reset code is: {{ otp }}\n'
            'Valid for {{ expiry }} minutes.'
        ),
    },
    'email_verification_link': {
        'name': 'Email Verification Link',
        'code': 'email_verification_link',
        'subject': 'Verify Your Email',
        'content': (
            'Click this link to verify your email:\n' '{{verification_url}}'
        ),
    },
}

# --------------------------------------------------------
# CHOICES
# --------------------------------------------------------

TIME_FORMATS = (
    ('hh:mm:ss A', 'HH:MM:SS AM/PM (e.g., 08:15:30 PM)'),
    ('hh:mm A', 'HH:MM AM/PM (e.g., 08:15 PM)'),
    ('HH:mm:ss', 'HH:MM:SS (24-hour format, e.g., 20:15:30)'),
    ('HH:mm', 'HH:MM (24-hour format, e.g., 20:15)'),
)

DATE_FORMATS = (
    ('MM/DD/YYYY', 'MM/DD/YYYY (e.g., 04/03/2024)'),
    ('DD.MM.YYYY', 'DD.MM.YYYY (e.g., 03.04.2024)'),
    ('DD/MM/YYYY', 'DD/MM/YYYY (e.g., 03/04/2024)'),
    ('MMM/DD/YYYY', 'MMM/DD/YYYY (e.g., Apr/03/2024)'),
    ('DD.MMM.YYYY', 'DD.MMM.YYYY (e.g., 03.Apr.2024)'),
    ('DD/MMM/YYYY', 'DD/MMM/YYYY (e.g., 03/Apr/2024)'),
)

DATE_TIME_FORMATS = (
    (
        'MM/DD/YYYY hh:mm:ss A',
        'MM/DD/YYYY HH:MM:SS AM/PM (e.g., 04/03/2024 08:15:30 PM)',
    ),
    (
        'MM/DD/YYYY hh:mm A',
        'MM/DD/YYYY HH:MM AM/PM (e.g., 04/03/2024 08:15 PM)',
    ),
    (
        'DD.MM.YYYY HH:mm:ss',
        'DD.MM.YYYY HH:MM:SS (24-hour format, e.g., 03.04.2024 20:15:30)',
    ),
    (
        'DD.MM.YYYY HH:mm',
        'DD.MM.YYYY HH:MM (24-hour format, e.g., 03.04.2024 20:15)',
    ),
    (
        'DD.MM.YYYY hh:mm:ss A',
        'DD.MM.YYYY HH:MM:SS AM/PM (12-hour format, e.g., 03.04.2024 08:15:30 PM)',
    ),
    (
        'DD.MM.YYYY hh:mm A',
        'DD.MM.YYYY HH:MM AM/PM (12-hour format, e.g., 03.04.2024 08:15 PM)',
    ),
    ('DD/MM/YYYY HH:mm:ss', 'DD/MM/YYYY HH:MM:SS (e.g., 03/04/2024 20:15:30)'),
    ('DD/MM/YYYY HH:mm', 'DD/MM/YYYY HH:MM (e.g., 03/04/2024 20:15)'),
    (
        'MMM/DD/YYYY hh:mm:ss A',
        'MMM/DD/YYYY HH:MM:SS AM/PM (e.g., Apr/03/2024 08:15:30 PM)',
    ),
    (
        'MMM/DD/YYYY hh:mm A',
        'MMM/DD/YYYY HH:MM AM/PM (e.g., Apr/03/2024 08:15 PM)',
    ),
    (
        'DD.MMM.YYYY HH:mm:ss',
        'DD.MMM.YYYY HH:MM:SS (24-hour format, e.g., 03.Apr.2024 20:15:30)',
    ),
    (
        'DD.MMM.YYYY HH:mm',
        'DD.MMM.YYYY HH:MM (24-hour format, e.g., 03.Apr.2024 20:15)',
    ),
    (
        'DD/MMM/YYYY HH:mm:ss',
        'DD/MMM/YYYY HH:MM:SS (e.g., 03/Apr/2024 20:15:30)',
    ),
    ('DD/MMM/YYYY HH:mm', 'DD/MMM/YYYY HH:MM (e.g., 03/Apr/2024 20:15)'),
)

DEVICE_TYPES = (
    ("ios", "ios"), 
    ("android", "android"), 
    ("web", "web")
)

PRIVACY_POLICY = 'privacy_policy'
ABOUT_US = 'about_us'
FAQ = 'faq'
TERMS_CONDITION = 'terms_condition'

SECTION_CHOICES = [
    (PRIVACY_POLICY, 'Privacy Policy'),
    (ABOUT_US, 'About Us'),
    (ABOUT_US, 'About Us'),
    (TERMS_CONDITION, 'Terms_Condition'),
    (FAQ, 'FAQ'),
]

# --------------------------------------------------------
# CONSTANTS
# --------------------------------------------------------

OTP_LENGTH = 6
PASSWORD_LENGTH = 8

GOOGLE_TOKENINFO_URL = 'https://www.googleapis.com/oauth2/v3/tokeninfo'
FACEBOOK_GRAPH_URL = 'https://graph.facebook.com/me'

UPLOAD_PATH = "uploads/%Y/%m/%d/"
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

LICENSE_KEY = 'X-License-Key'
