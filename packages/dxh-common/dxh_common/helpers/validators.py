import re
import uuid
import datetime
from django.core.exceptions import ValidationError
from django.core.validators import EmailValidator, URLValidator
from dxh_common.utils.constants import PASSWORD_LENGTH
from apps.core.models.system_setting import SystemSetting


def validate_positive(value):
    try:
        if not isinstance(value, (int, float)):
            raise ValidationError(f"{value} is not a number.")
        if value <= 0:
            raise ValidationError(f"{value} is not a positive number.")
    except Exception as e:
        raise ValidationError(str(e))


def validate_email(value):
    try:
        validator = EmailValidator(
            message=f"{value} is not a valid email address.")
        validator(value)
    except Exception as e:
        raise ValidationError(str(e))


def validate_password_strength(password):
    try:
        system_setting = SystemSetting.objects.first()

        if not password or not isinstance(password, str):
            raise ValidationError("Password must be a valid string.")

        if len(password) < PASSWORD_LENGTH:
            raise ValidationError(
                f"Password must be at least {PASSWORD_LENGTH} characters long.")

        if system_setting and system_setting.password_format:
            if 'uppercase' in system_setting.password_format and not re.search(r'[A-Z]', password):
                raise ValidationError(
                    "Password must contain at least one uppercase letter.")
            if 'lowercase' in system_setting.password_format and not re.search(r'[a-z]', password):
                raise ValidationError(
                    "Password must contain at least one lowercase letter.")
            if 'number' in system_setting.password_format and not re.search(r'[0-9]', password):
                raise ValidationError(
                    "Password must contain at least one number.")
            if 'special' in system_setting.password_format and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
                raise ValidationError(
                    "Password must contain at least one special character.")
    except Exception as e:
        raise ValidationError(str(e))


def validate_password(password: str, confirm_password: str) -> bool:
    try:
        if not isinstance(password, str) or not isinstance(confirm_password, str):
            raise ValidationError("Password must be a string.")

        if not password.strip() or not confirm_password.strip():
            raise ValidationError("Password cannot be empty.")

        return password == confirm_password
    except Exception as e:
        raise ValidationError(str(e))


def is_valid_url(url: str) -> bool:
    try:
        validator = URLValidator()
        validator(url)

        return True
    except ValidationError:
        return False
    except Exception as e:
        raise ValidationError(str(e))


def is_valid_uuid(value: str) -> bool:
    try:
        if not isinstance(value, str):
            return False

        uuid_obj = uuid.UUID(value, version=4)
        return str(uuid_obj).lower() == value.lower()
    except (ValueError, TypeError):
        return False
    except Exception as e:
        raise ValidationError(str(e))


def is_valid_date(date_str: str) -> bool:
    try:
        if not isinstance(date_str, str) or not date_str.strip():
            return False

        datetime.datetime.strptime(date_str, "%Y-%m-%d")
        return True
    except ValueError:
        return False
    except Exception as e:
        raise ValidationError(str(e))


def validate_integer(value):
    try:
        if not isinstance(value, int):
            raise ValidationError("Value must be an integer.")
    except Exception as e:
        raise ValidationError(str(e))


def validate_non_empty_string(value):
    try:
        if not isinstance(value, str) or not value.strip():
            raise ValidationError("Value must be a non-empty string.")
    except Exception as e:
        raise ValidationError(str(e))


def validate_boolean(value):
    try:
        if not isinstance(value, bool):
            raise ValidationError("Value must be a boolean.")
    except Exception as e:
        raise ValidationError(str(e))


def validate_list(value):
    try:
        if not isinstance(value, list):
            raise ValidationError("Value must be a list.")
    except Exception as e:
        raise ValidationError(str(e))
