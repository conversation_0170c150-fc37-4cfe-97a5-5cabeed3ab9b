from dxh_libraries.rest_framework import Response, pagination
from dxh_common.logger import Logger
from apps.core.services import SystemSettingService

logger = Logger(__name__)
MAX_PAGE_SIZE = 100


class CustomPagination(pagination.PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'

    def get_page_size(self, request):
        query_page_size = request.query_params.get(self.page_size_query_param)

        if query_page_size and query_page_size.isdigit():
            page_size = int(query_page_size)

            if page_size > MAX_PAGE_SIZE:
                return MAX_PAGE_SIZE
            return page_size

        user = request.user
        if user and user.company:
            try:
                system_setting_service = SystemSettingService()
                system_setting = system_setting_service.get(
                    company=user.company)
                return int(system_setting.pagination_size)
            except Exception as e:
                logger.error(
                    "Failed to fetch pagination size from SystemSettingService: %s", str(
                        e)
                )
        return self.page_size

    def get_paginated_response(self, data):
        return Response({
            'records': data,
            'pagination': {
                'current_page': self.page.number,
                'num_pages': self.page.paginator.num_pages,
                'page_size': self.get_page_size(self.request),
                'total_count': self.page.paginator.count,
            },
        })
