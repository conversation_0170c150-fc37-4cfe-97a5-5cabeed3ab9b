from dxh_libraries.rest_framework import APIView
from dxh_libraries.rest_framework_simplejwt import TokenObtainPairView
from dxh_common.permissions import IsAuthenticated, AllowAny
from dxh_common.throttles import UserRateThrottle


class BaseApiView(APIView):
    permission_classes = [IsAuthenticated]


class PublicApiView(TokenObtainPairView):
    permission_classes = [AllowAny]
    throttle_classes = [UserRateThrottle]
