from dxh_common.base.base_repository import RepositoryError


class ServiceError(Exception):
    """Custom exception for service layer errors."""

    pass


class BaseService:
    def __init__(self, repository):
        """
        Initialize the service with a repository.
        :param repository: The repository instance to interact with the database.
        """
        if not repository:
            raise ValueError("A valid repository must be provided.")
        self.repository = repository

    def get(self, **filters):
        """
        Retrieve a single object matching the filters.
        :param filters: Query filters.
        :return: Object or None.
        """
        try:
            return self.repository.get(**filters)
        except RepositoryError as e:
            raise ServiceError(f"Service error during get operation: {e}")

    def list(self, **filters):
        """
        Retrieve all objects matching the filters.
        :param filters: Query filters.
        :return: QuerySet.
        """
        try:
            return self.repository.filter(**filters)
        except RepositoryError as e:
            raise ServiceError(f"Service error during list operation: {e}")

    def get_all(self):
        """
        Retrieve all objects in the repository.
        :return: QuerySet of all objects.
        """
        try:
            return self.repository.get_all()
        except RepositoryError as e:
            raise ServiceError(f"Service error during get_all operation: {e}")

    def create(self, **data):
        """
        Create a new object in the database.
        :param data: Fields for the new object.
        :return: The created object.
        """
        try:
            return self.repository.create(**data)
        except RepositoryError as e:
            raise ServiceError(f"Service error during create operation: {e}")

    def update(self, instance, **data):
        """
        Update an existing object.
        :param instance: The object to update.
        :param data: Fields to update.
        :return: Updated object.
        """
        try:
            return self.repository.update(instance, **data)
        except RepositoryError as e:
            raise ServiceError(f"Service error during update operation: {e}")

    def delete(self, instance):
        """
        Delete an object from the database.
        :param instance: The object to delete.
        """
        try:
            self.repository.delete(instance)
        except RepositoryError as e:
            raise ServiceError(f"Service error during delete operation: {e}")

    def bulk_create(self, objects):
        """
        Bulk create multiple objects in the database.
        :param objects: A list of dictionaries representing objects to create.
        :return: List of created objects.
        """
        try:
            return self.repository.bulk_create(objects)
        except RepositoryError as e:
            raise ServiceError(
                f"Service error during bulk create operation: {e}"
            )

    def bulk_update(self, instances, fields):
        """
        Bulk update multiple objects in the database.
        :param instances: List of model instances to update.
        :param fields: List of fields to update.
        :return: None.
        """
        try:
            self.repository.bulk_update(instances, fields)
        except RepositoryError as e:
            raise ServiceError(
                f"Service error during bulk update operation: {e}"
            )
