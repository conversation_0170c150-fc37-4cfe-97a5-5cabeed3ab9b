from datetime import datetime
from dxh_libraries.rest_framework import Response, status, exception_handler
from dxh_common.logger import Logger
from dxh_common.base.base_repository import RepositoryError
from dxh_common.base.base_service import ServiceError

logger = Logger(__name__)


def custom_exception_handler(exc, context):
    # Call DRF's default exception handler to handle specific exceptions
    response = exception_handler(exc, context)

    # Extract the request path from the context
    path = context['request'].path if 'request' in context else None
    timestamp = datetime.utcnow().isoformat()

    if response is not None:
        # Log DRF-specific exceptions with context and status code
        logger.error(
            "DRF Exception occurred",
            context={
                "exception": str(exc),
                "context": context,
                "status_code": response.status_code,
                "errors": response.data,
            },
            exc_info=True,  # Include traceback if available
        )
        # Return formatted response for DRF exceptions
        return Response(
            {
                "path": path,
                "timestamp": timestamp,
                "status": "fail",
                "message": response.data.get("detail", "A validation error occurred."),
                "data": None,
                "errors": response.data,
            },
            status=response.status_code,
        )

    # Handle custom exceptions
    if isinstance(exc, RepositoryError):
        logger.error(
            "Repository Error occurred",
            context={
                "exception": str(exc),
                "context": context,
            },
            exc_info=True,  # Include traceback for debugging
        )
        return Response(
            {
                "path": path,
                "timestamp": timestamp,
                "status": "error",
                "message": "An unexpected error occurred in repository layer. Please try again later.",
                "data": None,
                "errors": str(exc),
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

    if isinstance(exc, ServiceError):
        logger.error(
            "Service Error occurred",
            context={
                "exception": str(exc),
                "context": context,
            },
            exc_info=True,  # Include traceback for debugging
        )
        return Response(
            {
                "path": path,
                "timestamp": timestamp,
                "status": "error",
                "message": "An unexpected error occurred in service layer. Please try again later.",
                "data": None,
                "errors": str(exc),
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

    # Log and handle unexpected exceptions
    logger.critical(
        "Unhandled Exception occurred",
        context={
            "exception": str(exc),
            "context": context,
        },
        exc_info=True,
    )
    return Response(
        {
            "path": path,
            "timestamp": timestamp,
            "status": "error",
            "message": "An unexpected error occurred. Please try again later.",
            "data": None,
            "errors": str(exc),
        },
        status=status.HTTP_500_INTERNAL_SERVER_ERROR,
    )
