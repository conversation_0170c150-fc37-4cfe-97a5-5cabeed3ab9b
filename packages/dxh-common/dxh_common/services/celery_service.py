from dxh_libraries.celery import shared_task
from celery.utils.log import get_task_logger

logger = get_task_logger(__name__)


def logged_task(*task_args, **task_kwargs):
    def decorator(func):
        @shared_task(*task_args, **task_kwargs)
        def wrapper(*args, **kwargs):
            logger.info(
                f"Task {func.__name__} started with args: {args} and kwargs: {kwargs}"
            )
            result = func(*args, **kwargs)
            logger.info(
                f"Task {func.__name__} completed with result: {result}"
            )
            return result

        return wrapper

    return decorator
