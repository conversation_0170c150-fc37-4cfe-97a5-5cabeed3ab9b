import requests
from django.conf import settings
from dxh_common.logger import Logger

logger = Logger(__name__)


class RecaptchaService:
    def __init__(self):
        self.base_url = settings.RECAPTCHA_BASE_URL
        self.project_id = settings.RECAPTCHA_PROJECT_ID
        self.site_key = settings.RECAPTCHA_SITE_KEY
        self.api_key = settings.GOOGLE_RECAPTCHA_API_KEY
        self.url = f"{self.base_url}/{self.project_id}/assessments?key={self.api_key}"
        self.min_score = getattr(settings, "RECAPTCHA_MIN_SCORE", 0.5)

    def _prepare_payload(self, token):
        return {
            "event": {
                "token": token,
                "site_key": self.site_key,
            }
        }

    def _send_request(self, payload):
        headers = {"Content-Type": "application/json"}
        try:
            response = requests.post(self.url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"captcha request failed: {e}")
            raise RuntimeError("Failed to connect to captcha service.")

    def validate_token(self, token):
        try:
            payload = self._prepare_payload(token)
            response_data = self._send_request(payload)

            risk_analysis = response_data.get("riskAnalysis", {})
            score = risk_analysis.get("score", 0)
            reasons = risk_analysis.get("reasons", [])

            if score < self.min_score:
                return self._build_response(
                    status="fail",
                    message="Token validation failed due to low score.",
                    data={"score": score},
                    errors={"reasons": reasons},
                )

            return self._build_response(
                status="success",
                message="Token validation successful.",
                data={"score": score},
                errors=None,
            )

        except ValueError as e:
            logger.error(f"Failed to parse captcha response: {e}")
            return self._build_response(
                status="error",
                message="Unable to process captcha response.",
                data=None,
                errors={"details": str(e)},
            )

        except RuntimeError as e:
            return self._build_response(
                status="error",
                message="Failed to connect to captcha service.",
                data=None,
                errors={"details": str(e)},
            )

    def _build_response(self, status, message, data=None, errors=None):
        return {
            "status": status,
            "message": message,
            "data": data,
            "errors": errors,
        }
