from django.http import JsonResponse
from django.conf import settings
from dxh_common.utils.constants import LICENSE_KEY


class LicenseKeyMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        allowed_license_key = settings.LICENSE_KEY

        license_key = request.headers.get(LICENSE_KEY)
        if not license_key or license_key != allowed_license_key:
            result = {
                "message": "Invalid or missing license key",
            }
            return JsonResponse(result, status=403)

        return self.get_response(request)
