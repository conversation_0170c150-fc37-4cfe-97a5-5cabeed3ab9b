from datetime import datetime
from rest_framework.renderers import JSONRenderer


class CustomJSO<PERSON>enderer(JSONRenderer):
    def render(self, data, accepted_media_type=None, renderer_context=None):
        # Extract the response object from the renderer context
        response = renderer_context.get("response") if renderer_context else None
        request = renderer_context.get("request") if renderer_context else None
        
        # Determine the status based on the HTTP status code
        response_status = "success" if response and response.status_code < 400 else "fail"
        # Extract the message and errors if available
        if isinstance(data, dict):
            message = data.pop("message", "Request processed successfully." if response_status == "success" else "Unable to process your request at this time.")
            errors = data.pop("errors", None)
        else:
            message = "Request processed successfully." if response_status == "success" else "Unable to process your request at this time."
            errors = None
            
        result = data.get("data", None)
        path = request.path if request else None
        timestamp = datetime.utcnow().isoformat()
        pagination = data.get("pagination", None)
        
        # Construct the standardized response format
        formatted_response = {
            "path": path,
            "timestamp": timestamp,
            "status": response_status,
            "message": message,
            "data": result if response_status == "success" else None,
            "errors": errors if response_status == "fail" else None,
        }

        if pagination:
            formatted_response["pagination"] = pagination

        return super().render(formatted_response, accepted_media_type, renderer_context)