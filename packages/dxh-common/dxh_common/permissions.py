from dxh_libraries.rest_framework import BasePermission, AuthenticationFailed
from dxh_libraries.rest_framework_api_key import Has<PERSON><PERSON><PERSON>ey


class IsSuperAdmin(BasePermission):
    """
    Allows access only to super admins.
    """

    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.is_superuser


class IsAdmin(BasePermission):
    """
    Allows access only to admins (staff users).
    """

    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.is_staff


class IsSuperAdminOrAdmin(BasePermission):
    """
    Custom permission to allow access to either super admins or admins.
    """

    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and (request.user.is_superuser or request.user.is_staff)


class IsAuthenticated(BasePermission):
    """
    Allows access only to authenticated users.
    """

    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated)


class AllowAny(BasePermission):
    """
    Allow any access.
    """

    def has_permission(self, request, view):
        return True


class CustomHasAPIKey(HasAPIKey):
    def has_permission(self, request, view):

        api_key = self.get_key(request)
        if not api_key:
            raise AuthenticationFailed("Access denied.")

        return super().has_permission(request, view)
