from functools import wraps
from dxh_libraries.rest_framework import Response, status
from dxh_common.services import RecaptchaService


def recaptcha(enabled=True):
    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(self, request, *args, **kwargs):
            if enabled:
                try:
                    captcha_service = RecaptchaService()
                    captcha_token = request.headers.get("X-Captcha-Token")

                    if not captcha_token:
                        return Response(
                            {"message": "ReCAPTCHA verification failed: No token provided."},
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                    captcha_response = captcha_service.validate_token(
                        captcha_token)

                    if not captcha_response or captcha_response.get("status") != "success":
                        return Response(
                            {"message": captcha_response.get(
                                "message", "ReCAPTCHA validation failed.")},
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                except Exception as e:
                    return Response(
                        {"message": f"ReCAPTCHA validation error: {str(e)}"},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    )

            return view_func(self, request, *args, **kwargs)

        return wrapped_view

    return decorator
