[tool.black]
line-length = 120
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
    (^\.|.gitignore|migrations/|models/|models\.py$)
'''
skip-string-normalization = true
fast = true
preview = true

[tool.ruff.lint.isort]
case-sensitive = true
force-single-line = true
section-order = ["future", "standard-library", "first-party", "django", "rest_framework", "third-party", "local-folder"]


[tool.ruff.lint.isort.sections]
"django" = ["django"]
"rest_framework" = ["rest_framework"]
