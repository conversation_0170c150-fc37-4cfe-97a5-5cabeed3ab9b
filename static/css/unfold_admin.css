/* All apps for the admin panel */

a.section {
  text-transform: uppercase;
}

a.block.font-semibold.mb-2.mt-4.text-font-important-light.text-sm.truncate.dark\:text-font-important-dark {
  text-transform: uppercase;
}

/* Hide Return to site button */
a.flex.font-medium.items-center.text-sm.text-primary-600.dark\:text-primary-500 {
  visibility: hidden;
}

form[action="/i18n/setlang/"] button[type="submit"] {
  text-transform: capitalize;
}
.object-tools {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.import_link,
.export_link {
  background-color: #4caf50; /* Green */
  border: none;
  color: white;
  padding: 8px 15px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 14px;
  border-radius: 5px;
  cursor: pointer;
}

.import_link {
  background-color: #6c5dd3; /* Blue */
  margin: 5px;
}

.export_link {
  background-color: #ff7e67; /* Orange */
}

.import_link:hover {
  background-color: #1e88e5;
}

.export_link:hover {
  background-color: #e67e22;
}