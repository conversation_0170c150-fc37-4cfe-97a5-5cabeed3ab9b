// Replace the settings icon with a custom image in the admin panel sidebar
let base64Image =
  "data:image/png;base64,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";

document.addEventListener("DOMContentLoaded", function () {
  document.querySelectorAll('a[href*="/admin/"]').forEach((link) => {
    let icon = link.querySelector(".material-symbols-outlined.md-18");

    if (icon && icon.textContent.trim() === "settings") {
      icon.style.display = "none";
      link.style.backgroundColor = "transparent";

      // Create an <img> element and insert it inside the <a> tag
      let img = document.createElement("img");
      img.src = base64Image;
      img.alt = "Settings";
      img.style.width = "32px";
      img.style.height = "32px";

      link.appendChild(img);
    }
  });
});
