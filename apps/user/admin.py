from django.contrib import admin
from common.config import BaseAdmin
from apps.user.models import (
    Role, UserRole, UserActivity, UserPreference, ConnectedApp, DailyTokenUsages,
    UserActionLog, DailyMessageUsage,
)


admin.site.register(ConnectedApp)
# --------------------------------------------------------
# USER PREFERENCE ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(UserPreference)
class UserPreferenceAdmin(BaseAdmin):
    """
    Admin configuration for the UserPreference model.
    """
    list_display = ('user', 'language', 'timezone', 'created_at')
    list_filter = ('language', 'timezone')
    search_fields = ('user__email',)
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'updated_at')


# --------------------------------------------------------
# ROLE ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(Role)
class RoleAdmin(BaseAdmin):
    """
    Admin configuration for the Role model.
    """
    list_display = ('name', 'description', 'created_at')
    search_fields = ('name', 'description')
    ordering = ('name',)
    readonly_fields = ('created_at', 'updated_at')


# --------------------------------------------------------
# USER ROLE ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(UserRole)
class UserRoleAdmin(BaseAdmin):
    """
    Admin configuration for the UserRole model.
    """
    list_display = ('user', 'role', 'created_at')
    list_filter = ('role',)
    search_fields = ('user__email', 'role__name')
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'updated_at')


# --------------------------------------------------------
# USER ACTIVITY ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(UserActivity)
class UserActivityAdmin(BaseAdmin):
    """
    Admin configuration for the UserActivity model.
    """
    list_display = ('user', 'activity_type', 'ip_address', 'created_at')
    list_filter = ('activity_type', 'created_at')
    search_fields = ('user__email', 'description', 'ip_address')
    readonly_fields = ('created_at', 'updated_at', 'ip_address')
    ordering = ('-created_at',)

@admin.register(DailyTokenUsages)
class DailyTokenUsagesAdmin(BaseAdmin):
    """
    Admin configuration for the DailyTokenUsages model.
    """
    list_display = ('user', 'activity_date', 'total_tokens', 'updated_at')
    list_filter = ('activity_date', 'created_at')
    search_fields = ('user__email',)
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-created_at',)

# --------------------------------------------------------
#   Admin configuration for the UserActionLog model.
# --------------------------------------------------------
@admin.register(UserActionLog)
class UserActionLogAdmin(admin.ModelAdmin):
    list_display = ('user', 'action_type', 'message_id', 'conversation_id', 'source', 'location', 'created_at')
    list_filter = ('action_type', 'source', 'company', 'created_at')
    search_fields = ('user__email', 'company__name', 'action_type', 'source', 'location')
    readonly_fields = ('created_at', 'updated_at', 'message_id', 'conversation_id', 'user', 'company', 'action_type', 'source', 'selected_text', 'location', 'metadata')
    ordering = ('-created_at',)

    def get_queryset(self, request):
        """Optimize queryset for performance."""
        return super().get_queryset(request).select_related('user', 'company')


@admin.register(DailyMessageUsage)
class DailyMessageUsageAdmin(admin.ModelAdmin):
    list_display = ('user', 'activity_date', 'message_count', 'updated_at')
    list_filter = ('activity_date', 'created_at')
    search_fields = ('user__email',)
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-created_at',)