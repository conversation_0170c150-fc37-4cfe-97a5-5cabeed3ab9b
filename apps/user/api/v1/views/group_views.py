from typing import Any
from dxh_libraries.rest_framework import status, Response
from rest_framework.permissions import IsAdminUser

from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from django.contrib.auth.models import Group, Permission

from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView
from apps.user.services import PermissionService
from apps.user.api.v1.serializers import GroupSerializer 
from apps.user.api.v1.serializers import PermissionSerializer

User = get_user_model()
logger = Logger(__name__)


class GroupListView(BaseApiView):
    permission_classes = [IsAdminUser]

    def get(self, request):
        groups = Group.objects.all()
        result = {
            "message": "Groups retrieved successfully",
            "data": GroupSerializer(groups, many=True).data
        }
        return Response(result)

    def post(self, request):
        serializer = GroupSerializer(data=request.data)
        if serializer.is_valid():
            group = serializer.save()
            result = {
                "message": "Group created successfully",
                "data": GroupSerializer(group).data,
            }
            return Response(
                result,
                status=status.HTTP_201_CREATED
            )

        result = {
            "message": "Validation error",
            "errors": serializer.errors
        }
        return Response(result)

    def put(self, request):
        group = get_object_or_404(Group, id=request.data.get('id'))
        serializer = GroupSerializer(
            group, data=request.data, partial=True
        )
        if serializer.is_valid():
            group = serializer.save()
            result = {
                "message": "Group updated successfully",
                "data": serializer.data
            }
            return Response(result)

        result = {
            "message": "Validation error",
            "errors": serializer.errors
        }
        return Response(result)

    def delete(self, request):
        group = get_object_or_404(Group, id=request.data.get('id'))
        group.delete()

        result = {
            "message": "Group deleted successfully"
        }
        return Response(result)


class GroupDetailView(BaseApiView):
    permission_classes = [IsAdminUser]

    def get(self, request, group_id):
        group = get_object_or_404(Group, id=group_id)
        serializer = GroupSerializer(group)
        result = {
            "message": "Group details retrieved",
            "data": serializer.data
        }
        return Response(result)

    def put(self, request, group_id):
        group = get_object_or_404(Group, id=group_id)
        serializer = GroupSerializer(
            group, data=request.data, partial=True
        )
        if serializer.is_valid():
            group = serializer.save()
            result = {
                "message": "Group updated successfully",
                "data": serializer.data
            }
            return Response(result)

        result = {
            "message": "Validation error",
            "errors": serializer.errors
        }
        return Response(result)

    def delete(self, request, group_id):
        group = get_object_or_404(Group, id=group_id)
        group.delete()
        result = {
            "message": "Group deleted successfully"
        }
        return Response(result)


class PermissionListView(BaseApiView):
    permission_classes = [IsAdminUser]

    def get(self, request):
        permissions = Permission.objects.all()

        result = {
            "message": "Permissions retrieved successfully",
            "data": PermissionSerializer(permissions, many=True).data
        }
        return Response(result)


class PermissionDetailView(BaseApiView):
    permission_classes = [IsAdminUser]

    def get(self, request, permission_id):
        permission = get_object_or_404(Permission, id=permission_id)
        serializer = PermissionSerializer(permission)
        result = {
            "message": "Permission details retrieved",
            "data": serializer.data
        }
        return Response(result)


class GroupUserView(BaseApiView):
    permission_classes = [IsAdminUser]

    def post(self, request, group_id):
        group = get_object_or_404(Group, id=group_id)
        user_id = request.data.get('user_id')
        user = get_object_or_404(User, id=user_id)

        if user.groups.filter(id=group_id).exists():
            result = {
                "message": "User already belongs to this group",
            }
            return Response(
                result,
                status=status.HTTP_400_BAD_REQUEST
            )

        group.user_set.add(user)
        result = {
            "message": "User added to group successfully",
        }
        return Response(result)

    def delete(self, request, group_id):
        group = get_object_or_404(Group, id=group_id)
        user_id = request.data.get('user_id')
        user = get_object_or_404(User, id=user_id)

        if not user.groups.filter(id=group_id).exists():
            result = {
                "message": "User does not belong to this group",
            }
            return Response(
                result,
                status=status.HTTP_400_BAD_REQUEST
            )

        group.user_set.remove(user)
        result = {
            "message": "User removed from group successfully",
        }
        return Response(result)


class GroupPermissionAPIView(BaseApiView):
    permission_classes = [IsAdminUser]

    def __init__(self):
        self.permission_service = PermissionService()
        self.permissions = Permission.objects.all()

    def get(self, request, group_id):
        if not group_id:
            result = {
                "message": "Group ID is required",
            }
            return Response(
                result,
                status=status.HTTP_400_BAD_REQUEST
            )

        user_permissions = self.permission_service.get_all_group_permissions(group_id)
        serializer = PermissionSerializer(user_permissions, many=True)
        result = {
            "message": "Group permissions retrieved",
            "data": serializer.data
        }
        return Response(result)

    def post(self, request, group_id):
        if not group_id:
            result = {
                "message": "Group ID is required",
            }
            return Response(
                result,
                status=status.HTTP_400_BAD_REQUEST
            )

        group_permissions_ids = request.data.get('group_permissions_ids', [])
        self.permission_service.set_permissions_for_group(
            group_id , group_permissions_ids)

        result = {
            "message": "Group permissions updated",
        }
        return Response(result)
