from typing import Any
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import status, Response
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView

from apps.user.api.v1.serializers import RoleSerializer 
from apps.user.api.v1.serializers.user_serializers import UserDetailSerializer 
from apps.notification.api.v1.serializers import NotificationSerializer
from apps.user.api.v1.serializers import DeleteAccountSerializer
from apps.user.services.user_service import UserService
from apps.identity.services.identity_service import IdentityService

User = get_user_model()
logger = Logger(__name__)


class RequestDeletionView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()
        self.identity_service = IdentityService()
   
    def post(self, request):
        try:
            user = request.user
            payload = request.data
            serializer = DeleteAccountSerializer(data=payload, context={"request": request})
            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            data = serializer.validated_data
            confirmation_text = data.get("confirmation_text")
            if confirmation_text != "DELETE MY ACCOUNT":
                result = {
                    "message": _("Confirmation text is incorrect."),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            if not user.has_usable_password():
                success = self.identity_service.validate_otp(user, data.get("code"))
                if not success:
                    result = {
                        "message": _("Invalid code or expired."),
                    }
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
              
            self.user_service.request_account_deletion(user)
            result = {
                "message": _("Your account will be deleted after 30 days. If you change your mind, you can cancel the deletion within this period.")
            }

            return Response(result, status=status.HTTP_200_OK)
        
        except Exception as e:
            raise e


class CancelDeletionView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()
    
    def post(self, request):
        try:
            user = request.user
            self.user_service.cancle_account_deletion(user)
            result = {
                "message": _("Account deletion cancelled successfully.")
            }

            return Response(result, status=status.HTTP_200_OK)
        
        except Exception as e:
            raise e


class ExportDataView(BaseApiView):
    def get(self, request, user_id):
        user = get_object_or_404(User, id=user_id)

        if user != request.user and not request.user.is_staff:
            result = {
                "message": "Permission denied"
            }
            return Response(
                result,
                status=status.HTTP_403_FORBIDDEN
            )

        # Collect user data
        user_data = {
            'profile': UserDetailSerializer(user).data,
            'notifications': NotificationSerializer(
                user.notification_set.all(),
                many=True
            ).data,
            'roles': RoleSerializer(
                user.roles.all(),
                many=True
            ).data
        }

        result = {
            "message": "Data export successful",
            "data": user_data
        }
        return Response(result)
