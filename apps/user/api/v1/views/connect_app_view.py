from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import status, Response

from apps.user.services import CloudStorageProviderService
from apps.user.services import UserService

class ConnectAppAPIView(BaseApiView):
    """API View for managing connected apps."""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.file_sorage_service = CloudStorageProviderService()
        self.user_service = UserService()

    def post(self, request):
        provider = request.data.get("provider")
        authorization_code = request.data.get("code")
        if not authorization_code or not provider:
            return Response(
                {"message": "Missing required fields"},
                status=status.HTTP_400_BAD_REQUEST,
            )
            
        storage_provider = self.file_sorage_service.get_provider(provider)

        try:
            response_data = storage_provider.exchange_code(authorization_code)
            connected_app = self.user_service.save_connected_app(request.user, provider, response_data)
            
            return Response(
                {"message": "Connected app saved successfully", "data": connected_app},
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            return Response(
                {"message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )
