from apps.user.api.v1.serializers import UserActionLogSerializer
from apps.user.services import UserActionLogService
from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import status, Response
from apps.identity.utils.get_user_meta_data import get_device_info

class UserActionLogView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_action_service = UserActionLogService()

    def post(self, request, *args, **kwargs):
        data = request.data
        _, metadata = get_device_info(request)
        
        data['user'] = request.user
        data['metadata'] = metadata
        try:
            self.user_action_service.create(**data)
            return Response({"message": "Logged Successfully"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({"errors": str(e)}, status=status.HTTP_400_BAD_REQUEST)
