from rest_framework import serializers
from common.serializers import BaseSerializer
from django.utils.translation import gettext_lazy as _


class DeleteAccountSerializer(BaseSerializer):
    password = serializers.CharField(
        required=False,
        allow_null=True
    )
    code = serializers.CharField(
        required=False,
        allow_null=True
    )
    confirmation_text = serializers.CharField(
        required=True,
        error_messages={"required": _("Confirmation text is required.")},
    )
    
    def validate(self, attrs):
        user = self.context["request"].user
        password = attrs.get("password")
        code = attrs.get("code")

        if user.has_usable_password() and not password:
            raise serializers.ValidationError(_("Password is required."))

        if password and not user.check_password(password):
            raise serializers.ValidationError(_("Password is incorrect."))

        if not user.has_usable_password() and not code:
            raise serializers.ValidationError(_("Code in required"))

        return attrs