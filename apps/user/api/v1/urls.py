from django.urls import path
from apps.user.api.v1 import views

urlpatterns = [
    # Users
    path('users/', views.UserListView.as_view(), name='user-list'),
    path('users/<int:user_id>/', views.UserDetailView.as_view(), name='user-detail'),
    path('users/<int:user_id>/status/', views.UserStatusView.as_view(), name='user-status'),
    path('users/search/', views.UserSearchView.as_view(), name='user-search'),
    path('users/filters/', views.UserFilterView.as_view(), name='user-filter'),
    path('users/update-profile/', views.UserProfileView.as_view(), name='update-profile'),

    # Account
    path('accounts/deletion/request/', views.RequestDeletionView.as_view(), name='request-deletion'),
    path('accounts/deletion/cancel/', views.CancelDeletionView.as_view(), name='cancel-deletion'),
    path('accounts/<int:user_id>/export/', views.ExportDataView.as_view(), name='export-data'),

    # Preferences
    path('preferences/', views.UserPreferenceView.as_view(), name='user-preferences'),
    path('preferences/theme/', views.ThemePreferenceView.as_view(), name='theme-preference'),
    path('preferences/language/', views.LanguagePreferenceView.as_view(), name='language-preference'),
    path('preferences/timezone/', views.TimezonePreferenceView.as_view(), name='timezone-preference'),

    # Groups and Permissions
    path('groups/', views.GroupListView.as_view(), name='group-list'),
    path('groups/<int:group_id>/', views.GroupDetailView.as_view(), name='group-detail'),
    path('groups/<int:group_id>/permissions/', views.GroupPermissionAPIView.as_view(), name='group-permission-list-add'),
    path('groups/<int:group_id>/users/', views.GroupUserView.as_view(), name='group-users'),
    path('permissions/', views.PermissionListView.as_view(), name='permission-list'),
    path('permissions/<int:permission_id>/', views.PermissionDetailView.as_view(), name='permission-detail'),

    # Connected Apps
    path('connect-apps/', views.ConnectAppAPIView.as_view(), name='connected-app'),
    
    path('implicit-feedback/', views.UserActionLogView.as_view(), name='implicit-feedback'),
]
