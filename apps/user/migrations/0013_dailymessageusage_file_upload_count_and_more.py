# Generated by Django 5.1.5 on 2025-05-14 09:15

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0012_dailymessageusage'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='dailymessageusage',
            name='file_upload_count',
            field=models.IntegerField(default=0, verbose_name='File Upload Count'),
        ),
        migrations.AlterField(
            model_name='dailymessageusage',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='daily_message_usages', to=settings.AUTH_USER_MODEL, verbose_name='User'),
        ),
    ]
