# Generated by Django 5.1.5 on 2025-02-20 06:57

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_address_uuid_company_uuid_contactinfo_uuid_and_more'),
        ('user', '0009_alter_dailytokenusages_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserActionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('message_id', models.UUIDField(verbose_name='Message ID')),
                ('conversation_id', models.UUIDField(verbose_name='Conversation ID')),
                ('source', models.CharField(max_length=50, verbose_name='Source')),
                ('action_type', models.CharField(max_length=20, verbose_name='Action')),
                ('selected_text', models.TextField(blank=True, null=True, verbose_name='Selected Text')),
                ('location', models.CharField(blank=True, max_length=100, null=True, verbose_name='Location')),
                ('metadata', models.JSONField(default=dict, verbose_name='Meta Data')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'User Action Log',
                'verbose_name_plural': 'User Action Logs',
                'ordering': ['-created_at'],
            },
        ),
    ]
