# Generated by Django 5.1.5 on 2025-02-13 08:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0008_dailytokenusages'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='dailytokenusages',
            options={'verbose_name': 'Daily Token Usages', 'verbose_name_plural': 'Daily Token Usages'},
        ),
        migrations.AlterField(
            model_name='dailytokenusages',
            name='activity_date',
            field=models.DateField(auto_now_add=True, verbose_name='Activity Date'),
        ),
        migrations.AlterField(
            model_name='dailytokenusages',
            name='completion_tokens',
            field=models.IntegerField(default=0, verbose_name='Completion Tokens'),
        ),
        migrations.AlterField(
            model_name='dailytokenusages',
            name='prompt_tokens',
            field=models.IntegerField(default=0, verbose_name='Prompt Tokens'),
        ),
        migrations.AlterField(
            model_name='dailytokenusages',
            name='total_tokens',
            field=models.IntegerField(default=0, editable=False, verbose_name='Total Tokens'),
        ),
    ]
