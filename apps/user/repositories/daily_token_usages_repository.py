from common.repository import BaseRepository
from apps.user.models import DailyTokenUsages
from django.utils import timezone
from django.db.models import Sum


class DailyTokenUsagesRepository(BaseRepository):
    def __init__(self):
        super().__init__(DailyTokenUsages)

    def get_monthly_token_usage(self, user):
        today = timezone.now().date()
        start_of_month = today.replace(day=1)

        monthly_usage = self.model.objects.filter(
            user=user,
            activity_date__gte=start_of_month,
            activity_date__lte=today
        ).aggregate(total=Sum('total_tokens'))['total']

        return monthly_usage or 0