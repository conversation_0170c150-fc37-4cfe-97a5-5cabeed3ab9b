from common.repository import BaseRepository
from apps.user.models import ConnectedApp
from datetime import timedelta, datetime

class ConnectedAppRepository(BaseRepository):
    def __init__(self):
        super().__init__(ConnectedApp)
    
    def create_connected_app(self, user, provider, response):
        """Creates a new ConnectedApp instance."""
        expires_in_seconds = response.get("expires_in", 3600)
        expires_at = datetime.now() + timedelta(seconds=expires_in_seconds)

        app, _ = ConnectedApp.objects.update_or_create(
            user=user,
            provider=provider,
            defaults={
                "access_token": response.get("access_token"),
                "refresh_token": response.get("refresh_token"),
                "expires_at": expires_at,
                "token_type": response.get("token_type")
            }
        )
        return {
            "uuid": app.uuid,
            "provider": app.provider,
            "access_token": app.access_token,
            "refresh_token": app.refresh_token,
            "expires_at": app.expires_at,
            "token_type": app.token_type
        }