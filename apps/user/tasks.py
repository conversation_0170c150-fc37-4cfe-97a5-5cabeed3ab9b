from datetime import timedelta
from common.logger import Logger
from celery import shared_task
from django.utils.timezone import now
from django.contrib.auth import get_user_model

User = get_user_model()
logger = Logger(__name__)


@shared_task
def delete_users_after_30_days():
    threshold_date = now() - timedelta(days=30)
    users_to_delete = User.objects.filter(
        deletion_requested=True,
        deletion_requested_at__lte=threshold_date
    )
    
    count = users_to_delete.update(is_active=False, is_deleted=True)
    logger.info(f"Deleted {count} users who requested deletion 30 days ago.")
