import requests
from django.conf import settings
from abc import ABC, abstractmethod
from requests.exceptions import RequestException
from apps.integration.utils import ConfigHelper

class StorageProvider(ABC):
    """Abstract base class for handling social authentication."""
    @abstractmethod
    def exchange_code(self, authorization_code: str):
        """Exchanges authorization code for an access token, refresh token, and id_token."""
        pass


class GoogleStorageProvider(StorageProvider):
    """Handles authentication with Google OAuth2."""
    def __init__(self):
        config_helper = ConfigHelper()
        self.token_url = "https://oauth2.googleapis.com/token"
        self.client_id = config_helper.get_integration_setting('google', 'client_id', default=settings.SOCIAL_AUTH_GOOGLE_OAUTH2_KEY)
        self.client_secret = config_helper.get_integration_setting('google', 'client_secret', default=settings.SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET)
        self.redirect_uri = settings.GOOGLE_CALLBACK_URL
        self.grant_type = "authorization_code"

    def exchange_code(self, authorization_code: str):
        """Exchanges authorization code for an access token."""
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "redirect_uri": self.redirect_uri,
            "code": authorization_code,
            "grant_type": self.grant_type,
        }

        try:
            response = requests.post(self.token_url, data=data)
            response.raise_for_status()
            token_data = response.json()
            
            return token_data
        except Exception as e:
            raise RequestException(f"Error exchanging code: {str(e)}")


class CloudStorageProviderService:
    """Service layer for interacting with file storage providers."""

    providers = {
        "google_drive": GoogleStorageProvider,
        "onedrive": NotImplemented  # TODO: Implement OneDriveAuthProvider
    }

    @staticmethod
    def get_provider(provider_name):
        provider_class = CloudStorageProviderService.providers.get(provider_name)
        if not provider_class:
            raise ValueError(f"Unsupported provider: {provider_name}")
        return provider_class()