from django.conf import settings
from django.db.models import Q
from django.utils import timezone

from common.logger import Logger
from common.service import BaseService
from common.service import ServiceError
from common.constants import VerificationTokenType
from apps.cross_cutting import EmailService
from apps.core.services import CompanyService
from apps.identity.repositories import UserRepository
from apps.identity.services import IdentityService, VerificationCodeService
from apps.user.repositories import ConnectedAppRepository

logger = Logger(__name__)


class UserService(BaseService):
    def __init__(self):
        super().__init__(UserRepository())
        self.identity_service = IdentityService()
        self.verification_code_service = VerificationCodeService()
        self.company_service = CompanyService()
        self.connect_app_repository = ConnectedAppRepository()
        self.email_service = EmailService()

    def get_list_users(self):
        try:
            return self.repository.get_all()
        except Exception as e:
            logger.error(f"Error getting list of users: {str(e)}")
            raise ServiceError(f"Error getting list of users: {str(e)}")

    def get_user(self, data):
        try:
            return self.repository.get(**data)
        except Exception as e:
            logger.error(f"Error getting user with data {data}: {str(e)}")
            raise ServiceError(f"Error getting user: {str(e)}")

    def create_user(self, data):
        try:
            data["username"] = data["email"]
            user = self.repository.get(email=data['email'])

            # Check if user exists
            if user:
                if user.is_deleted:
                    user.set_password(data['password'])
                    user.save()
                    verification_code = self.verification_code_service.create_verification_code(
                        user, VerificationTokenType.EMAIL_OTP.value
                    )
                    self.identity_service.send_verification_link(user=user, code=verification_code)
                    return user
                # If user exists but is not deleted, we'll create a new user below

            # Create new user
            user = self.repository.create(**data, is_active=False)
            user.set_password(data['password'])
            user.save()

            # Create verification code for the new user
            verification_code = self.verification_code_service.create_verification_code(
                user, VerificationTokenType.EMAIL_OTP.value
            )
            self.identity_service.send_verification_link(user=user, code=verification_code)

            return user
        except Exception as e:
            logger.error(f"Error creating user with email {data['email']}: {str(e)}")
            raise ServiceError(f"Error creating user: {str(e)}")

    def update_user(self, user, data):
        try:
            for key, value in data.items():
                if hasattr(user, key):
                    setattr(user, key, value)
            user.save()
            logger.info(f"User {user.pk} updated successfully.")

            return user, None

        except Exception as e:
            logger.error(f"Error updating user {user.pk}: {str(e)}")
            return None, str(e)

    def search_users(self, query):
        try:
            users = self.repository.filter(
                Q(email__icontains=query) |
                Q(username__icontains=query) |
                Q(first_name__icontains=query) |
                Q(last_name__icontains=query)
            )
            logger.info(f"Search for users with query '{query}' returned {users.count()} results.")

            return users

        except Exception as e:
            logger.error(f"Error searching users with query '{query}': {str(e)}")
            raise ServiceError(f"Error searching users: {str(e)}")

    def filter_users(self, filters):
        try:
            queryset = self.repository.get_all()

            if filters.get('status'):
                queryset = queryset.filter(is_active=filters['status'] == 'active')

            if filters.get('verified'):
                queryset = queryset.filter(is_email_verified=filters['verified'])

            if filters.get('date_joined_after'):
                queryset = queryset.filter(
                    date_joined__gte=filters['date_joined_after']
                )

            if filters.get('date_joined_before'):
                queryset = queryset.filter(
                    date_joined__lte=filters['date_joined_before']
                )

            logger.info(f"Filtered users based on filters: {filters}")

            return queryset

        except Exception as e:
            logger.error(f"Error filtering users with filters {filters}: {str(e)}")
            raise ServiceError(f"Error filtering users: {str(e)}")

    def save_connected_app(self, user, provider, response):
        """Saves access token and refresh token for connected apps."""
        try:
            return self.connect_app_repository.create_connected_app(user, provider, response)
        except Exception as e:
            logger.error(f"Error saving connected app for provider {provider}: {str(e)}")
            raise ServiceError(f"Error saving connected app: {str(e)}")

    def request_account_deletion(self, user):
        try:
            user.deletion_requested = True
            user.deletion_requested_at = timezone.now()
            user.save()

            display_name = user.name
            if not display_name:
                base = user.email.split('@')[0].split('.')[0]
                display_name = " ".join(word.capitalize() for word in base.split('_'))

            context = {
                "user": display_name,
                "deletion_date": user.deletion_requested_at + timezone.timedelta(days=30),
                "recovery_url": f"{settings.FRONTEND_BASE_URL}/en/account/recovery"
            }
            self.email_service.send_email(
                to_email=user.email,
                subject="You account will be deleted after 30 days.",
                template_name="account_deletion",
                context=context
            )
        except Exception as e:
            logger.error(f"Error requesting account deletion for user {user.pk}: {str(e)}")
            raise ServiceError(f"Error requesting account deletion: {str(e)}")

    def cancle_account_deletion(self, user):
        try:
            user.deletion_requested = False
            user.deletion_requested_at = None
            user.save()

        except Exception as e:
            logger.error(f"Error requesting account deletion for user {user.pk}: {str(e)}")
            raise ServiceError(f"Error requesting account deletion: {str(e)}")