from django.contrib.auth.models import Permission, Group
from common.logger import Logger
from common.service import ServiceError

logger = Logger(__name__)

class PermissionService:

    def get_all_user_permissions(self, user):
        try:
            return user.user_permissions.all()
        except Exception as e:
            logger.error(f"Error getting user permissions for user {user.pk}: {str(e)}")
            raise ServiceError(f"Error getting user permissions: {str(e)}")

    def set_permissions_for_user(self, user, user_permissions_ids):
        try:
            user.user_permissions.clear()
            permissions = Permission.objects.filter(id__in=user_permissions_ids)
            user.user_permissions.add(*permissions)

        except Exception as e:  
            logger.error(f"Error setting user permissions for user {user.pk}: {str(e)}")
            raise ServiceError(f"Error setting user permissions: {str(e)}")

    def get_all_group_permissions(self, group_id):
        try:
            group = Group.objects.filter(id=group_id).first()
            return group.permissions.all()

        except Exception as e:
            logger.error(f"Error getting group permissions for group {group_id}: {str(e)}")
            raise ServiceError(f"Error getting group permissions: {str(e)}")

    def set_permissions_for_group(self, group_id, permission_ids):
        try:
            group = Group.objects.filter(id=group_id).first()
            if group:
                group.permissions.clear()
                permissions = Permission.objects.filter(id__in=permission_ids)
                group.permissions.add(*permissions)

        except Exception as e:
            logger.error(f"Error setting group permissions for group {group_id}: {str(e)}")
            raise ServiceError(f"Error setting group permissions: {str(e)}")