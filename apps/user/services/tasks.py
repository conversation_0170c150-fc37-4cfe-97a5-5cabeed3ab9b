from celery import shared_task
from django.utils import timezone
from django.contrib.auth import get_user_model

User = get_user_model()


@shared_task
def delete_expired_accounts():
    """Delete accounts where the 7-day grace period has expired."""
    expiration_date = timezone.now() - timezone.timedelta(days=7)
    users_to_delete = User.objects.filter(deletion_requested=True, deletion_requested_at__lte=expiration_date)
    
    count = users_to_delete.count()
    users_to_delete.delete()

    return f"Deleted {count} expired accounts"