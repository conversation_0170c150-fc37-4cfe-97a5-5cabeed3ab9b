from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel
from common.constants import PROVIDER

User = get_user_model()

class ConnectedApp(BaseModel):
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='connected_apps',
        verbose_name=_("User"),
    )
    provider = models.CharField(
        max_length=20, 
        choices=PROVIDER.choices(),
        verbose_name=_("Provider")
    )
    access_token = models.TextField()
    refresh_token = models.TextField(
        null=True, 
        blank=True,
        verbose_name=_("Refresh Token")
    )
    expires_at = models.DateTimeField()
    token_type = models.CharField(
        max_length=20,
        verbose_name=_("Token Type")
    )

    def __str__(self):
        return f"{self.user.email} - {self.provider}"
