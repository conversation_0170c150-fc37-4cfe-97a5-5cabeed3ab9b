from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel
from common.constants import Language, Theme

User = get_user_model()


class UserPreference(BaseModel):
    company = models.ForeignKey(
        'core.Company',
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("Company")
    )
    user = models.OneToOneField(
        User,
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("User")
    )
    language = models.CharField(
        max_length=10,
        choices=Language.choices(),
        default=Language.ENGLISH.value,
        verbose_name=_("Language")
    )
    timezone = models.CharField(
        max_length=50,
        default='UTC',
        verbose_name=_("Timezone")
    )
    theme = models.CharField(
        max_length=5, 
        choices=Theme.choices(), 
        default=Theme.LIGHT.value,
        null=False, 
        blank=False
    )

    class Meta:
        db_table = 'user_preferences'
        verbose_name = _('User preference')
        verbose_name_plural = _('User preferences')

    def __str__(self):
        return f"{self.user} Preferences"
