from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel

User = get_user_model()


class DailyTokenUsages(BaseModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='daily_token_usages')
    activity_date = models.DateField(auto_now_add=True, verbose_name=_("Activity Date"))
    prompt_tokens = models.IntegerField(default=0, verbose_name=_("Prompt Tokens"))
    completion_tokens = models.IntegerField(default=0, verbose_name=_("Completion Tokens"))
    total_tokens = models.IntegerField(default=0, editable=False, verbose_name=_("Total Tokens"))

    class Meta:
        unique_together = ('user', 'activity_date')
        verbose_name = _('Daily Token Usages')
        verbose_name_plural = _('Daily Token Usages')

    def save(self, *args, **kwargs):
        """Ensure total_tokens is always up-to-date before saving."""
        self.total_tokens = self.prompt_tokens + self.completion_tokens
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.email} - {self.activity_date} - {self.total_tokens}"
