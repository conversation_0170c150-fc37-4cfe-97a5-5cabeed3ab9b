from django.db import models
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel


class Role(BaseModel):
    company = models.ForeignKey(
        'core.Company',
        on_delete=models.CASCADE,
        null=True,
        db_index=True,
        verbose_name=_("Company")
    )
    name = models.CharField(
        max_length=100,
        unique=True,
        db_index=True,
        verbose_name=_("Role Name")
    )
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Role Description")
    )

    class Meta:
        db_table = 'user_roles'
        verbose_name = _('User role')
        verbose_name_plural = _('User roles')

    def __str__(self):
        return self.name
