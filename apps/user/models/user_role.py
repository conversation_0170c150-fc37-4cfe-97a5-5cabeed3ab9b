from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel

User = get_user_model()

class UserRole(BaseModel):
    company = models.ForeignKey(
        'core.Company',
        on_delete=models.CASCADE,
        null=True,
        db_index=True,
        verbose_name=_("Company")
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        db_index=True,
        verbose_name=_("User")
    )
    role = models.ForeignKey(
        'user.Role',
        on_delete=models.CASCADE,
        db_index=True,
        verbose_name=_("Role")
    )

    class Meta:
        db_table = 'user_role_mappings'
        unique_together = ('user', 'role')
        verbose_name = _('User role mapping')
        verbose_name_plural = _('User role mappings')

    def __str__(self):
        return f"{self.user} - {self.role}"
