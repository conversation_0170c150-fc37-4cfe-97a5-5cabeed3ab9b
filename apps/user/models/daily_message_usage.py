from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel

User = get_user_model()


class DailyMessageUsage(BaseModel):
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='daily_message_usages',
        verbose_name=_("User")
    )
    activity_date = models.DateField(
        auto_now_add=True, 
        verbose_name=_("Activity Date")
    )
    message_count = models.IntegerField(
        default=0, 
        verbose_name=_("Message Count")
    )
    file_upload_count = models.IntegerField(
        default=0, 
        verbose_name=_("File Upload Count")
    )
    image_generation_count = models.IntegerField(
        default=0, 
        verbose_name=_("Image Generation Count")
    )

    class Meta:
        unique_together = ('user', 'activity_date')
        verbose_name = _('Daily Message Usage')
        verbose_name_plural = _('Daily Message Usages')

    def __str__(self):
        return f"{self.user.email} - {self.user.subscription_type}- {self.activity_date} - {self.message_count}"
