from django.db import models
from common.models import BaseModel
from django.utils.translation import gettext_lazy as _

class UserActionLog(BaseModel):
    user = models.ForeignKey(
        "identity.User", 
        on_delete=models.SET_NULL, 
        null=True,
        verbose_name=_("User")
    )
    company = models.ForeignKey(
        'core.Company',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Company")
    )
    message_id = models.UUIDField(verbose_name=_("Message ID"))
    conversation_id = models.UUIDField(verbose_name=_("Conversation ID"))
    source = models.CharField(max_length=50, verbose_name=_("Source"))
    action_type = models.CharField(max_length=20, verbose_name=_("Action"))
    selected_text = models.TextField(blank=True, null=True, verbose_name=_("Selected Text"))
    location = models.CharField(max_length=100, blank=True, null=True, verbose_name=_("Location"))
    metadata = models.J<PERSON><PERSON>ield(default=dict, verbose_name=_("Meta Data"))

    class Meta:
        ordering = ["-created_at"]
        verbose_name=_("User Action Log")
        verbose_name_plural=_("User Action Logs")

    def __str__(self):
        return f"{self.action_type} by {self.user} in {self.location} at {self.created_at}"
