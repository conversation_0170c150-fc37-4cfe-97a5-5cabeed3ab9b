from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel

User = get_user_model()


class UserActivity(BaseModel):
    company = models.ForeignKey(
        'core.Company',
        on_delete=models.CASCADE,
        null=True,
        db_index=True,
        verbose_name=_("Company")
    )
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        db_index=True,
        verbose_name=_("User")
    )
    activity_type = models.CharField(
        max_length=50,
        db_index=True,
        verbose_name=_("Activity Type")
    )
    description = models.TextField()
    ip_address = models.GenericIPAddressField(
        null=True,
        verbose_name=_("IP Address")
    )
    device_info = models.CharField(
        max_length=255, 
        null=True, 
        blank=True,
        verbose_name=_("Device Information")
    ) 
    metadata = models.JSONField(default=dict, verbose_name=_("Meta Data"))

    class Meta:
        db_table = 'user_activities'