from django.db import models
from common.logger import Logger
from apps.llm_manager.utils.prompt_to_title import prompt_to_title

logger =Logger(__name__)

class ConversationManager(models.Manager):
    def create_conversation(self, user, prompt, conversation_uuid=None):
        """Create a new conversation or retrieve an existing one."""
        if conversation_uuid:
            try:
                conversation = self.get(uuid=conversation_uuid, user=user)
            except self.model.DoesNotExist:
                logger.error(f"No conversation found for user {user.uuid} with UUID {conversation_uuid}")
                raise ValueError(f"No conversation found for user {user.uuid} with UUID {conversation_uuid}")
        else:
            conversation = self.create(
                user=user,
                title=prompt_to_title(prompt),
            )
        return conversation