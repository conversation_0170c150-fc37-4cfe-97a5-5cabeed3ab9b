from django.core.management.base import BaseCommand
from apps.llm_manager.models import LLMModels

LLM_MODELS = [
    {"name": "ChatGPT-4o-mini", "code": "gpt-4o-mini"},
    {"name": "ChatGPT-4o", "code": "gpt-4o"},
    {"name": "ChatGPT-4", "code": "gpt-4"},
    # {"name": "DeepSeek Chat", "code": "deepseek-chat"},
]
class Command(BaseCommand):
    help = "Populate the database with predefined AI models"

    def handle(self, *args, **kwargs):
        created_count = 0
        for model_data in LLM_MODELS:
            obj, created = LLMModels.objects.get_or_create(
                code=model_data["code"],
                defaults=model_data,
            )
            if created:
                created_count += 1

        self.stdout.write(self.style.SUCCESS(f"Successfully added {created_count} AI models"))
