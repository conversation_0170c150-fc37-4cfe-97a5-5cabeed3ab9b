import json
import uuid
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status

from apps.llm_manager.models import Conversation, Message, LLMModel
from apps.llm_manager.services import ConversationService

User = get_user_model()


class StopStreamingTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
        
        # Create a test LLM model
        self.model = LLMModel.objects.create(
            name='GPT-4o Mini',
            code='gpt-4o-mini',
            provider='openai',
            is_active=True
        )
        
        # Clear cache before each test
        cache.clear()

    def test_stop_streaming_endpoint_exists(self):
        """Test that the stop streaming endpoint is accessible"""
        url = reverse('llm_manager:stop-streaming')
        response = self.client.post(url, {
            'streaming_session_id': 'test_session_id'
        })
        
        # Should return 404 since session doesn't exist, but endpoint should be accessible
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_stop_streaming_missing_session_id(self):
        """Test stop streaming with missing session ID"""
        url = reverse('llm_manager:stop-streaming')
        response = self.client.post(url, {})
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('streaming_session_id is required', response.data['message'])

    def test_stop_streaming_nonexistent_session(self):
        """Test stop streaming with non-existent session ID"""
        url = reverse('llm_manager:stop-streaming')
        response = self.client.post(url, {
            'streaming_session_id': 'nonexistent_session'
        })
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('not found', response.data['message'])

    def test_stop_streaming_active_session(self):
        """Test stopping an active streaming session"""
        # Create an active streaming session in cache
        session_id = f"stream_{self.user.id}_{uuid.uuid4()}"
        cache.set(f"streaming_active:{session_id}", True, timeout=300)
        
        url = reverse('llm_manager:stop-streaming')
        response = self.client.post(url, {
            'streaming_session_id': session_id
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('stopped successfully', response.data['message'])
        
        # Verify session was removed from cache
        self.assertIsNone(cache.get(f"streaming_active:{session_id}"))

    def test_anonymous_stop_streaming_endpoint(self):
        """Test anonymous stop streaming endpoint"""
        # Use unauthenticated client
        client = APIClient()
        
        url = reverse('llm_manager:anonymous-stop-streaming')
        response = client.post(url, {
            'streaming_session_id': 'anon_stream_test_session'
        })
        
        # Should return 404 since session doesn't exist
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_anonymous_stop_streaming_invalid_session_format(self):
        """Test anonymous stop streaming with invalid session format"""
        client = APIClient()
        
        url = reverse('llm_manager:anonymous-stop-streaming')
        response = client.post(url, {
            'streaming_session_id': 'invalid_session_format'
        })
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Invalid streaming session ID', response.data['message'])

    def test_anonymous_stop_streaming_valid_session(self):
        """Test stopping an active anonymous streaming session"""
        # Create an active anonymous streaming session
        session_id = "anon_stream_127.0.0.1_test_device_conv_msg"
        cache.set(f"streaming_active:{session_id}", True, timeout=300)
        
        client = APIClient()
        url = reverse('llm_manager:anonymous-stop-streaming')
        response = client.post(url, {
            'streaming_session_id': session_id
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('stopped successfully', response.data['message'])
        
        # Verify session was removed from cache
        self.assertIsNone(cache.get(f"streaming_active:{session_id}"))

    @patch('apps.llm_manager.api.v1.views.chat_with_ai.AIHandlerFactory')
    def test_streaming_session_creation(self, mock_ai_factory):
        """Test that streaming sessions are created during chat"""
        # Mock AI handler
        mock_handler = MagicMock()
        mock_handler.send_text_request.return_value = iter([
            MagicMock(choices=[MagicMock(delta=MagicMock(content="Hello"))])
        ])
        mock_handler.count_tokens.return_value = 10
        mock_ai_factory.return_value.get_handler.return_value = mock_handler
        
        # Mock other services
        with patch('apps.llm_manager.api.v1.views.chat_with_ai.ConversationService') as mock_conv_service:
            mock_conversation = MagicMock()
            mock_conversation.uuid = uuid.uuid4()
            mock_user_message = MagicMock()
            mock_ai_message = MagicMock()
            mock_ai_message.uuid = uuid.uuid4()
            
            mock_conv_service.return_value.create_conversation_and_message.return_value = (
                mock_conversation, mock_user_message, [{"role": "user", "content": "test"}]
            )
            mock_conv_service.return_value.save_ai_response.return_value = mock_ai_message
            
            with patch('apps.llm_manager.api.v1.views.chat_with_ai.DailyMessageUsageService') as mock_usage:
                mock_usage.return_value.check_daily_limit.return_value = (True, 1, 100)
                mock_usage.return_value.update_daily_usage.return_value = MagicMock()
                
                with patch('apps.llm_manager.api.v1.views.chat_with_ai.DailyTokenUsagesService') as mock_token:
                    mock_token.return_value.get_monthly_token_usage.return_value = 1000
                    mock_token.return_value.update_daily_usages.return_value = MagicMock()
                    
                    url = reverse('llm_manager:ai-conversation')
                    response = self.client.post(url, {
                        'message': {
                            'content': {
                                'parts': ['Hello AI']
                            }
                        },
                        'model': 'gpt-4o-mini'
                    }, format='json')
                    
                    self.assertEqual(response.status_code, status.HTTP_200_OK)
                    
                    # Check that a streaming session was created in cache
                    # Note: This is a simplified test - in reality, we'd need to parse the streaming response

    def tearDown(self):
        """Clean up after each test"""
        cache.clear()


class StreamingSessionManagerTestCase(TestCase):
    """Test streaming session management utilities"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        cache.clear()

    def test_session_id_format_authenticated(self):
        """Test authenticated user session ID format"""
        message_uuid = uuid.uuid4()
        expected_session_id = f"stream_{self.user.id}_{message_uuid}"
        
        # This would be the format used in the actual implementation
        self.assertTrue(expected_session_id.startswith("stream_"))
        self.assertIn(str(self.user.id), expected_session_id)
        self.assertIn(str(message_uuid), expected_session_id)

    def test_session_id_format_anonymous(self):
        """Test anonymous user session ID format"""
        ip_address = "***********"
        device_info = "test_device"
        conversation_id = str(uuid.uuid4())
        message_id = str(uuid.uuid4())
        
        expected_session_id = f"anon_stream_{ip_address}_{device_info}_{conversation_id}_{message_id}"
        
        self.assertTrue(expected_session_id.startswith("anon_stream_"))
        self.assertIn(ip_address, expected_session_id)
        self.assertIn(device_info, expected_session_id)

    def test_cache_operations(self):
        """Test cache operations for streaming sessions"""
        session_id = "test_session_123"
        cache_key = f"streaming_active:{session_id}"
        
        # Set session as active
        cache.set(cache_key, True, timeout=300)
        self.assertTrue(cache.get(cache_key))
        
        # Remove session (simulate stop)
        cache.delete(cache_key)
        self.assertIsNone(cache.get(cache_key))

    def tearDown(self):
        cache.clear()
