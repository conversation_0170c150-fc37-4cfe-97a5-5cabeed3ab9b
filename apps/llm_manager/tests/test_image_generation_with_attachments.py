from django.test import TestCase
from django.contrib.auth import get_user_model
from unittest.mock import Mock, patch, MagicMock
from rest_framework.test import APIClient
from rest_framework import status
import json

from apps.llm_manager.api.v1.views.image_generation_view import ImageGenerationView
from apps.file_manager.models import FileUpload
from apps.core.models import Company

User = get_user_model()


class ImageGenerationWithAttachmentsTest(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create test user and company
        self.company = Company.objects.create(
            name="Test Company",
            email="<EMAIL>"
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            company=self.company
        )
        
        # Create test file upload
        self.file_upload = FileUpload.objects.create(
            user=self.user,
            company=self.company,
            file="test_images/test_image.jpg",
            file_type="jpg",
            original_name="test_image.jpg",
            size=1024
        )
        
        # Set up API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # API endpoint
        self.url = '/api/llm-manager/v1/images/generation/'

    def test_process_attachments_with_valid_file(self):
        """Test processing attachments with a valid file"""
        view = ImageGenerationView()
        view.file_service = Mock()
        view.file_service.get.return_value = self.file_upload
        
        validated_data = {
            "message": {
                "metadata": {
                    "attachments": [
                        {
                            "id": self.file_upload.id,
                            "file_type": "jpg"
                        }
                    ]
                }
            }
        }
        
        file_paths = view.process_attachments(validated_data)
        
        self.assertEqual(len(file_paths), 1)
        self.assertEqual(file_paths[0], self.file_upload.file.url)

    def test_process_attachments_with_no_attachments(self):
        """Test processing when no attachments are present"""
        view = ImageGenerationView()
        
        validated_data = {
            "message": {
                "content": {
                    "parts": ["test prompt"]
                }
            }
        }
        
        file_paths = view.process_attachments(validated_data)
        
        self.assertEqual(len(file_paths), 0)

    def test_process_attachments_with_empty_metadata(self):
        """Test processing with empty metadata"""
        view = ImageGenerationView()
        
        validated_data = {
            "message": {
                "metadata": {
                    "attachments": []
                }
            }
        }
        
        file_paths = view.process_attachments(validated_data)
        
        self.assertEqual(len(file_paths), 0)

    @patch('apps.llm_manager.api.v1.views.image_generation_view.AIHandlerFactory')
    @patch('apps.llm_manager.utils.file_types.is_vision_file_type')
    def test_enhance_prompt_with_attachments_success(self, mock_is_vision, mock_ai_factory):
        """Test successful prompt enhancement with image attachments"""
        # Mock vision file type check
        mock_is_vision.return_value = True
        
        # Mock AI handler response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "A beautiful landscape with mountains and trees"
        
        mock_handler = Mock()
        mock_handler.send_text_request_with_files.return_value = mock_response
        
        mock_ai_factory.return_value.get_handler.return_value = mock_handler
        
        view = ImageGenerationView()
        
        original_prompt = "Create a similar image"
        file_paths = ["/path/to/image.jpg"]
        
        enhanced_prompt = view.enhance_prompt_with_attachments(original_prompt, file_paths)
        
        expected_prompt = "Create a similar image\n\nBased on the attached images: A beautiful landscape with mountains and trees"
        self.assertEqual(enhanced_prompt, expected_prompt)

    @patch('apps.llm_manager.utils.file_types.is_vision_file_type')
    def test_enhance_prompt_with_non_vision_files(self, mock_is_vision):
        """Test prompt enhancement with non-vision files (should return original prompt)"""
        # Mock non-vision file type
        mock_is_vision.return_value = False
        
        view = ImageGenerationView()
        
        original_prompt = "Create an image"
        file_paths = ["/path/to/document.pdf"]
        
        enhanced_prompt = view.enhance_prompt_with_attachments(original_prompt, file_paths)
        
        # Should return original prompt since no vision files
        self.assertEqual(enhanced_prompt, original_prompt)

    @patch('apps.llm_manager.api.v1.views.image_generation_view.AIHandlerFactory')
    @patch('apps.llm_manager.utils.file_types.is_vision_file_type')
    def test_enhance_prompt_with_ai_error(self, mock_is_vision, mock_ai_factory):
        """Test prompt enhancement when AI service fails"""
        # Mock vision file type check
        mock_is_vision.return_value = True
        
        # Mock AI handler to raise exception
        mock_handler = Mock()
        mock_handler.send_text_request_with_files.side_effect = Exception("AI service error")
        
        mock_ai_factory.return_value.get_handler.return_value = mock_handler
        
        view = ImageGenerationView()
        
        original_prompt = "Create an image"
        file_paths = ["/path/to/image.jpg"]
        
        enhanced_prompt = view.enhance_prompt_with_attachments(original_prompt, file_paths)
        
        # Should return original prompt when AI service fails
        self.assertEqual(enhanced_prompt, original_prompt)

    def test_request_payload_structure(self):
        """Test that the request payload structure supports attachments"""
        payload = {
            "conversation_id": None,
            "model": "dall-e-3",
            "action": "generate",
            "message": {
                "author": {
                    "role": "user"
                },
                "content": {
                    "content_type": "text",
                    "parts": ["Create a beautiful landscape"],
                    "size": "1024x1024",
                    "style": "natural"
                },
                "metadata": {
                    "attachments": [
                        {
                            "id": self.file_upload.id,
                            "file_type": "jpg",
                            "original_name": "test_image.jpg"
                        }
                    ]
                }
            }
        }
        
        # Verify the payload structure is valid JSON
        json_payload = json.dumps(payload)
        parsed_payload = json.loads(json_payload)
        
        self.assertIn("message", parsed_payload)
        self.assertIn("metadata", parsed_payload["message"])
        self.assertIn("attachments", parsed_payload["message"]["metadata"])
        self.assertEqual(len(parsed_payload["message"]["metadata"]["attachments"]), 1)
        self.assertEqual(parsed_payload["message"]["metadata"]["attachments"][0]["id"], self.file_upload.id)
