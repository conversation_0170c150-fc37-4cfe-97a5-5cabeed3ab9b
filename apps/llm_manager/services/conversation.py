from dxh_common.base.base_service import BaseService
from django.http import Http404
from apps.llm_manager.repositories import ConversationRepository, MessageRepository, ProjectRepository
from apps.llm_manager.utils.prompt_to_title import prompt_to_title


class ConversationService(BaseService):
    def __init__(self):
        super().__init__(ConversationRepository())
        self.message_repo = MessageRepository()
        self.project_repo = ProjectRepository()

    def get_user_conversations(self, user):
        """Fetch user conversations"""
        return self.repository.get_user_conversations(user)

    def get_user_project_conversations(self, user, project_id):
        """Fetch user conversations for project"""
        return self.repository.get_user_project_conversations(user, project_id)

    def get_user_archived_conversations(self, user):
        """Fetch user archived conversations"""
        return self.repository.get_user_archived_conversations(user)

    def get_conversation_by_uuid(self, user, conversation_id):
        """Fetch a conversation"""
        conversation = self.repository.get_user_conversation(user, conversation_id)
        if not conversation:
            raise ValueError("Conversation not found")
        return conversation

    def get_conversation_history(self, user, conversation_id):
        """ Fetch a  messages"""
        conversation = self.get_conversation_by_uuid(user, conversation_id)
        messages = self.message_repo.get_threaded_conversation_messages(conversation)
        return conversation, messages

    def update_conversation_title(self, user, conversation_id, new_title):
        """Update conversation title"""
        conversation = self.repository.get_user_conversation(user, conversation_id)
        return self.repository.update_conversation_title(conversation, new_title)

    def delete_conversation(self, user, conversation_id):
        """Delete a user's conversation."""
        conversation = self.repository.get_user_conversation(user, conversation_id)
        self.repository.delete_conversation(conversation)

    def delete_all_conversations(self, user):
        self.repository.delete_all_conversations(user)

    def create_conversation_and_message(self, user, validated_data, custom_gpt=None, public_conversation_id=None):
        """Create a new conversation and message"""
        conversation = self.repository.get_or_create_conversation(user, validated_data)

        if public_conversation_id:
            public_conv = self.get(shareable_uuid=public_conversation_id, is_public=True)
            messages = self.message_repo.filter(conversation=public_conv).order_by("created_at")
            for message in messages:
                self.message_repo.create(
                    conversation=conversation,
                    parent=message.parent,
                    author=message.author,
                    content=message.content,
                    content_type=message.content_type,
                    llm_model=message.llm_model
                )

        # Set custom GPT if provided
        if custom_gpt:
            conversation.custom_gpt = custom_gpt
            conversation.save()

        parent_message = None
        try:
            last_message = self.message_repo.filter(
                conversation=conversation,
                author="assistant"
            ).order_by("-created_at").first()

            if last_message:
                parent_message = last_message
        except Exception as e:
            pass

        user_message, _ = self.message_repo.save_user_message(
            conversation,
            validated_data,
            parent_message=parent_message
        )

        recent_message = self.message_repo.get_recent_messages(conversation) #Get previous chat
        formated_prompt = self.format_messages_for_ai(recent_message, conversation)
        return conversation, user_message, formated_prompt

    def format_messages_for_ai(self, past_messages: list, conversation=None) -> list[dict]:
        """
        Formats past messages and new user input for OpenAI's API.
        Includes custom GPT instructions if specified.
        """
        messages = []

        if conversation and conversation.custom_gpt:
            messages.append({
                "role": "user",
                "content": conversation.custom_gpt.instructions
            })

        # Add past messages
        messages.extend([{"role": msg.author, "content": msg.content} for msg in past_messages])

        return messages

    def save_ai_response(self, conversation, ai_response, user_message, model):
        """Process and save AI response"""
        return self.message_repo.save_assistant_message(conversation, ai_response, user_message, model)

    def grant_access(self, conversation):
        """Make a conversation public"""
        return self.repository.grant_access(conversation)

    def revoke_access(self, conversation):
        """Make a conversation private"""
        return self.repository.revoke_access(conversation)

    def pin_conversation(self, conversation):
        """Pin a conversation"""
        return self.repository.pin_conversation(conversation)

    def unpin_conversation(self, conversation):
        """Unpin a conversation"""
        return self.repository.unpin_conversation(conversation)

    def get_public_conversation_history(self, shareable_uuid):
        """Fetches a public conversation and its messages."""
        conversation = self.repository.get_public_conversation(shareable_uuid)
        if not conversation:
            raise Http404("Conversation not found.")
        messages = self.message_repo.get_threaded_conversation_messages(conversation)
        return conversation, messages

    def archive(self, conversation):
        """Pin a conversation"""
        return self.repository.archive(conversation)

    def unarchive(self, conversation):
        """Unpin a conversation"""
        return self.repository.unarchive(conversation)

    def add_project_to_conversation(self, user, conversation_id, project_id):
        conversation = self.get_conversation_by_uuid(user, conversation_id)
        project = self.project_repo.get_project_by_id(user, project_id)

        if conversation:
            return self.repository.add_to_project(conversation, project)
        return None

    def remove_project_from_conversation(self, user, conversation_id, project_id):
        conversation = self.get_conversation_by_uuid(user, conversation_id)
        project = self.project_repo.get_project_by_id(user, project_id)

        if conversation and conversation.project==project:
            return self.repository.remove_from_project(conversation)
        return None

    def search_user_conversations(self, user, query):
        """
        Search user conversations by title or message content.
        """
        return self.repository.search_conversations(user, query)