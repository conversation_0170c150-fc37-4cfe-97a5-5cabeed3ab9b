from apps.cross_cutting.ai_service import <PERSON>Seek<PERSON><PERSON><PERSON>, OpenAIHandler, LamaAIHandler, OpenAIImageHandler
from apps.llm_manager.services import LLMModelsService

class AIHandlerFactory:
    """Factory to create AI handler instances based on the selected provider."""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.llm_model_service = LLMModelsService()

    def get_handler(self, model_name):
        handlers = {
            "gpt-4o-mini": lambda: OpenAIHandler(model_name),
            "gpt-4o": lambda: OpenAIHandler(model_name),
            "gpt-4": lambda: OpenAIHandler(model_name),
            "deepseek-chat": lambda: <PERSON>Seek<PERSON><PERSON><PERSON>(model_name),
            "llama3.1-70b": lambda: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(model_name),
            "deepseek-r1": lambda: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(model_name),
            "dall-e-3": lambda: <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON>(model_name),
        }
        return handlers.get(model_name, lambda: OpenAIHandler("gpt-4o"))()
