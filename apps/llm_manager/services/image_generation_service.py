import os
from dxh_common.logger import Logger
from dxh_common.base.base_service import BaseService, ServiceError

from apps.llm_manager.repositories import ImageGenerationRepository, MessageRepository
from apps.llm_manager.services import AIHandlerFactory
from apps.user.services import DailyMessageUsageService
from apps.file_manager.tasks import download_generated_image_task
from apps.file_manager.services import FileService
from apps.llm_manager.utils.file_types import is_vision_file_type, is_document_file_type

logger = Logger(__name__)


class ImageGenerationService(BaseService):
    def __init__(self, **kwargs):
        super().__init__(ImageGenerationRepository())
        self.ai_factory_service = AIHandlerFactory()
        self.message_usage_service = DailyMessageUsageService()
        self.message_repository = MessageRepository()
        self.file_service = FileService()

    def generate_image(self, user, prompt, conversation, user_message, size="1024x1024", style="natural"):
        try:
            can_send, current_count, max_limit = self.message_usage_service.check_daily_limit(user)
            if not can_send:
                return None, f"You've hit your daily message limit ({current_count}/{max_limit}). Come back tomorrow or upgrade your plan!"

            image_handler = self.ai_factory_service.get_handler("dall-e-3")

            response = image_handler.generate_image(prompt, size=size, style=style)

            if not response:
                return None, "Failed to generate image. Please try again later."

            try:
                image_url = response.data[0].url
                generated_image = self.repository.create_image(
                    user=user,
                    prompt=prompt,
                    image_url=image_url,
                    size=size,
                    model="dall-e-3",
                )

                if conversation:
                    message_content = f"{image_url}"
                    message = self.message_repository.save_assistant_message(
                        conversation=conversation,
                        response_content=message_content,
                        user_message=user_message,
                        model=None,
                        content_type="image"
                    )

                    message.metadata = {
                        "generated_image": {
                            "id": str(generated_image.uuid),
                            "prompt": prompt,
                            "size": size,
                            "model": "dall-e-3"
                        }
                    }
                    message.save()

                self.message_usage_service.update_daily_usage(user)

                download_generated_image_task(
                    conversation_id=conversation.uuid,
                    message_id=message.uuid,
                    user_id=user.id,
                    image_id=generated_image.id,
                    url=image_url,
                )

                return message, None

            except Exception as db_error:
                logger.error(f"Repository error saving generated image: {db_error}")
                return None, "Error saving the generated image. Please try again."

        except Exception as e:
            logger.error(f"Error generating image: {e}")
            return None, "An unexpected error occurred. Please try again later."

    def get_user_images(self, user):
        try:
            return self.repository.get_user_images(user)
        except Exception as e:
            logger.error(f"Error retrieving user images: {e}")
            raise ServiceError(f"Error retrieving user images: {e}")

    def process_attachments(self, validated_data):
        file_paths = []
        user_message_data = validated_data.get("message", {})

        if user_message_data and "metadata" in user_message_data and "attachments" in user_message_data["metadata"]:
            for attachment in user_message_data["metadata"]["attachments"]:
                try:
                    file_obj = self.file_service.get(id=attachment["id"])
                    if file_obj:
                        # Get file extension to validate file type
                        file_ext = file_obj.file_type.lower() if file_obj.file_type else ""

                        # For image generation, we only process image files for DALL-E
                        # Document files can be processed for prompt enhancement but not sent to DALL-E
                        if is_vision_file_type(file_ext) or is_document_file_type(file_ext):
                            file_paths.append(file_obj.file.url)
                            logger.info(f"Added {file_ext} file for image generation processing")
                        else:
                            logger.warning(f"Skipping unsupported file type '{file_ext}' for image generation")
                    else:
                        logger.warning(f"File with ID {attachment['id']} not found")
                except Exception as e:
                    logger.error(f"Error retrieving file for image generation: {str(e)}")

        return file_paths

    def validate_attachments_for_dalle(self, file_paths):
        """
        Validate that attachments are suitable for DALL-E image generation.
        DALL-E doesn't support file inputs, so we only use attachments for prompt enhancement.

        Returns:
            tuple: (image_files, document_files, validation_message)
        """
        image_files = []
        document_files = []
        unsupported_files = []

        for file_path in file_paths:
            file_ext = os.path.splitext(file_path)[1].lower().replace('.', '')
            if is_vision_file_type(file_ext):
                image_files.append(file_path)
            elif is_document_file_type(file_ext):
                document_files.append(file_path)
            else:
                unsupported_files.append(file_ext)

        validation_message = None
        if unsupported_files:
            validation_message = f"Note: Unsupported file types ({', '.join(unsupported_files)}) were ignored for image generation."

        if image_files or document_files:
            validation_message = f"Processing {len(image_files)} image(s) and {len(document_files)} document(s) for prompt enhancement."

        return image_files, document_files, validation_message

    def enhance_prompt_with_attachments(self, original_prompt, file_paths):
        try:
            image_files, document_files, validation_message = self.validate_attachments_for_dalle(file_paths)

            if validation_message:
                logger.info(validation_message)

            if not image_files and not document_files:
                logger.info("No supported files found for prompt enhancement")
                return original_prompt

            handler = self.ai_factory_service.get_handler("gpt-4o")
            analysis_prompt = [
                {
                    "role": "user",
                    "content": f"""Analyze the attached files and help enhance this image generation prompt: "{original_prompt}"
                    Please provide:
                    1. For images: Describe visual elements, style, composition, colors, objects, and artistic techniques
                    2. For documents: Extract key visual concepts, themes, or descriptions that could inform image generation
                    3. Suggest how these elements could enhance the original prompt.

                    Focus on visual and artistic aspects that would be useful for image generation.

                    IMPORTANT: The enhanced prompt will be used with DALL-E 3, which only accepts text prompts.
                    Do not reference the files directly, but incorporate their visual concepts into the text description."""
                }
            ]

            all_files = image_files + document_files

            response = handler.send_text_request_with_files(
                prompts=analysis_prompt,
                file_paths=all_files,
                stream=False
            )

            if response and hasattr(response, 'choices') and response.choices:
                file_analysis = response.choices[0].message.content

                enhanced_prompt = f"{original_prompt}\n\nBased on the attached files: {file_analysis}"

                logger.info(f"Enhanced prompt with {len(image_files)} images and {len(document_files)} documents")
                return enhanced_prompt

        except Exception as e:
            logger.error(f"Error enhancing prompt with attachments: {str(e)}")

        return original_prompt

    def extract_prompt_details(self, validated_data):
        content = validated_data["message"]["content"]
        prompt = content["parts"][0]
        size = content.get("size") or "1024x1024"
        style = content.get("style") or "natural"
        return prompt, size, style

    def check_daily_image_generation_limit(self, user):
        can_send, _, _ = self.message_usage_service.check_daily_image_generation_limit(user)
        return can_send