from apps.llm_manager.repositories import ProjectRepository


class ProjectService:
    """
    Business logic for managing projects.
    """
    @staticmethod
    def get_project(user, project_id):
        return ProjectRepository.get_project_by_id(user, project_id)
    
    @staticmethod
    def get_projects_for_user(user):
        """Retrieve all projects for the authenticated user."""
        return ProjectRepository.get_user_projects(user)

    @staticmethod
    def create_new_project(user, data):
        """Create a new project with request data."""
        return ProjectRepository.create_project(user, data.get("name"), data.get("description"))

    @staticmethod
    def update_existing_project(user, project_id, data):
        """Update an existing project if the user owns it."""
        project = ProjectRepository.get_project_by_id(user, project_id)

        if project:
            return ProjectRepository.update_project(project, data.get("name"), data.get("description"))

        return None  # Return None if the user is unauthorized or project doesn't exist

    @staticmethod
    def delete_project_if_owner(user, project_id):
        """Delete a project only if the user owns it."""
        project = ProjectRepository.get_project_by_id(user, project_id)

        if project:
            ProjectRepository.delete_project(project)
            return True  # Successful deletion

        return False  # Unauthorized or project not found
