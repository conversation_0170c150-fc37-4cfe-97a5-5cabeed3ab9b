from django.contrib import admin
from common.config import BaseAdmin

from apps.llm_manager.models import ( InteractionHistory, Conversation, ConversationTag, 
Message, LLMModels, Project, CustomGPT
)

# Register your models here.

# --------------------------------------------------------
# CONVERSATION ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(Conversation)
class ConversationAdmin(BaseAdmin):
    list_display = ('user', 'project', 'title', 'custom_gpt', 'is_pinned', 'is_public', 'is_archived', 'last_message_at', 'message_count')
    list_filter = ('is_pinned', 'is_public', 'is_archived', 'last_message_at', 'custom_gpt')
    search_fields = ('title', 'user__email', 'project__name', 'custom_gpt__name')
    ordering = ['-last_message_at','-created_at']

# --------------------------------------------------------
# CONVERSATION TAG ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(ConversationTag)
class ConversationTagAdmin(BaseAdmin):
    list_display = ('conversation', 'tags')
    search_fields = ('conversation__id', 'tags')


# --------------------------------------------------------
# INTERACTION HISTORY ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(InteractionHistory)
class InteractionHistoryAdmin(BaseAdmin):
    list_display = ('message', 'user', 'feedback_type', 'rating')
    list_filter = ('feedback_type',)
    search_fields = ('user__email', 'message__content')

# --------------------------------------------------------
# LLM MODELS ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(LLMModels)
class LLMModelsAdmin(BaseAdmin):
    list_display = ('name', 'code', 'description')
    search_fields = ('name', 'code')

# --------------------------------------------------------
# MESSAGE ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(Message)
class MessageAdmin(BaseAdmin):
    list_display = ('conversation', 'author', 'display_content', 'used_tokens', 'is_edited', 'edited_at')
    list_filter = ('author', 'is_edited')
    search_fields = ('content', 'conversation__title')
    readonly_fields = ('used_tokens',)

    def display_content(self, obj):
        return obj.content[:50]
    display_content.short_description = 'Content'
    display_content.admin_order_field = 'content'


# --------------------------------------------------------
# PROJECT ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(Project)
class ProjectAdmin(BaseAdmin):
    list_display = ('user', 'name', 'description', 'is_favorite')
    search_fields = ('user__email', 'name')

# --------------------------------------------------------
# CUSTOM GPT ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(CustomGPT)
class CustomGPTAdmin(BaseAdmin):
    list_display = ('user', 'name', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__email', 'name', 'instructions')
