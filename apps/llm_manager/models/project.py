from django.db import models
from common.models import BaseModel

class Project(BaseModel):
    """
    Represents a user's project in which they can have multiple conversations.
    """
    user = models.ForeignKey(
        'identity.User', 
        on_delete=models.CASCADE, 
        related_name='projects'
    )  # The owner of the project
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    is_favorite = models.BooleanField(default=False)

    def __str__(self):
        return f"Project: {self.name} (User: {self.user.email})"
