from django.db import models
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel


class GeneratedImage(BaseModel):
    user = models.ForeignKey(
        "identity.User",
        on_delete=models.CASCADE,
        related_name="generated_images",
        verbose_name=_("User")
    )
    prompt = models.TextField(
        verbose_name=_("Prompt")
    )
    image_url = models.TextField(
        verbose_name=_("Image URL")
    )
    file = models.ForeignKey(
        "file_manager.FileUpload",
        on_delete=models.SET_NULL,
        related_name="generated_images",
        null=True,
        blank=True,
        verbose_name=_("Downloaded File")
    )
    size = models.CharField(
        max_length=20, 
        default="1024x1024", 
        verbose_name=_("Size")
    )
    model = models.CharField(
        max_length=50, 
        default="dall-e-3", 
        verbose_name=_("Model")
    )

    class Meta:
        db_table = "llm_manager_generated_images"
        verbose_name = _("Generated Image")
        verbose_name_plural = _("Generated Images")
        ordering = ["-created_at"]
    
    def __str__(self):
        return f"Image for {self.user.email} - {self.created_at}"
