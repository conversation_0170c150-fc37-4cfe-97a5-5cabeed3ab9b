from django.db import models
from common.models import BaseModel


class InteractionHistory(BaseModel):
    """
    Tracks user interactions (e.g., likes/dislikes) with messages.
    """
    message = models.ForeignKey(
        'llm_manager.Message', 
        on_delete=models.CASCADE, 
        related_name='interactions'
    )
    user = models.ForeignKey(
        'identity.User', 
        on_delete=models.CASCADE, 
        related_name='message_interactions'
    )  # User providing feedback
    feedback_type = models.CharField(
        max_length=20,
        choices=[
            ('like', 'Like'),
            ('dislike', 'Dislike'),
            ('rating', 'Rating'),
        ]
    )
    rating = models.PositiveSmallIntegerField(null=True, blank=True)  # Optional for ratings

    def __str__(self):
        return f"Feedback by {self.user}: {self.feedback_type}"
