from django.db import models
from dxh_libraries.translation import gettext_lazy as _
from common.models import BaseModel


class MessageAttachment(BaseModel):
    message = models.ForeignKey(
        'llm_manager.Message',
        on_delete=models.CASCADE,
        related_name='attachments',
        verbose_name=_("Message")
    )
    file = models.ForeignKey(
        'file_manager.FileUpload',
        on_delete=models.CASCADE,
        related_name='message_attachments',
        verbose_name=_("File")
    )
    openai_file_id = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("OpenAI File ID")
    )
    
    class Meta:
        db_table = "llm_manager_message_attachments"
        verbose_name = _("Message Attachment")
        verbose_name_plural = _("Message Attachments")
        
    def __str__(self):
        return f"Attachment for Message {self.message.id}: {self.file.original_name}"
