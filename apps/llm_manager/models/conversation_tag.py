from django.db import models


class ConversationTag(models.Model):
    """
    Tags for categorizing conversations.
    """
    conversation = models.ForeignKey(
        'llm_manager.Conversation', 
        on_delete=models.CASCADE, 
        related_name='tags'
    )
    tags = models.JSONField(default=list, blank=True)

    def __str__(self):
        return f"Tag '{self.tags}' for Conversation {self.conversation.id}"
