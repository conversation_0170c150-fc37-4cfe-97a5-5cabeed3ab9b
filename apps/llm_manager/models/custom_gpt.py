from django.db import models
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel


class CustomGPT(BaseModel):
    user = models.ForeignKey(
        'identity.User', 
        on_delete=models.CASCADE, 
        related_name='custom_gpts',
        verbose_name=_("User")
    )
    name = models.CharField(
        max_length=255,
        verbose_name=_("Name")
    )
    instructions = models.TextField(
        verbose_name=_("Instructions")
    )
    
    def __str__(self):
        return f"{self.name} (by {self.user.email})"
    
    class Meta:
        db_table = "llm_manager_custom_gpts"
        ordering = ["-created_at"]
        verbose_name = _("Custom GPT")
        verbose_name_plural = _("Custom GPTs")
