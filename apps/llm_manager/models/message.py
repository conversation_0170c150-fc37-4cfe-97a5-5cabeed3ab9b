from django.db import models
from dxh_libraries.translation import gettext_lazy as _
from apps.llm_manager.constants import CONTENT_TYPE_CHOICES
from common.constants import RollChoice
from common.models import BaseModel



class Message(BaseModel):
    conversation = models.ForeignKey(
        'llm_manager.Conversation', 
        on_delete=models.SET_NULL, 
        related_name='messages',
        null=True,
        verbose_name=_("Conversation")
    ) 
    llm_model =  models.ForeignKey(
        'llm_manager.LLMModels', 
        on_delete=models.SET_NULL, 
        related_name='messages',
        null=True,
        blank=True,
        verbose_name=_("LLM Model")
    )
    parent = models.ForeignKey(
        'self', 
        on_delete=models.SET_NULL, 
        related_name="children",
        null=True, 
        blank=True,
        verbose_name=_("Parent")
    )
    author = models.CharField(
        max_length=10, 
        choices=RollChoice.choices()  # 'user' or 'assistant'
    ) 
    content = models.TextField(
        verbose_name=_("Content")
    )
    content_type = models.Char<PERSON><PERSON>(
        max_length=10, 
        choices=CONTENT_TYPE_CHOICES, 
        default=CONTENT_TYPE_CHOICES[0][0],
        verbose_name=_("Content Type")
    )
    used_tokens = models.IntegerField(
        null=True, 
        blank=True,
        verbose_name=_("Used Tokens")
    )
    metadata = models.JSONField(
        default=dict, 
        blank=True,
        verbose_name=_("Metadata")
    )
    
    is_edited = models.BooleanField(
        default=False,
        verbose_name=_("Is Edited")
    )
    edited_at = models.DateTimeField(
        null=True, 
        blank=True,
        verbose_name=_("Edited At")
    )

    def __str__(self):
        return f"{self.author.capitalize()} at {self.created_at}: {self.content[:50]}"
