import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from apps.llm_manager.managers import ConversationManager
from common.models import BaseModel

class Conversation(BaseModel):
    """
    Represents a conversation session between a user and ChatGPT.
    """

    user = models.ForeignKey(
        'identity.User',
        on_delete=models.CASCADE,
        related_name='conversations',
        null=True,
        blank=True
    )  # Optional link to a User
    project = models.ForeignKey(
        'llm_manager.Project',
        on_delete=models.CASCADE,
        related_name='conversations',
        null=True,
        blank=True
    )
    title = models.CharField(max_length=255, blank=True, null=True)

    pinned_at = models.DateTimeField(null=True, blank=True)
    is_pinned = models.BooleanField(default=False)

    is_public = models.BooleanField(default=False)
    shareable_uuid = models.UUIDField(blank=True, null=True)

    is_archived = models.BooleanField(default=False)
    archived_at = models.DateTimeField(null=True, blank=True)

    last_message_at = models.DateTimeField(null=True, blank=True)
    message_count = models.IntegerField(default=0)
    metadata = models.JSONField(default=dict, blank=True)

    custom_gpt = models.ForeignKey(
        'llm_manager.CustomGPT',
        on_delete=models.SET_NULL,
        related_name='conversations',
        null=True,
        blank=True,
        verbose_name=_("Custom GPT")
    )

    objects = ConversationManager()

    def __str__(self):
        return f"Conversation: {self.uuid} Title: {self.title}"
