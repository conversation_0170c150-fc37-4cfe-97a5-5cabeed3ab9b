from django.db import models
from common.models import BaseModel
from django.utils.translation import gettext_lazy as _

class LLMModels(BaseModel):
    name = models.CharField(max_length=100, unique=True, db_index=True, verbose_name=_("Model Name"))  
    code = models.CharField(max_length=100, unique=True, db_index=True, verbose_name=_("Model Code"))
    description = models.TextField(verbose_name=_("Model Description"), null=True, blank=True)  
    class Meta:
        verbose_name = _("LLM Model")
        verbose_name_plural = _("LLM Models")
    
    def __str__(self):
        return f"{self.name} - ({self.code})"
