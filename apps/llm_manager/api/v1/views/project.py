from apps.llm_manager.api.v1.serializers import ProjectSerializer
from apps.llm_manager.services import ProjectService
from rest_framework import viewsets, permissions
from dxh_libraries.rest_framework import Response, status
from common.paginations import CustomPagination


class ProjectViewSet(viewsets.ViewSet):
    """
    Handles API requests for Projects.
    """
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'uuid' 
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.project_service = ProjectService() 
        self.paginator = CustomPagination()
        
    def list(self, request):
        """GET /api/projects/ - Get all projects for the authenticated user with pagination."""
        projects = self.project_service.get_projects_for_user(request.user)
        paginated_projects = self.paginator.paginate_queryset(projects, request)

        serializer = ProjectSerializer(paginated_projects, many=True)
        
        result = {
            "message": "Projects retrived successfully",
            "data": self.paginator.get_paginated_response(data=serializer.data).data 
        }
        return Response(result, status=status.HTTP_201_CREATED)

    def create(self, request):
        """POST /api/projects/ - Create a new project."""
        project = self.project_service.create_new_project(request.user, request.data)
        serializer = ProjectSerializer(project)
        result = {
            "message": "Project created successfully",
            "data": serializer.data
        }
        return Response(result, status=status.HTTP_201_CREATED)

    def retrieve(self, request, uuid=None):
        """GET /api/projects/{uuid}/ - Get a specific project."""
        project = self.project_service.get_project(request.user, project_id=uuid)

        if not project:
            result = {
                "message": "Project not found or unauthorized",
            }
            return Response(result, status=status.HTTP_404_NOT_FOUND)

        serializer = ProjectSerializer(project)
        result = {
            "message": "Project retrieved successfully",
            "data": serializer.data
        }
        return Response(result)

    def update(self, request, uuid=None):
        """PUT /api/projects/{uuid}/ - Update a project."""
        project = self.project_service.update_existing_project(request.user, project_id=uuid, data=request.data)

        if not project:
            result = {
                "message": "Project not found or unauthorized",
            }
            return Response(result, status=status.HTTP_403_FORBIDDEN)

        serializer = ProjectSerializer(project)
        result = {
            "message": "Project updated successfully",
            "data": serializer.data
        }
        return Response(result)

    def destroy(self, request, uuid=None): 
        """DELETE /api/projects/{uuid}/ - Delete a project."""
        success = self.project_service.delete_project_if_owner(request.user, project_id=uuid)

        if not success:
            result = {
                "message": "Project not found or unauthorized",
            }
            return Response(result, status=status.HTTP_403_FORBIDDEN)

        result = {
            "message": "Project deleted successfully",
        }
        return Response(result, status=status.HTTP_204_NO_CONTENT)
