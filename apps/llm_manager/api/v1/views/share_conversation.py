from django.conf import settings
from django.http import Http404
from dxh_libraries.rest_framework import status, Response
from dxh_common.base.base_api_view import BaseApiView, PublicApiView
from dxh_common.logger import Logger

from apps.llm_manager.utils.response_formater import format_ai_response
from apps.llm_manager.services import ConversationService
from apps.cross_cutting import EmailService

logger = Logger(__name__)

class ShareConversationView(BaseApiView):
    """
    Handles sharing and unsharing of conversations.
    """
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.conversation_service = ConversationService()

    def post(self, request, conversation_id):
        """Enable or disable public sharing of a conversation."""
        grant_access = request.data.get("grant_access", True)
        user_email = request.user.email
        try:
            conversation = self.conversation_service.get_conversation_by_uuid(request.user, conversation_id)

            if grant_access:
                updated_conversation = self.conversation_service.grant_access(conversation)
                result = {
                    "data": {
                        "shareable_uuid": updated_conversation.shareable_uuid
                    }
                }
                return Response(
                    result,
                    status=status.HTTP_200_OK
                )
            else:
                self.conversation_service.revoke_access(conversation)
                return Response({"message": "Conversation is now private"}, status=status.HTTP_200_OK)

        except Http404:
            return Response({"message": "Conversation not found."}, status=status.HTTP_404_NOT_FOUND)


class PublicConversationView(PublicApiView):
    """
    Handles fetching publicly shared conversations.
    """
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.conversation_service = ConversationService()

    def get(self, request, shareable_uuid):
        """
        Fetch a publicly shared conversation in parent-child chain order.
        Starts with the first message (no parent) and follows the chain of children.
        """
        try:
            conversation, messages = self.conversation_service.get_public_conversation_history(shareable_uuid)

            result = {
                "data": {
                    "title": conversation.title,
                    "history": [format_ai_response(msg, 'is_public') for msg in messages]
                }
            }
            return Response(
                result,
                status=status.HTTP_200_OK
            )

        except Http404:
            return Response({"message": "Conversation not found."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error retrieving public conversation: {str(e)}")
            return Response(
                {"message": "Error retrieving conversation. Please try again."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )