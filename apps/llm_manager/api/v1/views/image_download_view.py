from dxh_libraries.rest_framework import status, Response
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.logger import Logger
from django.utils.translation import gettext_lazy as _

from apps.llm_manager.services import ImageGenerationService
from apps.llm_manager.api.v1.serializers import GeneratedImageSerializer

logger = Logger(__name__)

class ImageDownloadView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.image_generation_service = ImageGenerationService()

    def get(self, request):
        try:
            image_id = request.GET.get('image_id')

            if not image_id:
                result = {
                    "message": "Image ID is required"
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            status_only = request.GET.get('status_only', 'false').lower() == 'true'
            image = self.image_generation_service.get_image(image_id)

            if not image:
                result = {
                    "message": "Image not found or has expired"
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            if image.user != request.user:
                result = {
                    "message": "You don't have permission to access this image"
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)

            if status_only:
                serializer = GeneratedImageSerializer(image, context={'request': request})
                result = {
                    "message": "Image ready for download",
                    "data": serializer.data
                }
                return Response(result, status=status.HTTP_200_OK)

            filename = request.GET.get('filename')

            file_response, error = self.image_generation_service.download_image(image, filename)
            if error:
                result = {
                    "message": f"Error downloading image: {error}"
                }
                return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            return file_response

        except Exception as e:
            logger.error({"event": "ImageDownloadView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e