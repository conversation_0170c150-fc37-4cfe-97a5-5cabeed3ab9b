import json
import uuid
from django.core.cache import cache
from django.http import StreamingHttpResponse
from dxh_common.base.base_api_view import BaseApiView, PublicApiView
from dxh_common.logger import Logger
from dxh_libraries.rest_framework import Response, status
from dxh_libraries.translation import gettext_lazy as _

from apps.core.services.system_setting_service import SystemSettingService
from apps.file_manager.services import FileService
from apps.identity.utils.get_user_meta_data import get_device_info
from apps.llm_manager.api.v1.serializers import PromptSerializer
from apps.llm_manager.constants import (
    BLOCK_TIME_FOR_USER,
    MAX_CHAT_LIMIT_FOR_ANNONYMOUS_USER,
)
from apps.llm_manager.services import (
    AIHandlerFactory,
    ConversationService,
    CustomGPTService,
    LLMModelsService,
)
from apps.llm_manager.utils.file_types import (
    SUPPORTED_FILE_TYPES,
    get_unsupported_files,
    is_supported_file_type,
)
from apps.llm_manager.utils.response_formater import format_ai_response
from apps.llm_manager.utils.token_usages_policy import TokenUsagePolicyFactory
from apps.payment.services import PlanService, SubscriptionService
from apps.user.services import DailyMessageUsageService, DailyTokenUsagesService

logger = Logger(__name__)


class AIConversationView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.ai_factory_service = AIHandlerFactory()
        self.llm_model_service = LLMModelsService()
        self.conversation_service = ConversationService()
        self.message_usage_service = DailyMessageUsageService()
        self.token_usages_service = DailyTokenUsagesService()
        self.token_usage_policy_factory = TokenUsagePolicyFactory()
        self.plan_service = PlanService()
        self.subscription_service = SubscriptionService()
        self.custom_gpt_service = CustomGPTService()
        self.file_service = FileService()

    def post(self, request):
        payload = request.data
        user = request.user
        serializer = PromptSerializer(data=payload)
        if not serializer.is_valid():
            first_error_message = serializer.errors[next(iter(serializer.errors))][0]
            result = {
                "message": first_error_message,
                "errors": serializer.errors
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        valid_data = serializer.validated_data
        public_conversation_id = valid_data.get("public_conversation_id", None)
        code = valid_data.get("model")
        custom_gpt_id = valid_data.get("custom_gpt_id")
        user_message_data = valid_data.get("message", {})

        model = self.llm_model_service.get(code=code)
        if not model:
            result = {
                "message": "Invalid AI model selected"
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        model, total_used_tokens, token_limit = self._check_daily_limit(user, model)
        try:
            custom_gpt = self._get_custom_gpt(custom_gpt_id, user)
        except PermissionError as e:
            return Response({"message": str(e)}, status=status.HTTP_403_FORBIDDEN)
        except ValueError as e:
            return Response({"message": str(e)}, status=status.HTTP_404_NOT_FOUND)

        conversation, user_message, prompt_data = self.conversation_service.create_conversation_and_message(
            user, valid_data, custom_gpt, public_conversation_id
        )

        ai_handler = self.ai_factory_service.get_handler(model.code)
        input_tokens = self._count_tokens(prompt_data, ai_handler, user_message, model)
        ai_handler = self._model_fallback(user, model, total_used_tokens, token_limit, input_tokens)
        file_paths = self._get_file_paths(user_message_data)

        try:
            if file_paths:
                logger.info(f"Sending request with {len(file_paths)} file attachments")
                ai_response = ai_handler.send_text_request_with_files(prompt_data, file_paths)
            else:
                ai_response = ai_handler.send_text_request(prompt_data)

            if ai_response is None:
                return Response({"message": "Error processing files or retrieving AI response"}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error sending request to AI: {str(e)}")
            return Response({"message": f"Error: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)

        ai_response_obj = self.conversation_service.save_ai_response(conversation, "", user_message, model)
        def generate():
            output_tokens = 0
            full_content = []  # Ensure full_content is always initialized
            error_occurred = False

            try:
                yield json.dumps({"data": format_ai_response(ai_response_obj)}) + "\n"

                for chunk in ai_response:
                    try:
                        if chunk.choices[0].delta.content:
                            content_chunk = chunk.choices[0].delta.content
                            full_content.append(content_chunk)

                            output_tokens += ai_handler.count_tokens([{"role": "assistant", "content": content_chunk}])

                            yield json.dumps({"data": {"chunk": content_chunk}}) + "\n"
                    except Exception as chunk_error:
                        logger.error(f"Error processing chunk: {chunk_error}")
                        error_occurred = True
                        break

                if not error_occurred:
                    yield json.dumps({"data": ["DONE"]}) + "\n"

            except Exception as e:
                logger.error(f"Streaming error: {e}")
                error_occurred = True

            finally:
                try:
                    ai_response_obj.content = "".join(full_content) if full_content else ""
                    ai_response_obj.used_tokens = output_tokens
                    ai_response_obj.save()

                    message_usage = self.message_usage_service.update_daily_usage(user)
                    token_usage = self.token_usages_service.update_daily_usages(user, input_tokens, output_tokens)

                    logger.info(f"Updated usage for user {user.id}: messages={message_usage.message_count}, tokens={token_usage} (input={input_tokens}, output={output_tokens})")
                except Exception as final_error:
                    logger.error(f"Error finalizing AI response: {final_error}")

                if error_occurred:
                    yield json.dumps({"data": ["ERROR"]}) + "\n"

        return StreamingHttpResponse(generate(), content_type="application/json")
    
    def _model_fallback(self, user, model, total_used_tokens, token_limit, input_tokens):
        try:
            if total_used_tokens + input_tokens > token_limit and model.code != "gpt-4o-mini":
                model = self.llm_model_service.get(code="gpt-4o-mini")
                if not model:
                    result = {
                        "message": f"You've exceeded your daily token limit ({total_used_tokens}/{token_limit}). Please upgrade your plan to continue using premium models."
                    }
                    return Response(result, status=status.HTTP_403_FORBIDDEN)

        except Exception as e:
            logger.warning(f"Token usage check exception for user {user.id}: {str(e)}")
            model = self.llm_model_service.get(code="gpt-4o-mini")
            if not model:
                result = {
                    "message": "An error occurred. Please try again with a different model."
                }
                return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        ai_handler = self.ai_factory_service.get_handler(model.code)
        return ai_handler

    def _count_tokens(self, prompt_data, ai_handler, user_message, model):
        input_tokens = ai_handler.count_tokens(prompt_data)
        user_message.used_tokens = input_tokens
        user_message.llm_model = model
        user_message.save()

        return input_tokens

    def _check_daily_limit(self, user, model):
        can_send, current_count, max_limit = self.message_usage_service.check_daily_limit(user)
        logger.info(f"Message limit check for user {user.id}: can_send={can_send}, count={current_count}, limit={max_limit}, model={model.code}")

        total_used_tokens = self.token_usages_service.get_monthly_token_usage(user)

        if total_used_tokens is None:
            total_used_tokens = self.token_usages_service.get_monthly_token_usage(user)
            free_plan = self.plan_service.get(code="free")
            token_limit = free_plan.tokens_included if free_plan else 50000
        else:
            subscription = self.subscription_service.get(user=user, status='active')
            token_limit = subscription.plan.tokens_included if subscription and subscription.plan else 50000

        token_limit_exceeded = total_used_tokens >= token_limit
        if not can_send and token_limit_exceeded:
            result = {
                "message": f"You've hit both your daily message limit ({current_count}/{max_limit}) and your daily token limit ({total_used_tokens}/{token_limit}). Please upgrade your plan to continue using our services."
            }
            return Response(result, status=status.HTTP_429_TOO_MANY_REQUESTS)

        elif not can_send and not token_limit_exceeded:
            if model.code != "gpt-4o-mini":
                model = self.llm_model_service.get(code="gpt-4o-mini")
                if not model:
                    result = {
                        "message": f"You've hit your daily message limit ({current_count}/{max_limit}). Come back tomorrow or upgrade your plan to keep the conversation going!"
                    }
                    return Response(result, status=status.HTTP_429_TOO_MANY_REQUESTS)
            else:
                result = {
                    "message": f"You've hit your daily message limit ({current_count}/{max_limit}). Come back tomorrow or upgrade your plan to keep the conversation going!"
                }
                return Response(result, status=status.HTTP_429_TOO_MANY_REQUESTS)

        return model, total_used_tokens, token_limit

    def _get_custom_gpt(self, custom_gpt_id, user):
        if user.subscription_type == "free":
            raise PermissionError(_("Custom GPTs are only available for premium users"))
        
        if custom_gpt_id:
            custom_gpt = self.custom_gpt_service.get(uuid=custom_gpt_id, user=user)
            if not custom_gpt:
                raise ValueError("Custom GPT not found")
            return custom_gpt
        return None

    def _get_file_paths(self, user_message_data):
        file_paths = []
        file_types = []
        if user_message_data and "metadata" in user_message_data and "attachments" in user_message_data["metadata"]:
            for attachment in user_message_data["metadata"]["attachments"]:
                try:
                    file_obj = self.file_service.get(id=attachment["id"])
                    # file_paths.append("https://cdn.pioo.ai/temp.jpg")
                    file_paths.append(file_obj.file.url)
                    file_ext = file_obj.file_type.lower()
                    file_types.append(file_ext)
                except Exception as e:
                    logger.error(f"Error retrieving file: {str(e)}")


            unsupported_files = get_unsupported_files(file_types)
            if file_types:
                unsupported_files = get_unsupported_files(file_types)
                if unsupported_files:
                    error_msg = f"Unsupported file type(s): {', '.join(unsupported_files)}. Supported types: {', '.join(SUPPORTED_FILE_TYPES.keys())}"
                    logger.warning(error_msg)
                    return Response({"message": error_msg}, status=status.HTTP_400_BAD_REQUEST)
            return file_paths


class AnonymousAIConversationView(PublicApiView):
    """
    Handles AI conversations for users who are not logged in.
    Stores conversation history in Redis.
    """
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.llm_model_service = LLMModelsService()
        self.ai_factory_service = AIHandlerFactory()
        self.system_setting_service = SystemSettingService()
        self.plan_service = PlanService()

    def post(self, request):
        """
        Handles user input and retrieves AI response while maintaining context in Redis.
        """
        serializer = PromptSerializer(data=request.data)
        if not serializer.is_valid():
            first_error_message = serializer.errors[next(iter(serializer.errors))][0]
            result = {
                "message": first_error_message,
                "errors": serializer.errors
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        valid_data = serializer.validated_data

        ip_address = request.META.get('REMOTE_ADDR', 'unknown')
        device_info, _ = get_device_info(request)

        # Generate a unique cache key
        cache_key = f"chat_limit:{ip_address}:{device_info}"
        chat_count = cache.get(cache_key, 0)
        # Check if the device/IP exceeded the limit
        if chat_count >= MAX_CHAT_LIMIT_FOR_ANNONYMOUS_USER:
            return Response(
                {"message": f"Daily message limit reached ({chat_count}/{MAX_CHAT_LIMIT_FOR_ANNONYMOUS_USER}). Please try again tomorrow."},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )

        user_prompt = valid_data.get("message")
        code = valid_data.get("model")
        model = self.llm_model_service.get(code=code)
        if not model:
            return Response({"message": "Invalid AI model selected"}, status=400)

        # For anonymous users, restrict to gpt-4o-mini if not already selected
        if model.code != "gpt-4o-mini":
            model = self.llm_model_service.get(code="gpt-4o-mini")
            if not model:
                return Response({"message": "Default model not available"}, status=400)

        # Get conversation ID from request (or generate a new one)
        conversation_id = valid_data.get("conversation_id") or str(uuid.uuid4())
        msg_id = str(uuid.uuid4())
        # Retrieve existing conversation from Redis
        redis_key = f"chat:{conversation_id}"
        conversation_history = cache.get(redis_key, [])

        # Append new user message
        message_data = {
            "role": "user",
            "content": user_prompt["content"]["parts"][0],
        }
        conversation_history.append(message_data)

        # Send last 3 conversation history to AI
        ai_handler = self.ai_factory_service.get_handler(model.code)

        # Count tokens for anonymous users too
        input_tokens = ai_handler.count_tokens(conversation_history[-3:])

        # Check token usage for anonymous users
        token_key = f"token_usage:{ip_address}:{device_info}"
        current_usage = cache.get(token_key, {"input": 0, "output": 0, "total": 0})

        # Get token limit from free plan for anonymous users
        free_plan = self.plan_service.get(code="free")
        anon_token_limit = free_plan.tokens_included // 10 if free_plan else 5000  # 10% of free plan limit

        # Check if anonymous user has exceeded token limit
        if current_usage["total"] + input_tokens > anon_token_limit:
            result = {
                "message": f"You've reached the token limit for anonymous users ({anon_token_limit} tokens). Please sign up to continue using our AI services."
            }
            return Response(result, status=status.HTTP_403_FORBIDDEN)

        # Anonymous users cannot upload files, but we'll add a check just in case
        user_message_data = valid_data.get("message", {})
        if user_message_data and "metadata" in user_message_data and "attachments" in user_message_data["metadata"]:
            result = {
                "message": "File uploads are not supported for anonymous users. Please sign up to use this feature."
            }
            return Response(result, status=status.HTTP_403_FORBIDDEN)
        
        # Send request to AI
        ai_response = ai_handler.send_text_request(conversation_history[-3:])
        if not ai_response:
            return Response({"message": "Error retrieving AI response"}, status=status.HTTP_400_BAD_REQUEST)

        def generate():
            output_tokens = 0
            full_content = []

            yield json.dumps({"data": format_ai_response("", from_redis=True, conversation_id=conversation_id, msg_id=msg_id)}) + "\n"

            for chunk in ai_response:
                if chunk.choices[0].delta.content:
                    content_chunk = chunk.choices[0].delta.content
                    full_content.append(content_chunk)
                    # Count tokens for each chunk
                    output_tokens += ai_handler.count_tokens([{"role": "assistant", "content": content_chunk}])
                    yield json.dumps({"data": {"chunk": content_chunk }}) + "\n"

            yield json.dumps({"data": ["DONE"]}) + "\n"

            # Save the full response in Redis
            full_response_text = "".join(full_content)
            ai_message = {"role": "assistant", "content": full_response_text}
            conversation_history.append(ai_message)
            system_setting = self.system_setting_service.get()

            # Store token counts in Redis for anonymous users
            token_key = f"token_usage:{ip_address}:{device_info}"
            current_usage = cache.get(token_key, {"input": 0, "output": 0, "total": 0})
            current_usage["input"] += input_tokens
            current_usage["output"] += output_tokens
            current_usage["total"] = current_usage["input"] + current_usage["output"]
            cache.set(token_key, current_usage, timeout=BLOCK_TIME_FOR_USER)
            logger.info(f"Anonymous user token usage updated: {current_usage}")

            cache.set(redis_key, conversation_history, timeout=system_setting.cache_expiry_time)

        cache.set(cache_key, chat_count + 1, timeout=BLOCK_TIME_FOR_USER)

        return StreamingHttpResponse(generate(), content_type="application/json")
