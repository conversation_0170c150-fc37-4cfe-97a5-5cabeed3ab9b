from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import status, Response
from dxh_common.logger import Logger

from apps.llm_manager.services import LLMModelsService
from apps.llm_manager.api.v1.serializers import AIModelsSerializer

logger = Logger(__name__)


class LLMModelListView(BaseApiView):
    """
    Handles archiving and unarchiving of conversations.
    """
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.llm_model_service = LLMModelsService()

    def get(self, request):
        try:
            models = self.llm_model_service.get_all().order_by("created_at")
            result = {
                "message": "Models Retrieved Successfully",
                "data": AIModelsSerializer(models, many=True).data
            }

            return Response(result, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error({"event": "LLMModelListView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e