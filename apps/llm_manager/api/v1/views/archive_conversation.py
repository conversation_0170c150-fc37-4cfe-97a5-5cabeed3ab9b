from dxh_libraries.rest_framework import status, Response
from dxh_common.base.base_api_view import BaseApiView
from django.http import Http404
from dxh_common.logger import Logger
from apps.llm_manager.services import ConversationService

logger = Logger(__name__)


class ArchiveConversationView(BaseApiView):
    """
    Handles archiving and unarchiving of conversations.
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.conversation_service = ConversationService()

    def post(self, request, conversation_id):
        """Pin or unpin a conversation based on the request payload."""
        archive_status = request.data.get("archive", True)

        try:
            conversation = self.conversation_service.get_conversation_by_uuid(request.user, conversation_id)

            if archive_status:
                self.conversation_service.archive(conversation)
                result = {
                    "message": "Conversation archived successfully"
                }
                return Response(result)
            
            else:
                self.conversation_service.unarchive(conversation)
                result = {
                    "message": "Conversation unarchive successfully"
                }
                return Response(result)

        except Exception as e:
            raise e
