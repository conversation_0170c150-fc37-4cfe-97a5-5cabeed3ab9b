from dxh_libraries.rest_framework import status, Response
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.logger import Logger

from common.paginations import CustomPagination
from apps.llm_manager.api.v1.serializers import ConversationSerializer
from apps.llm_manager.utils.response_formater import format_ai_response
from apps.llm_manager.services import ConversationService
from apps.cross_cutting import EmailService

logger = Logger(__name__)


class ConversationListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.conversation_service = ConversationService()
        self.paginator = CustomPagination()

    def get(self, request):
        """Retrieve a paginated list of conversations for the authenticated user."""
        project_id = request.query_params.get('project_id')
        archived = request.query_params.get('archived', 'false').lower() == 'true'

        if archived:
            conversations = self.conversation_service.get_user_archived_conversations(request.user)
        elif project_id:
            conversations = self.conversation_service.get_user_project_conversations(request.user, project_id=project_id)
        else:
            conversations = self.conversation_service.get_user_conversations(request.user)
        serialized_data = ConversationSerializer(conversations, many=True).data
        paginated_conversations = self.paginator.paginate_queryset(serialized_data, request)
        paginated_response = self.paginator.get_paginated_response(paginated_conversations)
        result = {
            "data": paginated_response.data
        }

        return Response(result, status=status.HTTP_200_OK)

class DeleteAllConversationsView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.conversation_service = ConversationService()
        self.email_service = EmailService()

    def delete(self, request):
        """Delete all conversations and send an email notification to the user."""
        user_email = request.user.email
        context = {
            'user': user_email
        }
        self.conversation_service.delete_all_conversations(request.user)

        self.email_service.send_email(
            to_email=user_email,
            subject="All Conversations Deleted",
            template_name="delete_all_conversations",
            context=context,
        )

        return Response({"message": "All conversations deleted successfully."}, status=status.HTTP_200_OK)


class ConversationDetailView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.conversation_service = ConversationService()

    def get(self, request, conversation_id):
        """
        Retrieve conversation history in parent-child chain order.
        Starts with the first message (no parent) and follows the chain of children.
        """
        try:
            conversation, messages = self.conversation_service.get_conversation_history(request.user, conversation_id)

            result = {
                "data":{
                    "title": conversation.title,
                    "history": [format_ai_response(msg) for msg in messages]
                }
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error retrieving conversation: {str(e)}")
            return Response(
                {"message": "Error retrieving conversation. Please try again."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def patch(self, request, conversation_id):
        """Update the conversation title."""
        new_title = request.data.get("title")
        if not new_title:
            return Response({"message": "Title is required."}, status=status.HTTP_400_BAD_REQUEST)

        updated_conversation = self.conversation_service.update_conversation_title(request.user, conversation_id, new_title)
        return Response({"message": "Title updated successfully.", "title": updated_conversation.title}, status=status.HTTP_200_OK)

    def delete(self, request, conversation_id):
        """Delete a conversation and send an email notification to the user."""
        user_email = request.user.email
        context={
            'user': user_email,
            'conversation_id': conversation_id
        }
        self.conversation_service.delete_conversation(request.user, conversation_id)
        return Response({"message": "Conversation deleted successfully."}, status=status.HTTP_200_OK)