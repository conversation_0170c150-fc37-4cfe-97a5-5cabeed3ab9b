from dxh_libraries.rest_framework import status, Response
from dxh_common.base.base_api_view import BaseApiView
from apps.llm_manager.services import ConversationService

class ProjectConversationView(BaseApiView):
    conversation_service = ConversationService()

    def post(self, request, conversation_id):
        """Add project to conversation"""
        try:
            project_id = request.data.get('project_id')
            if not project_id:
                return Response(
                    {'error': 'project_id is required'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            conversation = self.conversation_service.add_project_to_conversation(
                user=request.user,
                conversation_id=conversation_id,
                project_id=project_id
            )

            if conversation:
                result = {
                    "message": "Conversation added successfully"
                }
                return Response(result)
            
            result = {
                "message":"Conversation or Project not found"
            }
            return Response(result)

        except Exception as e:
            raise e

    def delete(self, request, conversation_id):
        """Remove project from conversation"""
        try:
            project_id = request.data.get('project_id')
            if not project_id:
                return Response(
                    {'error': 'project_id is required'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            conversation = self.conversation_service.remove_project_from_conversation(
                user=request.user,
                conversation_id=conversation_id,
                project_id=project_id
            )

            if conversation:
                result = {
                    "message": "Conversation removed successfully"
                }
                return Response(result)
            
            result = {
                "message":"Conversation or Project not found"
            }
            return Response(result)
        
        except Exception as e:
            raise e
