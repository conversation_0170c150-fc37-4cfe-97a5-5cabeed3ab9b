from django.http import Http404
from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import status, Response
from dxh_common.logger import Logger

from apps.llm_manager.services import ConversationService

logger = Logger(__name__)


class PinConversationView(BaseApiView):
    """
    Handles pinning and unpinning of conversations.
    """
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.conversation_service = ConversationService()

    def post(self, request, conversation_id):
        """Pin or unpin a conversation based on the request payload."""
        pin_status = request.data.get("pin", True)

        try:
            conversation = self.conversation_service.get_conversation_by_uuid(request.user, conversation_id)

            if pin_status:
                updated_conversation = self.conversation_service.pin_conversation(conversation)
                return Response({"message": "Conversation pinned successfully."}, status=status.HTTP_200_OK)
            else:
                updated_conversation = self.conversation_service.unpin_conversation(conversation)
                return Response({"message": "Conversation unpinned successfully."}, status=status.HTTP_200_OK)

        except Http404:
            return Response({"message": "Conversation not found."}, status=status.HTTP_404_NOT_FOUND)
