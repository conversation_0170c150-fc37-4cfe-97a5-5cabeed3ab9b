from django.urls import path
from apps.llm_manager.api.v1 import views
from django.urls import path, include
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register(r'projects', views.ProjectViewSet, basename='project')

urlpatterns = [
    path('', include(router.urls)),
    path('projects/<str:uuid>/',views.ProjectViewSet.as_view({'get': 'retrieve', 'put': 'update', 'delete': 'destroy'})),
    # AI Chat Generation
    path('chats/', views.AIConversationView.as_view(), name='ai-conversation'),
    path('anonymous/chats/', views.AnonymousAIConversationView.as_view(), name='anonymous-ai-conversation'),

    # List & Retrieve Conversations
    path('chats/list/', views.ConversationListView.as_view(), name='conversation-list'),
    path('chats/<conversation_id>/messages/', views.ConversationDetailView.as_view(), name='conversation-history'),
    path('chats/delete-all/', views.DeleteAllConversationsView.as_view(), name='delete-all-conversations'),

    # Sharing & Public Access
    path('chats/<conversation_id>/share/', views.ShareConversationView.as_view(), name='share-conversation'),
    path('chats/public/<shareable_uuid>/', views.PublicConversationView.as_view(), name='public-conversation'),

    # Pin/Unpin Conversations
    path('chats/<conversation_id>/pin/', views.PinConversationView.as_view(), name='pin-conversation'),

    # Archive/Unarchive Conversations
    path('chats/<conversation_id>/archive/', views.ArchiveConversationView.as_view(), name='archive-conversation'),
    path('chats/<conversation_id>/add-to-project/', views.ProjectConversationView.as_view(), name='add-to-project'),
    # Chat Search
    path('search/', views.ChatSearchView.as_view(), name='chat-search'),
    #Ai model list
    path('models/', views.LLMModelListView.as_view(), name='model-list'),

    # Custom GPTs
    path('custom-gpts/', views.CustomGPTView.as_view(), name='custom-gpts-list-create'),
    path('custom-gpts/<int:id>/', views.CustomGPTView.as_view(), name='custom-gpts-detail'),

    # Image Generation
    path('images/generation/', views.ImageGenerationView.as_view(), name='image-generation'),
]
