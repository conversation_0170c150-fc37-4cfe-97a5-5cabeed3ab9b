from django.conf import settings
from dxh_libraries.rest_framework import serializers
from dxh_common.base.base_serializer import BaseModelSerializer, BaseSerializer
from apps.llm_manager.models import Conversation, LLMModels, Project, MessageAttachment
from apps.file_manager.models import FileUpload


class AuthorSerializer(BaseSerializer):
    role = serializers.CharField()


class MessageContentSerializer(BaseSerializer):
    content_type = serializers.CharField()
    parts = serializers.ListField(child=serializers.CharField())


class AttachmentSerializer(BaseSerializer):
    id = serializers.IntegerField()
    file_url = serializers.URLField(required=False, allow_null=True)
    file_type = serializers.CharField(required=False, allow_null=True)
    original_name = serializers.CharField(required=False, allow_null=True)
    size = serializers.IntegerField(required=False, allow_null=True)
    created_at = serializers.DateTimeField(required=False, allow_null=True)

class MessageMetadataSerializer(BaseSerializer):
    attachments = serializers.ListField(child=AttachmentSerializer(), required=False)

class MessageSerializer(BaseSerializer):
    id = serializers.CharField(required=False, allow_null=True)
    author = AuthorSerializer()
    content = MessageContentSerializer()
    metadata = MessageMetadataSerializer(required=False)

class PromptSerializer(BaseSerializer):
    conversation_id = serializers.CharField(required=False, allow_null=True)
    project_id = serializers.CharField(required=False, allow_null=True)
    public_conversation_id = serializers.CharField(required=False, allow_null=True)
    model = serializers.CharField(required=False)
    action = serializers.CharField(required=False)
    timezone = serializers.CharField(required=False)
    timezone_offset_min = serializers.IntegerField(required=False)
    websocket_request_id = serializers.CharField(required=False)
    parent_message_id = serializers.CharField(required=False)
    custom_gpt_id = serializers.CharField(required=False, allow_null=True)
    message = MessageSerializer(required=False, allow_null=True)

class ConversationSerializer(BaseModelSerializer):
    class Meta:
        model = Conversation
        fields = '__all__'
        read_only_fields = ["uuid", "created_at", "updated_at"]

class ProjectSerializer(BaseModelSerializer):
    class Meta:
        model = Project
        fields = '__all__'
        read_only_fields = ['user']  # Ensure user is automatically set

class ChatSearchSerializer(BaseModelSerializer):
    payload = serializers.SerializerMethodField()

    class Meta:
        model = Conversation
        fields = ['uuid', 'title', 'created_at', 'updated_at', 'is_pinned', 'is_archived', 'payload']

    def get_payload(self, obj):
        """
        Extract the last message and provide a snippet.
        """
        last_message = obj.messages.order_by('-created_at').first()
        if last_message:
            return {
                "kind": "message",
                "message_id": str(last_message.uuid),  # Correctly use uuid
                "snippet": last_message.content[:100]  # First 100 characters as snippet
            }
        return None


class AIModelsSerializer(BaseModelSerializer):
    class Meta:
        model = LLMModels
        fields = '__all__'


class MessageAttachmentSerializer(BaseModelSerializer):
    file_url = serializers.SerializerMethodField()

    class Meta:
        model = MessageAttachment
        fields = ('id', 'file', 'file_url', 'openai_file_id', 'created_at')

    def get_file_url(self, obj):
        if obj.file.file:
            return settings.MEDIA_BASE_URL + obj.file.file.url
        
        return None