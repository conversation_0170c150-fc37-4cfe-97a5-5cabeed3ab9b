from django.conf import settings
from django.urls import reverse
from dxh_common.base.base_serializer import BaseModelSerializer, BaseSerializer
from dxh_libraries.rest_framework import serializers
from apps.file_manager.api.v1.serializers import FileSerializer
from apps.llm_manager.models import GeneratedImage


class ImagePromptSerializer(BaseSerializer):
    prompt = serializers.CharField(required=True)
    size = serializers.ChoiceField(
        choices=["1024x1024", "1024x1792", "1792x1024"],
        default="1024x1024"
    )
    style = serializers.ChoiceField(
        choices=["natural", "vivid"],
        default="natural",
        required=False
    )

class GeneratedImageSerializer(BaseModelSerializer):
    file_data = FileSerializer(source='file', read_only=True)
    download_url = serializers.SerializerMethodField()

    class Meta:
        model = GeneratedImage
        fields = ['id', 'prompt', 'image_url', 'file', 'file_data', 'download_url', 'size', 'model', 'created_at']
        read_only_fields = fields

    def get_download_url(self, obj):
        if obj.file and obj.file.file:
            return settings.MEDIA_BASE_URL + obj.file.file.url

        return None