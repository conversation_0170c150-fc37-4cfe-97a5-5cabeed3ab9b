def prompt_to_title(prompt: str, counter: str = '', length: int = 50) -> str:
    # Strip any leading/trailing spaces and truncate if needed
    prompt = prompt.strip()

    # If the prompt is already within the length, return it
    if len(prompt) <= length:
        return prompt + " " + counter

    # Otherwise, truncate the prompt, but make sure it doesn't cut off in the middle of a word
    truncated_prompt = prompt[:length].rsplit(' ', 1)[0]  # Cut at the last space within the limit
    return truncated_prompt + counter + "..."  # Add ellipsis if truncation happened