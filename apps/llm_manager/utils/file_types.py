"""
Utility module for handling file types in AI conversations.
Defines supported file types and validation functions.
"""
from dxh_common.logger import Logger

logger = Logger(__name__)

# File types supported by OpenAI's vision models
VISION_FILE_TYPES = {
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'webp': 'image/webp',
}

# File types supported by OpenAI's document models
DOCUMENT_FILE_TYPES = {
    'pdf': 'application/pdf',
    'txt': 'text/plain',
    'csv': 'text/csv',
    'json': 'application/json',
    'md': 'text/markdown',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
}

# All supported file types
SUPPORTED_FILE_TYPES = {**VISION_FILE_TYPES, **DOCUMENT_FILE_TYPES}

def is_supported_file_type(file_type):
    """
    Check if a file type is supported by OpenAI.

    Args:
        file_type (str): The file extension (e.g., 'pdf', 'jpg')

    Returns:
        bool: True if supported, False otherwise
    """
    return file_type.lower() in SUPPORTED_FILE_TYPES

def is_vision_file_type(file_type):
    """
    Check if a file type is supported by OpenAI's vision models.

    Args:
        file_type (str): The file extension (e.g., 'jpg', 'png')

    Returns:
        bool: True if supported by vision models, False otherwise
    """
    return file_type.lower() in VISION_FILE_TYPES

def is_document_file_type(file_type):
    """
    Check if a file type is supported by OpenAI's document models.

    Args:
        file_type (str): The file extension (e.g., 'pdf', 'txt')

    Returns:
        bool: True if supported by document models, False otherwise
    """
    return file_type.lower() in DOCUMENT_FILE_TYPES

def get_mime_type(file_type):
    """
    Get the MIME type for a file extension.

    Args:
        file_type (str): The file extension (e.g., 'pdf', 'jpg')

    Returns:
        str: The MIME type or None if not supported
    """
    return SUPPORTED_FILE_TYPES.get(file_type.lower())

def get_unsupported_files(file_types):
    """
    Get a list of unsupported file types from a list.

    Args:
        file_types (list): List of file extensions to check

    Returns:
        list: List of unsupported file types
    """
    return [ft for ft in file_types if not is_supported_file_type(ft)]