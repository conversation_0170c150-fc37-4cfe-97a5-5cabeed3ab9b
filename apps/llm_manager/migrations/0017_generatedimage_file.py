# Generated by Django 5.1.5 on 2025-05-18 09:37

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('file_manager', '0004_alter_fileupload_file_type'),
        ('llm_manager', '0016_messageattachment'),
    ]

    operations = [
        migrations.AddField(
            model_name='generatedimage',
            name='file',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_images', to='file_manager.fileupload', verbose_name='Downloaded File'),
        ),
    ]
