# Generated by Django 5.1.5 on 2025-05-12 15:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('llm_manager', '0013_alter_message_llm_model'),
    ]

    operations = [
        migrations.AddField(
            model_name='message',
            name='content_type',
            field=models.CharField(choices=[('text', 'Text'), ('image', 'Image')], default='text', max_length=10, verbose_name='Content Type'),
        ),
        migrations.AlterField(
            model_name='message',
            name='content',
            field=models.TextField(verbose_name='Content'),
        ),
        migrations.AlterField(
            model_name='message',
            name='conversation',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='messages', to='llm_manager.conversation', verbose_name='Conversation'),
        ),
        migrations.AlterField(
            model_name='message',
            name='edited_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Edited At'),
        ),
        migrations.AlterField(
            model_name='message',
            name='is_edited',
            field=models.BooleanField(default=False, verbose_name='Is Edited'),
        ),
        migrations.AlterField(
            model_name='message',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, verbose_name='Metadata'),
        ),
        migrations.AlterField(
            model_name='message',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children', to='llm_manager.message', verbose_name='Parent'),
        ),
        migrations.AlterField(
            model_name='message',
            name='used_tokens',
            field=models.IntegerField(blank=True, null=True, verbose_name='Used Tokens'),
        ),
    ]
