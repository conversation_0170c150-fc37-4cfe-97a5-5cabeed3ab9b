# Generated by Django 5.1.5 on 2025-02-13 14:18

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('llm_manager', '0006_rename_public_conversation_is_public_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='conversationtag',
            name='tag',
        ),
        migrations.RemoveField(
            model_name='llmmodels',
            name='num_of_token_used',
        ),
        migrations.AddField(
            model_name='attachment',
            name='preview_url',
            field=models.URLField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='conversation',
            name='last_message_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='conversation',
            name='message_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='conversation',
            name='metadata',
            field=models.J<PERSON><PERSON>ield(blank=True, default=dict),
        ),
        migrations.AddField(
            model_name='conversationtag',
            name='tags',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='llmmodels',
            name='description',
            field=models.TextField(blank=True, null=True, verbose_name='Model Description'),
        ),
        migrations.AddField(
            model_name='message',
            name='edited_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='message',
            name='is_edited',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='message',
            name='llm_model',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='messages', to='llm_manager.llmmodels'),
        ),
        migrations.AddField(
            model_name='project',
            name='is_favorite',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='message',
            name='conversation',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='messages', to='llm_manager.conversation'),
        ),
    ]
