import uuid
from django.utils import timezone
from dxh_common.base.base_repository import BaseRepository
from apps.llm_manager.models import Conversation
from apps.llm_manager.repositories.project import ProjectRepository
from django.db.models import Q


class ConversationRepository(BaseRepository):
    def __init__(self):
        super().__init__(Conversation)

    def get_or_create_conversation(self, user, validated_data):
        """Retrieves or creates a conversation."""
        conversation_id = validated_data.get("conversation_id")
        project_id = validated_data.get("project_id")
        conversation =Conversation.objects.create_conversation(user, validated_data["message"]["content"]["parts"][0], conversation_id)

        if project_id:
            project = ProjectRepository.get_project_by_id(user, project_id)
            if project:
                conversation.project = project
                conversation.save()
        return conversation

    def get_user_conversations(self, user):
        """Retrieve all conversations for a user, ordered by pinned status and update date."""
        return Conversation.objects.filter(user=user, project__isnull=True, is_archived=False).order_by("pinned_at", "-updated_at")

    def get_user_project_conversations(self, user, project_id):
        """Retrieve all conversations for a user project, ordered by pinned status and update date."""
        project = ProjectRepository.get_project_by_id(user, project_id)
        return Conversation.objects.filter(user=user, project=project, is_archived=False).order_by("pinned_at", "-updated_at")

    def get_user_archived_conversations(self, user):
        """Retrieve all archived conversations for a user, ordered by archive date."""
        return Conversation.objects.filter(user=user, is_archived=True).order_by("-archived_at")

    def get_user_conversation(self, user, conversation_id):
        """Retrieve a single conversation for the authenticated user."""
        return Conversation.objects.filter(uuid=conversation_id, user=user).first()

    def update_conversation_title(self, conversation, new_title):
        """Update conversation title."""
        conversation.title = new_title
        conversation.save()
        return conversation

    def delete_conversation(self, conversation):
        """Delete a conversation."""
        conversation.delete()
    
    def delete_all_conversations(self, user):
        """Delete all conversations for a user."""
        self.get_user_conversations(user).delete()

    def pin_conversation(self, conversation):
        """Pins a conversation by setting `pinned_at`."""
        conversation.is_pinned = True
        conversation.pinned_at = timezone.now()
        conversation.save()
        return conversation

    def unpin_conversation(self, conversation):
        """Unpins a conversation by clearing `pinned_at`."""
        conversation.is_pinned = False
        conversation.pinned_at = None
        conversation.save()
        return conversation

    def grant_access(self, conversation):
        """Grants public access by generating a shareable link."""
        conversation.shareable_uuid = uuid.uuid4() if conversation.shareable_uuid is None else conversation.shareable_uuid
        conversation.is_public = True
        conversation.save()
        return conversation

    def revoke_access(self, conversation):
        """Revokes public access."""
        conversation.is_public = False
        conversation.save()
        return conversation
    
    def get_public_conversation(self, shareable_uuid):
        """Retrieve a publicly shared conversation"""
        return Conversation.objects.filter(shareable_uuid=shareable_uuid, is_public=True).first()
    
    def archive(self, conversation):
        """Marks conversation as archived."""
        conversation.is_archived = True
        conversation.archived_at = timezone.now()
        conversation.save()

    def unarchive(self, conversation):
        """Unarchives the conversation."""
        conversation.is_archived = False
        conversation.archived_at = None
        conversation.save()
    
    def add_to_project(self, conversation, project):
        """Add conversation to a project"""
        conversation.project = project
        conversation.save()
        return conversation

    def remove_from_project(self, conversation):
        """Remove conversation from project"""
        conversation.project = None
        conversation.save()
        return conversation
        

    def search_conversations(self, user, query):
        """
        Search conversations by title or related message content.
        """
        return Conversation.objects.filter(
            Q(user=user) & 
            (Q(title__icontains=query) | Q(messages__content__icontains=query))
        ).distinct()