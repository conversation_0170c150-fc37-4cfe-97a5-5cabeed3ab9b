from datetime import datetime
from common.constants import Roll<PERSON>hoice
from dxh_common.base.base_repository import BaseRepository
from apps.llm_manager.models import Message
from apps.file_manager.models import FileUpload
from apps.file_manager.repositories import FileRepository
from apps.llm_manager.repositories.message_attachment_repository import MessageAttachmentRepository
import uuid

class MessageRepository(BaseRepository):
    def __init__(self):
        super().__init__(Message)
        self.file_repository = FileRepository()
        self.message_atachment_repository = MessageAttachmentRepository()

    def save_user_message(self, conversation, validated_data, parent_message=None):
        user_prompt = validated_data["message"]
        if "id" in user_prompt:
            msg = self.get(uuid=user_prompt["id"])
            parent_message = msg.parent
        
        message_data = {
            "author": user_prompt["author"].get("role", "user"),
            "content": user_prompt["content"]["parts"][0],
            "parent": parent_message,
        }

        # Create or update the message

        message, created = self.model.objects.update_or_create(
            uuid=user_prompt["id"] if "id" in user_prompt else uuid.uuid4(),
            conversation=conversation,
            defaults={**message_data}
        )
        if not created:
            child = self.get(parent=message)
            if child:
                child.parent = None
                child.save()
            message.is_edited = True
            message.edited_at = datetime.now()
            message.save()

        # Handle attachments if present in metadata
        if "metadata" in user_prompt and "attachments" in user_prompt["metadata"]:
            self.save_message_attachments(message, user_prompt["metadata"]["attachments"])

        return message, created

    def save_assistant_message(self, conversation, response_content, user_message, model=None, content_type="text"):
        """Saves the AI assistant's response."""
        return self.create(
            conversation=conversation,
            parent=user_message,
            author=RollChoice.ASSISTANT.value,
            content=response_content,
            content_type=content_type,
            llm_model=model
        )

    def get_conversation_messages(self, conversation):
        """Retrieve messages for a given conversation."""
        return self.model.objects.filter(conversation=conversation).order_by("created_at")

    def get_threaded_conversation_messages(self, conversation):
        first_message = self.model.objects.filter(conversation=conversation, parent__isnull=True).order_by("created_at").first()

        if not first_message:
            return []

        try:
            message_chain = [first_message]
            while True:
                child = self.get(parent=first_message)
                if not child:
                    break
                message_chain.append(child)
                first_message = child

            return message_chain

        except Exception as e:
            raise e

    def get_recent_messages(self, conversation, limit=5):
        """
        Retrieves the last few messages for maintaining context.
        """
        messages = self.model.objects.filter(conversation=conversation).order_by("-updated_at")[:limit]
        return list(messages[::-1])  # Reverse to maintain order

    def save_message_attachments(self, message, attachments):
        for attachment_data in attachments:
            try:
                file_obj = self.file_repository.get(id=attachment_data["id"])
                attachment = self.message_atachment_repository.get(message=message, file=file_obj)
                if not attachment:
                    self.message_atachment_repository.create(
                        message=message,
                        file=file_obj
                    )

            except FileUpload.DoesNotExist:
                pass
            except Exception as e:
                raise e

    def get_message_attachments(self, message):
        return self.message_atachment_repository.filter(message=message)