from apps.llm_manager.models import Project


class ProjectRepository:
    """
    Handles all database interactions related to Project.
    """

    @staticmethod
    def get_user_projects(user):
        """Retrieve all projects owned by a user."""
        return Project.objects.filter(user=user).order_by("-updated_at")

    @staticmethod
    def get_project_by_id(user, project_id):
        """Retrieve a project by ID."""
        return Project.objects.filter(user=user, uuid=project_id).first()

    @staticmethod
    def create_project(user, name, description):
        """Create a new project."""
        return Project.objects.create(user=user, name=name, description=description)

    @staticmethod
    def update_project(project, name, description):
        """Update project details."""
        project.name = name
        project.description = description
        project.save()
        return project

    @staticmethod
    def delete_project(project):
        """Delete a project."""
        project.delete()
