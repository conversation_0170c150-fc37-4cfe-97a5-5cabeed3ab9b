from django.db import DatabaseError
from dxh_common.logger import Logger
from common.repository import BaseRepository

from apps.llm_manager.models import GeneratedImage
from apps.file_manager.repositories import FileRepository

logger = Logger(__name__)


class ImageGenerationRepository(BaseRepository):
    def __init__(self):
        super().__init__(GeneratedImage)
        self.file_repository = FileRepository()

    def create_image(self, user, prompt, image_url, size, model, file=None):
        try:
            image = self.create(
                user=user,
                prompt=prompt,
                image_url=image_url,
                file=file,
                size=size,
                model=model
            )

            return image
        
        except DatabaseError as e:
            logger.error(f"Database error creating generated image: {e}")
            raise Exception(f"Error creating generated image: {e}")


    def update_image_file(self, image_id, file):
        try:
            image = self.get(id=image_id)
            image.file = file
            image.save()
            return image
        except DatabaseError as e:
            logger.error(f"Database error updating generated image file: {e}")
            raise Exception(f"Error updating generated image file: {e}")

    
    def get_user_images(self, user):
        try:
            return self.filter(user=user).order_by('-created_at')
        except DatabaseError as e:
            logger.error(f"Database error retrieving user images: {e}")
            raise Exception(f"Error retrieving user images: {e}")
