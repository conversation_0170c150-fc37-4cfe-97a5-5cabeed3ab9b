from django.core.mail import send_mail, BadHeaderError
from django.template.loader import render_to_string
from django.conf import settings
from common.logger import Logger
from dxh_libraries.celery import shared_task

logger = Logger(__name__)


class EmailService:
    def __init__(self, from_email=None):
        self.from_email = from_email or settings.DEFAULT_FROM_EMAIL
        self.static_url = settings.MEDIA_BASE_URL+settings.STATIC_URL

    def send_email(self, to_email, subject, template_name, context):
        # Add static image URLs to context
        context['logo_image'] = self.static_url+'image/logo.png'
        context['facebook_image'] = self.static_url+'image/facebook.png'
        context['linkedin_image'] = self.static_url+'image/linkedin.png'
        context['instagram_image'] = self.static_url+'image/insta.png'
        
        # Log before sending
        logger.info(f"Attempting to send email to {to_email} with template {template_name}")
        
        # Queue the email sending task with explicit task name
        send_email_task.delay(
            to_email=to_email,
            subject=subject,
            template_name=template_name,
            context=context,
            from_email=self.from_email
        )
        
@shared_task()
def send_email_task(to_email, subject, template_name, context, from_email):
    try:
        # Render the HTML content
        html_content = render_to_string(f'email/{template_name}.html', context)
        
        # Send the email
        send_mail(
            subject=subject,
            message="", # Empty plain text message
            from_email=from_email,
            recipient_list=[to_email],
            fail_silently=False,
            html_message=html_content
        )
        logger.info(f"Email sent successfully to {to_email}")
        return True
        
    except BadHeaderError:
        logger.error(f"Invalid header found while sending email to {to_email}")
        return False
    except Exception as e:
        logger.error(f"Failed to send email to {to_email}: {str(e)}")
        return False
