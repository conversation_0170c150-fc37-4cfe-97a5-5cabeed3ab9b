from typing import List
from common.repository import BaseRepository, RepositoryError
from apps.identity.models.user_device import UserDevice

class DeviceRepository(BaseRepository):
    def __init__(self):
        super().__init__(UserDevice)

    def create_device(self, **kwargs) -> UserDevice:
        return UserDevice.objects.create(**kwargs)

    def get_user_devices(self, user_id: int) -> List[UserDevice]:
        try:
            return self.model.objects.filter(user_id=user_id, is_deleted=False)
        except Exception as e:
            raise RepositoryError(f"Error getting user devices: {e}")

    def delete_device(self, device_id: str):
        try:
            device = self.model.objects.get(uuid=device_id)
            device.is_deleted = True
            device.save()
            return device
        except self.model.DoesNotExist:
            raise RepositoryError(f"Device with id {device_id} does not exist.")
        except Exception as e:
            raise RepositoryError(f"Error deleting device: {e}")

    def delete_all_devices(self, user_id: int, except_device_id: str = None):
        try:
            queryset = self.model.objects.filter(user_id=user_id)
            if except_device_id:
                queryset = queryset.exclude(uuid=except_device_id)
            queryset.update(is_deleted=True)
            return queryset
        except Exception as e:
            raise RepositoryError(f"Error deleting devices: {e}")