from django.contrib.auth.hashers import check_password
from common.logger import Logger

logger = Logger(__name__)

class UserRepository:
    @staticmethod
    def delete_user_account(user, password):
        try:
            if not check_password(password, user.password):
                return False, "Invalid password"
            
            user.delete()
            return True, "Account deleted successfully"
        except Exception as e:
            logger.error(f"Error deleting user account: {str(e)}")
            return False, "Failed to delete account"