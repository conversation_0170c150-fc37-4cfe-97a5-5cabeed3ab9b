[{"model": "identity.mfasetup", "pk": "1", "fields": {"user": 3, "type": "TOTP", "secret": "some_secret_here", "backup_codes": ["code1", "code2", "code3"], "is_active": true, "updated_at": "2025-01-23T10:00:00Z", "created_at": "2025-01-23T10:00:00Z"}}, {"model": "identity.usersession", "pk": "1", "fields": {"user": 3, "token": "some_access_token_here", "refresh_token": "some_refresh_token_here", "expires_at": "2025-01-24T10:00:00Z", "is_active": true, "ip_address": "***********", "user_agent": "Mozilla/5.0", "updated_at": "2025-01-23T10:00:00Z", "created_at": "2025-01-23T10:00:00Z"}}, {"model": "identity.verificationcode", "pk": 1, "fields": {"user": 3, "code": "123456", "type": "EMAIL_OTP", "is_used": false, "attempts": 0, "expires_at": "2025-01-24T12:00:00Z", "created_at": "2025-01-23T10:00:00Z"}}, {"model": "identity.verificationtoken", "pk": 1, "fields": {"user": 3, "token": "5b42a59f-aa4b-4bce-ba23-44e4b3317825", "token_type": "PASSWORD_RESET_OTP", "otp": "123456", "is_used": false, "expires_at": "2025-01-24T12:00:00Z", "attempts": 0, "updated_at": "2025-01-23T10:00:00Z", "created_at": "2025-01-23T10:00:00Z"}}]