# from django.contrib.auth.models import Group as DefaultGroup
# from django.db import models
# from django.utils.translation import gettext_lazy as _
# from common.models import BaseModel


# class Group(DefaultGroup, BaseModel):
#     company = models.ForeignKey(
#         "core.Company",
#         on_delete=models.SET_NULL,
#         null=True,
#         blank=True,
#         related_name="company_groups",
#         verbose_name=_("Company"),
#     )

#     class Meta:
#         db_table = "identity_groups"
#         verbose_name = _("Group")
#         verbose_name_plural = _("Groups")

#     def __str__(self):
#         return self.name
