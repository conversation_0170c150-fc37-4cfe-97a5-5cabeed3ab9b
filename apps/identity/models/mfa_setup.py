from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model

from common.models import BaseModel
from common.constants import MFAType

User = get_user_model()

class MFASetup(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Company"),
    )
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL, 
        null=True, 
        db_index=True,
        verbose_name=_("User")
    )
    type = models.CharField(
        max_length=4,
        choices=MFAType.choices(),
        default=MFAType.TOTP.value,
        db_index=True,
        verbose_name=_("MFA Type")
    )
    secret = models.CharField(max_length=255, verbose_name=_("Secret"))
    backup_codes = models.J<PERSON><PERSON>ield(default=list, verbose_name=_("Backup Codes"))

    class Meta:
        db_table = "identity_mfa_setups"
        verbose_name = _("MFA Setup")
        verbose_name_plural = _("MFA Setups")

    def __str__(self):
        return str(self.user.email)