import uuid
from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from common.models import BaseModel
from common.constants import VerificationTokenType

User = get_user_model()


class VerificationToken(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Company"),
    )
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("User")
    )
    token = models.UUIDField(
        default=uuid.uuid4, 
        editable=False, 
        unique=True,
        verbose_name=_("Token")
    )
    token_type = models.CharField(
        max_length=20, 
        choices=VerificationTokenType.choices(), 
        db_index=True,
        verbose_name=_("Token Type")
    )
    otp = models.CharField(
        max_length=20, 
        null=True, 
        blank=True,
        verbose_name=_("OTP")
    )
    is_used = models.BooleanField(
        default=False, 
        db_index=True,
        verbose_name=_("Is Used")
    )
    expires_at = models.DateTimeField(
        verbose_name=_("Expires At")
    )
    attempts = models.IntegerField(
        default=0,
        verbose_name=_("Attempts")
    )

    class Meta:
        db_table = "identity_verification_tokens"
        verbose_name = _("Verification token")
        verbose_name_plural = _("Verification tokens")

    def is_valid(self):
        return (
            not self.is_used and
            self.attempts < 3 and
            timezone.now() <= self.expires_at
        )

    def mark_used(self):
        self.is_used = True
        self.save()

    def __str__(self):
        return str(self.user.email)
