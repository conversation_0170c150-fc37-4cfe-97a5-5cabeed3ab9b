# from django.db import models
# from django.utils.translation import gettext_lazy as _
# from common.models import BaseModel


# class GroupPermission(BaseModel):
#     company = models.ForeignKey(
#         "core.Company",
#         on_delete=models.SET_NULL,
#         null=True,
#         blank=True,
#         related_name="company_group_permissions",
#         verbose_name=_("Company"),
#     )
#     group = models.ForeignKey(
#         "myapp.Group",
#         on_delete=models.CASCADE,
#         related_name="group_permissions",
#         verbose_name=_("Group"),
#     )
#     permission = models.ForeignKey(
#         "myapp.Permission",
#         on_delete=models.CASCADE,
#         related_name="permission_groups",
#         verbose_name=_("Permission"),
#     )

#     class Meta:
#         db_table = "identity_group_permissions"
#         verbose_name = _("Group Permission")
#         verbose_name_plural = _("Group Permissions")

#     def __str__(self):
#         return f"{self.group.name} - {self.permission.name}"
