from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel

User = get_user_model()


class UserSession(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Company"),
    )
    user = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        db_index=True,
        verbose_name=_("User")
    )
    token = models.CharField(max_length=255, verbose_name=_("Token"))
    refresh_token = models.Char<PERSON>ield(max_length=255, verbose_name=_("Refresh Token"))
    expires_at = models.DateTimeField(verbose_name=_("Expires At"))
    ip_address = models.GenericIPAddressField(null=True, verbose_name=_("IP Address"))
    user_agent = models.CharField(max_length=255, null=True, verbose_name=_("User Agent"))

    class Meta:
        db_table = "identity_sessions"
        verbose_name = _("User Session")
        verbose_name_plural = _("User Sessions")

    def __str__(self):
        return str(self.user.email)

