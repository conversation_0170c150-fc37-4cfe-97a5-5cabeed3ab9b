# from django.contrib.auth.models import Permission as DefaultPermission
# from django.db import models
# from django.utils.translation import gettext_lazy as _
# from common.models import BaseModel


# class Permission(DefaultPermission, BaseModel):
#     company = models.ForeignKey(
#         "core.Company",
#         on_delete=models.SET_NULL,
#         null=True,
#         blank=True,
#         related_name="company_permissions",
#         verbose_name=_("Company"),
#     )

#     class Meta:
#         db_table = "identity_permissions"
#         verbose_name = _("Permission")
#         verbose_name_plural = _("Permissions")

#     def __str__(self):
#         return self.name
