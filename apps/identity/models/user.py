from django.contrib.auth.models import AbstractUser, Group
from django.db import models
from django.utils.translation import gettext_lazy as _
from common.constants import SubscriptionType, UserStatus
from common.models import BaseModel


class User(AbstractUser, BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="company_users",
        verbose_name=_("Company"),
    )
    name = models.CharField(max_length=255, verbose_name=_("Name"))
    email = models.EmailField(
        unique=True,
        db_index=True,
        verbose_name=_("Email Address"),
    )
    phone = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        db_index=True,
        verbose_name=_("Phone Number"),
    )
    profile_picture = models.ImageField(
        blank=True,
        null=True,
        upload_to="profile/",
        verbose_name=_("Profile Picture"),
    )
    deletion_requested = models.BooleanField(
        default=False,
        verbose_name=_("Deletion Requested")
    )
    deletion_requested_at = models.DateTimeField(
        null=True, 
        blank=True,
        verbose_name=_("Deletion Requested Date")
    )
    is_email_verified = models.BooleanField(
        default=False,
        verbose_name=_("Email Verified"),
        db_index=True,
    )
    is_phone_verified = models.BooleanField(
        default=False,
        verbose_name=_("Phone Verified"),
        db_index=True,
    )
    mfa_enabled = models.BooleanField(
        default=False,
        verbose_name=_("MFA Enabled"),
    )
    status = models.CharField(
        max_length=20,
        choices=UserStatus.choices(),
        default=UserStatus.ACTIVE.value,
        db_index=True,
        verbose_name=_("Status"),
    )
    last_login_ip = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name=_("Last Login IP"),
    )
    last_login_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Last Login Date"),
    )
    date_of_birth = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Date of Birth"),
    )
    bio = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Bio"),
    )
    groups = models.ManyToManyField(
        Group,
        related_name="group_users",
        verbose_name=_("Groups"),
        help_text=_("The groups this user belongs to."),
    )
    country = models.ForeignKey(
        "core.Country",
        on_delete=models.SET_NULL,
        related_name="user_countries",
        null=True,
        db_index=True,
        verbose_name=_("Country")
    )
    subscription_type = models.CharField(
        max_length=50,
        choices=SubscriptionType.choices,
        default=SubscriptionType.FREE.value,
        db_index=True,
        verbose_name=_("Subscription Type"),
    )

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["username"]

    class Meta:
        db_table = "identity_users"
        verbose_name = _("User")
        verbose_name_plural = _("Users")

    def __str__(self):
        return self.email
