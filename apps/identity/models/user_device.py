from django.db import models
from common.models import BaseModel

class UserDevice(BaseModel):
    device_id = models.CharField(max_length=64, unique=True, null=True, blank=True)
    user = models.ForeignKey(
        'User',
        on_delete=models.CASCADE,
        related_name='devices'
    )
    device_name = models.CharField(max_length=255)
    browser = models.CharField(max_length=255)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    last_login = models.DateTimeField(auto_now=True)
    is_current_device = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'identity_user_devices'
        ordering = ['-last_login']