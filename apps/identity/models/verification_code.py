from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from common.models import BaseModel
from common.constants import VerificationTokenType

User = get_user_model()


class VerificationCode(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Company"),
    )
    user = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        db_index=True,
        verbose_name=_("User")
    )
    code = models.CharField(
        max_length=50, 
        verbose_name=_("Code")
    )
    type = models.CharField(
        max_length=20, 
        choices=VerificationTokenType.choices(),
        verbose_name=_("Type")
    )
    is_used = models.BooleanField(
        default=False, 
        verbose_name=_("Is Used")
    )
    attempts = models.IntegerField(
        default=0, 
        verbose_name=_("Attempts")
    )
    expires_at = models.DateTimeField(verbose_name=_("Expires At"))

    class Meta:
        db_table = "identity_verification_codes"
        verbose_name = _("User verification code")
        verbose_name_plural = _("User verification codes")

    def __str__(self):
        return str(self.user.email)
