from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db.utils import IntegrityError


SUPERUSERS = [
    {
        "username": "admin",
        "email": "<EMAIL>",
        "password": "1Password!",
        "is_staff": True,
        "is_superuser": True
    },
    {  
        "username": "admin1",
        "email": "<EMAIL>",
        "password": "1Password!",
        "is_staff": True,
        "is_superuser": True
    },
]

class Command(BaseCommand):
    help = 'Creates two superusers if no user exists in the system'

    SUPERUSERS = SUPERUSERS

    def handle(self, *args, **kwargs):
        User = get_user_model()
        created_users = self._create_superusers(User)

        if created_users:
            self.stdout.write(self.style.SUCCESS(f"{created_users} superuser(s) created successfully."))
        else:
            self.stdout.write(self.style.WARNING("No superusers were created."))

    def _create_superusers(self, User):
        created_count = 0
        for user_data in self.SUPERUSERS:
            try:
                User.objects.create_superuser(**user_data)
                created_count += 1
                self.stdout.write(self.style.SUCCESS(f"Superuser '{user_data['username']}' created successfully."))
            except IntegrityError:
                self.stdout.write(self.style.WARNING(f"Superuser '{user_data['username']}' already exists. Skipping."))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Failed to create superuser '{user_data['username']}': {e}"))

        return created_count
