# Generated by Django 5.1.5 on 2025-02-05 13:13

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('identity', '0004_connectedapp'),
    ]

    operations = [
        migrations.AddField(
            model_name='connectedapp',
            name='uuid',
            field=models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID'),
        ),
        migrations.AddField(
            model_name='mfasetup',
            name='uuid',
            field=models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID'),
        ),
        migrations.AddField(
            model_name='user',
            name='uuid',
            field=models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID'),
        ),
        migrations.AddField(
            model_name='usersession',
            name='uuid',
            field=models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID'),
        ),
        migrations.AddField(
            model_name='verificationcode',
            name='uuid',
            field=models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID'),
        ),
        migrations.AddField(
            model_name='verificationtoken',
            name='uuid',
            field=models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID'),
        ),
    ]
