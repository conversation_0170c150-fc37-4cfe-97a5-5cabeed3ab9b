from django.contrib.auth.hashers import check_password
from common.logger import Logger

logger = Logger(__name__)

class DeleteAccountService:
    @staticmethod
    def delete_user_account(user, password):
        try:
            if not check_password(password, user.password):
                return False, "Invalid password"
            
            # Delete user account
            user.is_deleted = True
            user.is_active = False
            user.save()

            return True, "Account deleted successfully"
            
        except Exception as e:
            logger.error(f"Delete account error: {str(e)}")
            return False, str(e)