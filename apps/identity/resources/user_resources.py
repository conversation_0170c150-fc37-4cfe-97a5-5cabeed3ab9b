from import_export import resources
from apps.identity.models.user import User

class UserResource(resources.ModelResource):
    """
    Resource for exporting/importing User data.
    """
    class Meta:
        model = User
        fields = (
            "id",
            "email",
            "username",
            "first_name",
            "last_name",
            "phone",
            "is_active",
            "is_email_verified",
            "is_phone_verified",
            "mfa_enabled",
            "status",
            "last_login_at",
            "created_at",
            "date_joined",
            "last_login",
            "company",
            "last_login_ip",
            "profile_picture",
            "is_staff",
            "is_superuser",
        )
        export_order = fields  # Maintain the order of fields during export
        import_id_fields = ('id',)  # Use 'id' as the unique identifier for imports
        skip_unchanged = True  # Skip rows that haven't changed during import
        report_skipped = True  # Report skipped rows in the import result