from common.consumer import BaseWebSocketConsumer

class ExampleIdentityWebSocketConsumer(BaseWebSocketConsumer):
    """
    WebSocket consumer for handling identity-related actions.
    Extends the reusable BaseWebSocketConsumer.
    """

    async def route_action(self, action, payload):
        if action == "identity.get_details":
            return await self.get_identity_details(payload)
        elif action == "identity.list":
            return await self.list_identitys(payload)
        else:
            return self.response_error(f"Unknown action: {action}", action=action)

    async def get_identity_details(self, payload):
        identity_id = payload.get("identity_id")
        if not identity_id:
            return self.response_error("identity ID is required.", action="identity.get_details")

        # Simulating a identity lookup
        identity_data = {"id": identity_id, "name": "Sample identity", "price": 29.99}
        return self.response_success("identity details retrieved.", action="identity.get_details", data=identity_data)

    async def list_identitys(self, payload):
        # Simulating identity listing
        identitys = [
            {"id": 1, "name": "identity A", "price": 10.99},
            {"id": 2, "name": "identity B", "price": 15.49},
        ]
        return self.response_success("identity list retrieved.", action="identity.list", data={"identitys": identitys})
