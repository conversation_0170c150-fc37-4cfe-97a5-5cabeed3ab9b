from user_agents import parse

def get_device_info(request):
    """Extract device info from the request User-Agent."""
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    parsed_agent = parse(user_agent)
    device = {
        "browser": parsed_agent.browser.family,  
        "browser_version": parsed_agent.browser.version_string,
        "os": parsed_agent.os.family,
        "os_version": parsed_agent.os.version_string, 
        "device": parsed_agent.device.family,
        "is_mobile": parsed_agent.is_mobile,
        "is_tablet": parsed_agent.is_tablet, 
        "is_pc": parsed_agent.is_pc,
        "is_touch_capable": parsed_agent.is_touch_capable, 
        "ip_address": request.META.get("REMOTE_ADDR", ""),
    }
    device_info = f"{parsed_agent.os.family} {parsed_agent.os.version_string} - {parsed_agent.browser.family} {parsed_agent.browser.version_string}"
    return device_info, device