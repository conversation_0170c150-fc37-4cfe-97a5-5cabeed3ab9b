from allauth.socialaccount.providers.oauth2.client import OAuth2Client

class CustomOAuth2Client(OAuth2Client):
    """
    Custom OAuth2 client to fix error in dj_rest_auth==7.0.1 OAuth2Client.__init__() got multiple values for argument 'scope_delimiter'
    https://github.com/iMerica/dj-rest-auth/issues/673#issuecomment-**********
    """
    client_id_parameter = "client_id"

    def __init__(
        self,
        request,
        consumer_key,
        consumer_secret,
        access_token_method,
        access_token_url,
        callback_url,
        _scope,
        scope_delimiter=" ",
        headers=None,
        basic_auth=False,
    ):
        super().__init__(
            request,
            consumer_key,
            consumer_secret,
            access_token_method,
            access_token_url,
            callback_url,
            scope_delimiter,
            headers,
            basic_auth,
        )