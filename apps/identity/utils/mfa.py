import secrets
import string
import pyotp


def generate_backup_code(length=8):
    """Generate random backup code with specified length"""
    alphabet = string.ascii_uppercase + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def verify_totp_code(secret, code):
    """Verify TOTP code against secret"""
    totp = pyotp.TOTP(secret)
    return totp.verify(code)


def generate_totp_secret():
    """Generate new TOTP secret"""
    return pyotp.random_base32()
