# Utility function to log user activities
from django.conf import settings
from urllib.parse import urlencode
from dxh_libraries.rest_framework_simplejwt import AccessToken
from apps.identity.services import VerificationCodeService
from apps.user.models.activity import UserActivity
from apps.cross_cutting.email_service import EmailService
from apps.identity.utils.get_user_meta_data import get_device_info
from common.constants import VerificationTokenType


def log_activity(user, activity_type, description, request):
    """Log user activity and detect new IP or device logins."""
    current_ip = request.META.get('REMOTE_ADDR')
    current_device, metadata = get_device_info(request)

    # Check if the IP and device were used before
    is_new_ip = not UserActivity.objects.filter(user=user, ip_address=current_ip).exists()
    is_new_device = not UserActivity.objects.filter(user=user, device_info=current_device).exists()

    # Store activity
    UserActivity.objects.create(
        user=user,
        activity_type=activity_type,
        description=description,
        ip_address=current_ip,
        device_info=current_device,
        metadata=metadata
    )

    # If either IP or device is new, send an alert
    if is_new_ip or is_new_device:
        send_new_device_alert(user, current_ip, current_device)

def send_new_device_alert(user, ip, device_info):
    """Send an email notification when a user logs in from a new IP or device."""
    type = VerificationTokenType.PASSWORD_RESET_OTP.value
    verify_code_service = VerificationCodeService()
    code = verify_code_service.create_verification_code(user=user, code_type=type)
    token = AccessToken.for_user(user)
    params = {
        "code": code,
        "token": token,
        "email": user.email
    }
    reset_link = f"{settings.FRONTEND_BASE_URL}/auth/new-password?{urlencode(params)}"
    context = {
        "user": user.email,
        "ip": ip,
        "device": device_info,
        "reset_link": reset_link,
    }

    email_service = EmailService()
    email_service.send_email(
        to_email=user.email,
        subject="New Device or IP Login Alert",
        template_name="new_device_login",
        context=context,
    )