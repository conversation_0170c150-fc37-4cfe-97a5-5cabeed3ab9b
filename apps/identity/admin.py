from django.contrib import admin
from django.contrib.auth.models import Group
from django.contrib.auth.admin import UserAdmin, GroupAdmin
from django.contrib.auth.forms import AdminPasswordChangeForm
from django.utils.translation import gettext_lazy as _
from import_export.admin import ImportExportModelAdmin
from unfold.contrib.import_export.forms import ExportForm, ImportForm, SelectableFieldsExportForm

from common.config import BaseAdmin, UserAdminChangeForm, UserAdminCreationForm
from apps.identity.resources import UserResource
from apps.identity.models import (User, UserSession, VerificationCode, VerificationToken, MFASetup
)


admin.site.unregister(Group)


# --------------------------------------------------------
# USER ADMIN CONFIGURATION
# --------------------------------------------------------

@admin.register(User)
class CustomUserAdmin(UserAdmin, BaseAdmin, ImportExportModelAdmin):
    """
    Admin configuration for the User model.
    """
    import_form_class = ImportForm
    export_form_class = ExportForm
    export_form_class  = SelectableFieldsExportForm
    resource_classes = [UserResource]
    form = UserAdminChangeForm
    add_form = UserAdminCreationForm 
    change_password_form = AdminPasswordChangeForm
    list_display = (
        "email",
        "username",
        "is_active",
        "is_email_verified",
        "is_phone_verified",
        "mfa_enabled",
        "status",
        "last_login_at",
    )
    list_filter = (
        "is_active",
        "is_email_verified",
        "is_phone_verified",
        "mfa_enabled",
        "status",
        "is_staff",
    )
    search_fields = (
        "email", 
        "username", 
        "first_name", 
        "last_name", 
        "phone"
    )
    ordering = ("-date_joined",)
    readonly_fields = (
        "id",
        "created_at",
    )
    fieldsets = (
        (None, 
            {
                "fields": (
                    "email", 
                    "password"
                )
            }
         ),
        (_("Personal info"),
            {
                "fields": (
                    "username", 
                    "first_name", 
                    "last_name", 
                    "phone",
                    "subscription_type"
                )
        },
        ),
        (_("Verification"),
            {
                "fields": (
                    "is_email_verified", 
                    "is_phone_verified"
                )
        },
        ),
        (_("Security"), {
            "fields": (
                "mfa_enabled", 
                "status",
            )
        }
        ),
        (_("Access"),
            {
                "fields": (
                    "is_active",
                    "is_staff",
                    "is_superuser",
                    "groups",
                    "user_permissions",
                )
        },
        ),
        (_("Important dates"),
            {
                "fields": (
                    "last_login", 
                    "date_joined", 
                    "last_login_at"
                )
        },
        ),
        (_("Additional info"), 
            {
                "fields": (
                    "company",
                    "last_login_ip",
                )
        }
        ),
        (_("Profile Picture"),  
            {
                "fields": (
                    "profile_picture",  
                )
        }
        ),
    )

# --------------------------------------------------------
# USER SESSION ADMIN CONFIGURATION
# --------------------------------------------------------

@admin.register(UserSession)
class UserSessionAdmin(BaseAdmin):
    """
    Admin configuration for the UserSession model.
    """
    list_display = (
        "user",
        "is_active",
        "ip_address",
        "expires_at",
        "created_at",
    )
    list_filter = (
        "is_active", 
        "created_at"
    )
    search_fields = (
        "user__email", 
        "ip_address", 
        "user_agent"
    )
    ordering = ("-created_at",)
    readonly_fields = (
        "token", 
        "refresh_token"
    )

@admin.register(VerificationCode)
class VerificationCodeAdmin(BaseAdmin):
    """
    Admin configuration for the VerificationCode model.
    """
    list_display = (
        "user",
        "code",
        "is_used",
        "attempts",
        "expires_at",
        "created_at",
    )
    list_filter = (
        "type", 
        "is_used", 
        "created_at"
    )
    search_fields = (
        "user__email", 
        "code"
    )
    ordering = ("-created_at",)
    readonly_fields = ("code",)

# --------------------------------------------------------
# VERIFICATION CODE ADMIN CONFIGURATION
# --------------------------------------------------------

@admin.register(VerificationToken)
class VerificationTokenAdmin(BaseAdmin):
    """
    Admin configuration for the VerificationToken model.
    """
    list_display = (
        "user",
        "token_type",
        "token",
        "is_used",
        "attempts",
        "expires_at",
        "created_at",
    )
    list_filter = (
        "token_type", 
        "is_used", 
        "created_at"
    )
    search_fields = (
        "user__email", 
        "token"
    )
    ordering = ("-created_at",)
    readonly_fields = (
        "token", 
        "otp"
    )

# --------------------------------------------------------
# MFA SETUP ADMIN CONFIGURATION
# --------------------------------------------------------

@admin.register(MFASetup)
class MFASetupAdmin(BaseAdmin):
    """
    Admin configuration for the MFASetup model.
    """
    list_display = (
        "user", 
        "type", 
        "is_active", 
        "created_at"
    )
    list_filter = (
        "type", 
        "is_active"
    )
    search_fields = ("user__email",)
    ordering = ("-created_at",)
    readonly_fields = (
        "secret", 
        "backup_codes"
    )

# --------------------------------------------------------
# GROUP ADMIN CONFIGURATION
# --------------------------------------------------------

@admin.register(Group)
class GroupAdmin(GroupAdmin, BaseAdmin):
    pass
