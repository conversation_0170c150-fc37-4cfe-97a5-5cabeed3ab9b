import os
from django.db import transaction
from django.contrib.auth.hashers import is_password_usable
from django.conf import settings
from dxh_common.decorators import recaptcha
from dxh_libraries.rest_framework import status, Response, MultiPartParser, FormParser
from dxh_libraries.rest_framework_simplejwt import RefreshToken
from dxh_libraries.drf_yasg import swagger_auto_schema
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView, PublicApiView
from common.constants import VerificationTokenType


from apps.cross_cutting import ReCAPTCHAService
from apps.core.services import CompanyService, TimezoneService

from apps.identity.api.v1.serializers import ResendOTPSerializer
from apps.identity.api.v1.serializers import LoginSerializer
from apps.identity.api.v1.serializers import ForgotPasswordSerializer
from apps.identity.api.v1.serializers import LogoutSerializer
from apps.identity.api.v1.serializers import ResetPasswordSerializer
from apps.identity.api.v1.serializers import ChangePasswordSerializer
from apps.identity.api.v1.serializers import RegistrationSerializer
from apps.identity.api.v1.serializers import NameUpdateSerializer
from apps.identity.api.v1.serializers import ProfilePictureSerializer
from apps.identity.api.v1.serializers import DeleteAccountSerializer
from apps.identity.api.v1.serializers import UserDeviceSerializer
from apps.identity.services import DeleteAccountService
from apps.identity.services import DeviceService
from apps.user.services import UserPreferenceService
from apps.identity.services import IdentityService, SocialService, VerificationCodeService
from apps.notification.services import NotificationService
from apps.user.services import UserService
from apps.identity.utils.user_login_activity import log_activity
from apps.user.api.v1.serializers import UserSerializer, UserDetailSerializer

from config.settings.base import MEDIA_BASE_URL

logger = Logger(__name__)


class LoginView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.identity_service = IdentityService()
        self.device_service = DeviceService()

    @swagger_auto_schema(request_body=LoginSerializer)
    @recaptcha(enabled=settings.RECAPTCHA_ENABLE)
    def post(self, request):
        try:
            serializer = LoginSerializer(data=request.data)
            if not serializer.is_valid():
                result = {
                    "message": _("Invalid input. Please check the provided details."),
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            user, tokens, error = self.identity_service.login(
                email=serializer.validated_data["email"],
                password=serializer.validated_data["password"],
                request=request
            )

            if not user:
                result = {
                    "message": error,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            # Log login activity
            log_activity(user, 'login', _('User logged in'), request)

            result = {
                "message": _("Login successful."),
                "data": {
                    "tokens": tokens,
                    "user": UserSerializer(user).data,
                },
            }
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "LoginView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e


class LogoutView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @swagger_auto_schema(request_body=LogoutSerializer)
    def post(self, request):
        try:
            serializer = LogoutSerializer(data=request.data)

            if not serializer.is_valid():
                result = {
                    "message": _("Invalid input. Please check the provided details."),
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            if not serializer:
                result = {
                    "message": _("Refresh token is required."),
                }
                return Response(
                    result,
                    status=status.HTTP_400_BAD_REQUEST,
                )

            refresh_token = RefreshToken(serializer.validated_data["refresh_token"])
            refresh_token.blacklist()

            # Log logout activity
            log_activity(request.user, 'logout', _("User logged out"), request)

            result = {
                "message": _("Logged out successfully"),
            }
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "LogoutView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e


class RegisterView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()
        self.user_preference_service = UserPreferenceService()
        self.company_service = CompanyService()
        self.timezone_service = TimezoneService()
        self.verification_code_service = VerificationCodeService()
        self.identity_service = IdentityService()

    @swagger_auto_schema(request_body=RegistrationSerializer)
    @recaptcha(enabled=settings.RECAPTCHA_ENABLE)
    @transaction.atomic
    def post(self, request):
        try:
            payload = request.data
            serializer = RegistrationSerializer(data=payload)

            if not serializer.is_valid():
                result = {
                    "message": _("Invalid input. Please check the provided details."),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            data = serializer.validated_data
            # Check user already exist
            user = self.user_service.get(email=data["email"])
            if user and user.is_active and not user.is_deleted:
                result = {
                    "message": _("The email or username has been already registered"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            # Check user registration is not completed
            if user and not user.is_active and not user.is_email_verified:
                verification_code = self.verification_code_service.create_verification_code(user, VerificationTokenType.EMAIL_OTP.value)
                self.identity_service.send_verification_link(user=user, code=verification_code)

                result = {
                    "message": _("Registration is not completed. Please check your email to verify your account.")
                }

                return Response(result, status=status.HTTP_200_OK)

            default_timezone = self.timezone_service.get_default_timezone()
            timezone = default_timezone.code if default_timezone else "UTC"
            company = self.company_service.get_default_company()

            data = serializer.validated_data
            data["company"] = company
            data.pop("confirm_password", None)

            # Create user
            user = self.user_service.create_user(data)

            # Create user preference
            self.user_preference_service.create_user_preference(
                data={
                    "company": company,
                    "user": user,
                    "timezone": timezone,
                }
            )

            result = {
                "message": _("Registration successful. Please check your email to verify your account."),
                "data": UserSerializer(user).data,
            }

            return Response(result, status=status.HTTP_201_CREATED)
        except Exception as e:
                logger.error(f"RegisterView Error: {str(e)}")
                raise e

class ResendOTPView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.identity_service = IdentityService()
        self.user_service = UserService()
        self.verification_code_service = VerificationCodeService()

    @recaptcha(enabled=settings.RECAPTCHA_ENABLE)
    def post(self, request):
        serializer = ResendOTPSerializer(data=request.data)
        if not serializer.is_valid():
            first_error_message = serializer.errors[next(iter(serializer.errors))][0]

            result = {
                "message": first_error_message,
                "errors": serializer.errors
            }
            return Response(result)

        email = serializer.validated_data["email"]
        user = self.user_service.get(email=email)

        if not user:
            result = {
                "message": "User not found",
            }
            return Response(
                result,
                status_code=status.HTTP_404_NOT_FOUND
            )

        code_type = VerificationTokenType.EMAIL_OTP.value
        otp = self.verification_code_service.create_verification_code(user=user, code_type=code_type)

        # Send OTP via Email
        self.identity_service.resend_otp_email(user, otp)

        result = {
            "message":"OTP resent successfully. Please check your email."
        }
        return Response(result)


class FacebookAuthView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.social_service = SocialService()

    def post(self, request):
        token = request.data.get("token")
        if not token:
            result = {
                "message": _("Token is required")
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        user, error = self.social_service.facebook_auth(token)
        if error:
            result = {
                "message":  _(error)
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        # Generate tokens for the user
        refresh = RefreshToken.for_user(user)
        tokens = {"access": str(refresh.access_token), "refresh": str(refresh)}

        result = {
            "message": _("Facebook authentication successful"),
            "data": {"tokens": tokens, "user": UserSerializer(user).data},
        }
        return Response(result, status=status.HTTP_200_OK)


class SocialAuthCallbackView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.identity_service = IdentityService()

    def post(self, request):
        provider = request.data.get("provider")
        code = request.data.get("code")
        if not provider or not code:
            result = {
                "message": "Provider and code are required",
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        user, error = self.identity_service.handle_social_callback(provider, code)
        if error:
            result = {
                "message": error,
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        tokens = self.identity_service.generate_tokens(user)
        result = {
            "message": "Social authentication successful",
            "data": {"tokens": tokens, "user": UserSerializer(user).data},
        }

        return Response(result, status=status.HTTP_200_OK)


class MeView(BaseApiView):
    @swagger_auto_schema(responses={200: UserDetailSerializer})
    def get(self, request):
        try:
            user = request.user
            serializer = UserDetailSerializer(user)
            result = {
                "message": _("Profile retrieved successfully"),
                "data": serializer.data,
            }
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error retrieving profile: {str(e)}")
            raise e


class ForgotPasswordView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.identity_service = IdentityService()

    @swagger_auto_schema(request_body=ForgotPasswordSerializer)
    @recaptcha(enabled=settings.RECAPTCHA_ENABLE)
    def post(self, request):
        payload = request.data
        serializer = ForgotPasswordSerializer(data=payload)

        if not serializer.is_valid():
            result = {
                "message": _("Invalid input. Please check the provided details."),
                "errors": serializer.errors,
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        email = data.get("email")

        try:
            success = self.identity_service.forgot_password(email=email)

            if success:
                result = {
                    "message": _("Password reset instructions sent to your email"),
                }
                return Response(result, status=status.HTTP_200_OK)
            else:
                result = {
                    "message": _("User with this email does not exist"),
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            logger.error(f"Forgot password error: {str(e)}")
            raise e


class ResetPasswordView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.identity_service = IdentityService()

    @swagger_auto_schema(request_body=ResetPasswordSerializer)
    def post(self, request):
        payload = request.data
        user = request.user

        serializer = ResetPasswordSerializer(data=payload)

        if not serializer.is_valid():
            result = {
                "message": _("Invalid input. Please check the provided details."),
                "errors": serializer.errors,
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        new_password = data.get("new_password")

        try:
            success, message = self.identity_service.reset_password(user, new_password)

            if success:
                result = {
                    "message": _(message),
                }
                return Response(result, status=status.HTTP_200_OK)
            else:
                result = {
                    "message": _(message),
                }

                return Response(result, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Reset password error: {str(e)}")
            raise e


class ChangePasswordView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.identity_service = IdentityService()

    @swagger_auto_schema(request_body=ChangePasswordSerializer)
    def put(self, request):
        payload = request.data
        serializer = ChangePasswordSerializer(data=payload, context={"request": request})
        if not serializer.is_valid():
            first_error_message = serializer.errors[next(iter(serializer.errors))][0]
            result = {
                "message": first_error_message,
                "errors": serializer.errors,
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        old_password = data.get("old_password")
        new_password = data.get("new_password")

        try:
            if not is_password_usable(request.user.password):
                success, message = self.identity_service.set_new_password(
                    request.user, new_password
                )
            else:
                success, message = self.identity_service.change_password(
                    request.user, old_password, new_password
                )

            if success:
                # Log password change activity
                log_activity(
                    request.user, 'password_change',
                    _("User changed password"), request
                )

                result = {
                    "message": _(message),
                }

                return Response(result, status=status.HTTP_200_OK)

            else:
                result = {
                    "message": _(message),
                }

                return Response(result, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Change password error: {str(e)}")
            raise e


class RefreshTokenView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.identity_service = IdentityService()

    def post(self, request):
        try:
            refresh_token = request.data.get("refresh")
            token = self.identity_service.refresh_token(refresh_token)
            result = {
                "message": _("Token refreshed successfully"),
                "data": {"token": token},
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Token refresh error: {str(e)}")
            raise e


class SendVerificationEmailView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.identity_service = IdentityService()
        self.user_service = UserService()
        self.verification_code_service = VerificationCodeService()

    def post(self, request):
        email = request.data.get("email")
        input_type = request.data.get("type")

        if not email:
            result = {
                "message": _("Email is required"),
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = self.user_service.get(email=email)
            print(user)
            # Save OTP to VerificationCode model
            verification_type = getattr(VerificationTokenType, input_type).value
            verification_code = self.verification_code_service.create_verification_code(user=user, code_type=verification_type)

            # Send OTP via Email
            self.identity_service.send_verification_email(user, verification_code)

            result = {
                "message": _("Verification email sent"),
            }
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            raise e


class VerifyEmailView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.identity_service = IdentityService()

    def post(self, request):
        code = request.data.get("code")
        if not code:
            result = {
                "message": _("Verification code is required"),
            }

            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        success = self.identity_service.verify_email(code=code)
        if success:
            result = {
                "message": _("Email verified successfully"),
            }

            return Response(result, status=status.HTTP_200_OK)

        else:
            result = {
                "message": _("Invalid or expired verification code"),
            }

            return Response(result, status=status.HTTP_400_BAD_REQUEST)


class OTPVerifyView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.identity_service = IdentityService()

    def post(self, request):
        otp = request.data.get("otp")
        email = request.data.get("email")

        if not otp:
            result = {
                "message": _("Verification otp is required"),
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        if not email:
            result = {
                "message": _("Email is required"),
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        user = self.identity_service.verify_otp(email=email, otp=otp)

        if user:
            return Response({"message":"Email verified successfully."}, status=status.HTTP_200_OK)

        return Response({"message":"Invalid OTP or user not found."}, status=status.HTTP_400_BAD_REQUEST)


class SendVerificationCodeView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.identity_service = IdentityService()
        self.user_service = UserService()
        self.verification_code_service = VerificationCodeService()

    def post(self, request):
        phone = request.data.get("phone")

        if not phone:
            result = {
                "message": _("Phone number is required"),
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = self.user_service.get(phone=phone)

            # Save OTP to VerificationCode model
            verification_code = self.verification_code_service.create_verification_code(
                user=user,
                code_type=VerificationTokenType.PHONE_OTP.value
            )

            # Send OTP via SMS
            NotificationService._send_sms(
                user.phone, f"Your verification code is: {verification_code}"
            )

            result = {
                "message": _("Verification code sent"),
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            raise e


class VerifyPhoneView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.identity_service = IdentityService()

    def post(self, request):
        code = request.data.get("code")

        if not code:
            result = {
                "message": _("Verification code is required"),
            }

            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        success = self.identity_service.verify_phone(code)
        if success:
            result = {
                "message": _("Phone verified successfully"),
            }

            return Response(result, status=status.HTTP_200_OK)
        else:
            result = {
                "message": _("Invalid or expired verification code"),
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)


class VerifyEmailLinkView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.identity_service = IdentityService()

    def get(self, request):
        code = request.query_params.get("code")
        if not code:
            result = {
                "message": _("Verification code is required"),
            }

            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        success = self.identity_service.verify_email(code=code)
        if success:
            result = {
                "message": _("Email verified successfully"),
            }

            return Response(result, status=status.HTTP_200_OK)

        else:
            result = {
                "message": _("Invalid or expired verification code"),
            }

            return Response(result, status=status.HTTP_400_BAD_REQUEST)

class ValidateEmailView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.identity_service = IdentityService()

    def get(self, request):
        email = request.query_params.get("email", None)

        if not email:
            return Response({"message": _("Email is required")}, status=400)
        try:
            user = self.identity_service.get(email=email)
            is_email_exist = True if user else False
            result = {
                "message" : _("The email has been already registered"),
                "data": {"is_exists": is_email_exist},
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CompanyListView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e

class UpdateNameView(BaseApiView):
    @swagger_auto_schema(request_body=NameUpdateSerializer)
    def put(self, request):
        try:
            serializer = NameUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({
                    "message": "Invalid input",
                    "errors": serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

            user = request.user
            user.name = serializer.validated_data['name']
            user.save()

            return Response({
                "message": "Name updated successfully",
                "data": {"name": user.name}
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Name update error: {str(e)}")
            raise e

class UpdateProfilePictureView(BaseApiView):
    parser_classes = [MultiPartParser, FormParser]

    @swagger_auto_schema(
        request_body=ProfilePictureSerializer,
        responses={
            200: ProfilePictureSerializer,
            400: 'Validation Error'
        }
    )
    def put(self, request):
        try:
            serializer = ProfilePictureSerializer(data=request.data)
            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            user = request.user

            # Delete old profile picture if exists
            if user.profile_picture:
                if os.path.exists(user.profile_picture.path):
                    os.remove(user.profile_picture.path)

            user.profile_picture = serializer.validated_data['profile_picture']
            user.save()

            result = {
                "message": "Profile picture updated successfully",
                "data": {
                    "profile_picture_url": f"{MEDIA_BASE_URL}{user.profile_picture.url}"
                }
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Profile picture update error: {str(e)}")
            raise e

class DeleteAccountView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()

    @swagger_auto_schema(
        request_body=DeleteAccountSerializer,
        responses={
            200: 'Account deleted successfully',
            400: 'Bad Request',
            401: 'Unauthorized',
            500: 'Internal Server Error'
        }
    )
    def delete(self, request):
        try:
            serializer = DeleteAccountSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({
                    'status': 'error',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

            user = self.user_service.get(id=request.user.id)
            if not user:
                return Response({"message":"User not Found"}, status=status.HTTP_400_BAD_REQUEST)

            success, message = DeleteAccountService.delete_user_account(
                user=user,
                password=serializer.validated_data['password']
            )

            if not success:
                return Response({
                    'status': 'error',
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                'status': 'success',
                'message': 'Account deleted successfully'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Delete account error: {str(e)}")
            return Response({
                'status': 'error',
                'message': 'An error occurred while deleting account'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class UserDevicesView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.device_service = DeviceService()

    def get(self, request):
        try:
            # Register the current device
            self.device_service.register_device(request.user, request)

            # Retrieve all devices for the user
            devices = self.device_service.get_user_devices(request.user.id)
            serializer = UserDeviceSerializer(devices, many=True)
            result = {
                "message": _("User devices retrieved successfully."),
                "data": serializer.data
            }
            return Response(result, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error({"event": "UserDevicesView:get", "error": str(e)})
            result = {
                "message": _("An error occurred while retrieving user devices."),
                "errors": str(e)
            }
            return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request):
        try:
            device_id = request.query_params.get('device_id')
            if device_id:
                self.device_service.delete_device(device_id)
                message = _("Device logged out successfully")
            else:
                self.device_service.delete_all_devices(
                    request.user.id,
                    request.query_params.get('except_current')
                )
                message = _("All devices logged out successfully")
            result = {
                "message": message,
                "data": None
            }
            return Response(result, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error({"event": "UserDevicesView:delete", "error": str(e)})
            result = {
                "message": _("An unexpected error occurred"),
                "data": None,
                "errors": str(e)
            }
            return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)