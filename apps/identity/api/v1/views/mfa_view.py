import pyotp
from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import status, Response
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView
from common.constants import MFAType
from apps.identity.services import MFAService

logger = Logger(__name__)


class SetupMFAView(BaseApiView):
    def post(self, request):
        try:
            mfa_setup = MFAService().create_mfa_setup(request.user)
            secret = mfa_setup.secret
            backup_codes = mfa_setup.backup_codes

            request.user.mfa_enabled = True
            request.user.save()

            totp = pyotp.TOTP(secret)
            provisioning_uri = totp.provisioning_uri(
                request.user.email, issuer_name="GPT360"
            )

            result = {
                "message": _("MFA setup successful"),
                "data": {
                    "secret": secret,
                    "backup_codes": backup_codes,
                    "qr_uri": provisioning_uri,
                }
            }
            return Response(result)
        
        except Exception as e:
            logger.error(f"MFA setup error: {str(e)}")
            raise e

class VerifyMFAView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.mfa_service = MFAService()

    def post(self, request):
        code = request.data.get("code")
        if not code:
            result = {
                "message": _("MFA code is required"),
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        try:
            mfa_setup = self.mfa_service.get_mfa_setup(user=request.user, type=MFAType.TOTP.value)
            totp = pyotp.TOTP(mfa_setup.secret)

            if totp.verify(code):
                result = {
                    "message": _("MFA verification successful"),
                }
                return Response(result, status=status.HTTP_200_OK)
            else:
                result = {
                    "message": _("Invalid MFA code"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
        except Exception as e:
            raise e

class DisableMFAView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.mfa_service = MFAService()

    def post(self, request):
        try:
            mfa_setup = self.mfa_service.get_mfa_setup(user=request.user)
            mfa_setup.delete()

            request.user.mfa_enabled = False
            request.user.save()

            result = {
                "message": _("MFA disabled successfully"),
            }
            return Response(result, status=status.HTTP_200_OK)
        
        except Exception as e:
            raise e
