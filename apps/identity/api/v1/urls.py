from django.urls import path
from apps.identity.api.v1 import views

app_name = 'identity'

urlpatterns = [
    # Authentication
    path('login/', views.LoginView.as_view(), name='login'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    path('register/', views.RegisterView.as_view(), name='register'),
    path('me/', views.MeView.as_view(), name='me'),
    path('validate-email/', views.ValidateEmailView.as_view(), name='validate email'),

    # Password Management
    path('password/forgot/', views.ForgotPasswordView.as_view(), name='password-forgot'),
    path('password/reset/', views.ResetPasswordView.as_view(), name='password-reset'),
    path('password/change/', views.ChangePasswordView.as_view(), name='password-change'),

    # Token Management
    path('token/refresh/', views.RefreshTokenView.as_view(), name='token-refresh'),

    # Verification
    path('verification/email/send/', views.SendVerificationEmailView.as_view(), name='email-verification-send'),
    path('verification/email/verify/', views.VerifyEmailView.as_view(), name='email-verification'),
    path('verification/email/link/', views.VerifyEmailLinkView.as_view(), name='email-verification-link'),
    path("verification/email/otp/", views.OTPVerifyView.as_view(),  name="email-verification-otp"),
    path("verification/email/otp/resend/", views.ResendOTPView.as_view(), name='resend-otp'),
    path('verification/phone/send/', views.SendVerificationCodeView.as_view(), name='phone-verification-send'),
    path('verification/phone/verify/', views.VerifyPhoneView.as_view(), name='phone-verification'),

    # MFA
    path('mfa/', views.SetupMFAView.as_view(), name='mfa-setup'),
    path('mfa/verify/', views.VerifyMFAView.as_view(), name='mfa-verify'),
    path('mfa/disable/', views.DisableMFAView.as_view(), name='mfa-disable'),

    # Social Authentication
    path('social/google/', views.GoogleAuthView.as_view(), name='social-google'),
    path('social/facebook/', views.FacebookAuthView.as_view(), name='social-facebook'),
    path('social/callback/', views.SocialAuthCallbackView.as_view(), name='social-callback'),
    
    #name and profile picture update
    path('update/name/', views.UpdateNameView.as_view(), name='update-name'),
    path('update/profile/picture/', views.UpdateProfilePictureView.as_view(), name='update-profile-picture'),
    #Delete account
    path('delete/account/', views.DeleteAccountView.as_view(), name='delete-account'),
    #Device Management
    path('logged/in/devices/', views.UserDevicesView.as_view(), name='user-devices'),
]
