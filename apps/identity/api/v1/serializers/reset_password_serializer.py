from rest_framework import serializers
from common.serializers import BaseSerializer


class ResetPasswordSerializer(BaseSerializer):
    new_password = serializers.CharField(
        required=True, error_messages={"required": "New password is required."}
    )
    confirm_password = serializers.CharField(
        required=True, error_messages={"required": "Confirm password is required."}
    )

    def _validate_password(self, password, confirm_password):
        if password != confirm_password:
            raise serializers.ValidationError({"confirm_password": "Passwords do not match."})

        return password

    def validate(self, data):
        self._validate_password(data.get('new_password'), data.get('confirm_password'))
        
        return data
