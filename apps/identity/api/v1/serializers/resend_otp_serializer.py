from rest_framework import serializers
from django.contrib.auth import get_user_model
from common.serializers import BaseSerializer

User = get_user_model()

class ResendOTPSerializer(BaseSerializer):
    email = serializers.EmailField()
    
    def validate_email(self, value):
        """Ensure the user exists and is active."""
        if not User.objects.filter(email=value, is_active=True).exists():
            raise serializers.ValidationError("User with this email does not exist or is inactive.")
        return value