from rest_framework import serializers
from common.serializers import BaseSerializer
from django.utils.translation import gettext_lazy as _


class ChangePasswordSerializer(BaseSerializer):
    old_password = serializers.CharField(
        required=False,
        allow_null=True
    )
    new_password = serializers.CharField(
        required=True,
        error_messages={"required": _("New password is required.")},
    )
    confirm_password = serializers.CharField(
        required=True,
        error_messages={"required": _("Confirm password is required.")},
    )

    def validate(self, attrs):
        user = self.context["request"].user
        old_password = attrs.get("old_password")
        new_password = attrs.get("new_password")
        confirm_password = attrs.get("confirm_password")

        if user.has_usable_password() and not old_password:
            raise serializers.ValidationError(_("Old password is required."))

        if old_password and not user.check_password(old_password):
            raise serializers.ValidationError(_("Old password is incorrect."))

        if old_password and old_password == new_password:
            raise serializers.ValidationError(_("New password cannot be the same as the old password."))

        if new_password != confirm_password:
            raise serializers.ValidationError(_("New password and confirm password must match."))

        return attrs