from rest_framework import serializers
from django.conf import settings


class ProfilePictureSerializer(serializers.Serializer):
    profile_picture = serializers.ImageField(
        required=True,
        error_messages={
            'required': 'Please select an image to upload',
            'invalid': 'Invalid image format'
        }
    )

    def validate_profile_picture(self, value):
        # Check file size
        max_size = getattr(settings, 'MAX_UPLOAD_SIZE', 5 * 1024 * 1024)  # Default 5MB
        if value.size > max_size:
            raise serializers.ValidationError(
                f"Image size cannot exceed {max_size/1024/1024}MB"
            )

        # Check file format
        allowed_formats = getattr(settings, 'ALLOWED_IMAGE_FORMATS', 
                                ['image/jpeg', 'image/png', 'image/jpg'])
        if value.content_type not in allowed_formats:
            raise serializers.ValidationError(
                f"Unsupported file format. Allowed formats: {', '.join(allowed_formats)}"
            )

        return value