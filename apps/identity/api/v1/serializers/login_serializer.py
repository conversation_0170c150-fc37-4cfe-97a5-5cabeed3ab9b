from rest_framework import serializers
from common.serializers import BaseSerializer

class LoginSerializer(BaseSerializer):
    email = serializers.EmailField(
        required=True,
        error_messages={"required": "Email is required."}
    )
    password = serializers.CharField(
        write_only=True,
        required=True,
        style={"input_type": "password"},
        error_messages={"required": "Password is required."}
    )

    # class Meta:
    #     fields = ["email", "password"]

    