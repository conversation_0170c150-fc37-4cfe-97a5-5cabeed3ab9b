from rest_framework import serializers

class DeleteAccountSerializer(serializers.Serializer):
    password = serializers.CharField(write_only=True, required=True)
    confirmation_text = serializers.CharField(write_only=True, required=True)

    def validate_confirmation_text(self, value):
        if value != "DELETE MY ACCOUNT":
            raise serializers.ValidationError("Please type 'DELETE MY ACCOUNT' to confirm deletion")
        return value