from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from common.serializers import BaseModelSerializer
from common.constants import Language

User = get_user_model()


class RegistrationSerializer(BaseModelSerializer):
    email = serializers.EmailField(
        required=True,
        error_messages={"required": _("Email is required.")}
    )
    password = serializers.CharField(
        write_only=True,
        required=True,
        style={"input_type": "password"},
        error_messages={"required": _("Password is required.")}
    )
    confirm_password = serializers.CharField(
        write_only=True,
        required=True,
        style={"input_type": "password"},
        error_messages={"required": _("Please confirm your password.")}
    )
    name = serializers.CharField(
        required=True,
        error_messages={"required": _("Full name is required.")}
    )

    class Meta:
        model = User
        fields = [
            'id', 'email', 'name', 'password', 'confirm_password',
        ]
        read_only_fields = ('id',)

    def _validate_password(self, password, confirm_password):
        if password != confirm_password:
            raise serializers.ValidationError({"password": _("Passwords do not match.")})

        return password

    def validate(self, data):
        self._validate_password(data.get('password'), data.get('confirm_password'))

        return data
