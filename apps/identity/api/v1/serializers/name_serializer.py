from rest_framework import serializers
from common.serializers import BaseSerializer

class NameUpdateSerializer(BaseSerializer):
    name = serializers.CharField(
        max_length=255,
        required=True,
        error_messages={
            'required': 'Name is required',
            'blank': 'Name cannot be empty'
        }
    )

    def validate_name(self, value):
        if not value.strip():
            raise serializers.ValidationError("Name cannot be empty")
        return value.strip()