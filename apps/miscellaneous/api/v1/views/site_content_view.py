from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_libraries.drf_yasg import swagger_auto_schema
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView

from apps.miscellaneous.services import SiteContentService
from apps.miscellaneous.api.v1.serializers import SiteContentSerializer

logger = Logger(__name__)

class SiteContentListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.site_content_service = SiteContentService()

    @swagger_auto_schema(responses={200: SiteContentSerializer(many=True)})
    def get(self, request, section=None):  
        logger.info({"event": "SiteContentListView:get", "message": "Fetching site content data", "path": request.path})

        try:
            site_content = self.site_content_service.list(section=section)

            # Retrieve all site content
            if not site_content:
                result = {
                    "message": _("Site content not found"),
                    "errors": {"site_content": "No site content found."},
                }
                logger.warning({"event": "SiteContentListView:get", "message": "Site content not found"})
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            # Successfully retrieved site content
            result = {
                "message": _("Site content retrieved successfully"),
                "data": SiteContentSerializer(site_content, many=True).data,
            }
            logger.info({"event": "SiteContentListView:get", "message": "Site content retrieved successfully"})
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "SiteContentListView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e
