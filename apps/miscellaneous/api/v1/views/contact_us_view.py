from django.utils import timezone
from django.conf import settings
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.decorators import recaptcha
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import PublicApiView
from dxh_libraries.rest_framework import Response, status

from apps.cross_cutting import EmailService
from apps.miscellaneous.api.v1.serializers import ContactFormSerializer

logger = Logger(__name__)

class ContactView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.email_service = EmailService()

    @recaptcha(enabled=settings.RECAPTCHA_ENABLE)    
    def post(self, request, format=None):
        serializer = ContactFormSerializer(data=request.data)
        
        if serializer.is_valid():
            name = serializer.validated_data['name']
            email = serializer.validated_data['email']

            # Get current system time
            current_time = timezone.localtime()
            formatted_time = current_time.strftime("%B %d, %Y, %I:%M %p")

            context = {
                **serializer.validated_data,
                "submitted_on": formatted_time,
                "ip_address": request.META.get('REMOTE_ADDR')
            }

            try:
                # Send acknowledgement to user
                self.email_service.send_email(
                    to_email=email,
                    subject="Thank You for Contacting Us",
                    template_name="acknowledgement",
                    context=context
                )
                logger.info(f"Acknowledgement email sent to: {serializer.validated_data['email']}")

                # Send notification to support
                self.email_service.send_email(
                    to_email=settings.CUSTOMER_SUPPORT_EMAIL,
                    subject="New Contact Form Submission",
                    template_name="customer_query",
                    context=context,
                )
                logger.info(f"Customer query email sent to: {settings.CUSTOMER_SUPPORT_EMAIL}")

            except Exception as e: 
                logger.error(f"Error sending email: {e}")
                return Response(
                    {"message": "Error sending message. Please try again later."},  
                    status=status.HTTP_400_BAD_REQUEST,
                )

            return Response({"message": "Message sent successfully!"}, status=status.HTTP_200_OK)

        return Response({"errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
