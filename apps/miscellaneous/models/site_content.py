from django.db import models
from django.utils.translation import gettext_lazy as _
from django_quill.fields import QuillField
from common.models import BaseModel
from common.constants import SECTION_CHOICES


class SiteContent(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_("Company"),
        related_name="company_site_content",
    )
    title = models.CharField(max_length=100, verbose_name=_("Title"))
    section = models.CharField(
        max_length=50,
        choices=SECTION_CHOICES,
        unique=True,
        verbose_name=_("Content Section"),
    )
    content = QuillField(verbose_name=_("Content"))

    class Meta:
        db_table = "miscellaneous_site_contents"
        verbose_name = _("Site Content")
        verbose_name_plural = _("Site Contents")

    def __str__(self):
        return self.title