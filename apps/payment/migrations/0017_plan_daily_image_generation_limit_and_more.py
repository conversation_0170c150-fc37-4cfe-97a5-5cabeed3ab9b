# Generated by Django 5.1.5 on 2025-05-18 12:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payment', '0016_plan_daily_file_upload_limit_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='plan',
            name='daily_image_generation_limit',
            field=models.IntegerField(default=0, verbose_name='Image Generation Limit'),
        ),
        migrations.AddField(
            model_name='subscription',
            name='daily_image_generation_limit',
            field=models.IntegerField(default=0, verbose_name='Image Generation Limit'),
        ),
    ]
