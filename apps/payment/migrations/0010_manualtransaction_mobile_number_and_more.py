# Generated by Django 5.1.5 on 2025-04-24 10:16

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payment', '0009_rename_message_limit_plan_daily_message_limit_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='manualtransaction',
            name='mobile_number',
            field=models.CharField(blank=True, help_text='Mobile number used for payment verification', max_length=20, null=True, validators=[django.core.validators.RegexValidator(message="Mobile number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')], verbose_name='Mobile Number'),
        ),
        migrations.AddField(
            model_name='manualtransaction',
            name='payment_proof',
            field=models.ImageField(blank=True, help_text='Screenshot or image of payment confirmation', null=True, upload_to='uploads/%Y/%m/%d/', verbose_name='Payment Proof'),
        ),
    ]
