# Generated by Django 5.1.5 on 2025-03-17 06:08

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payment', '0002_payment_uuid_paymentlog_uuid_paymentmethod_uuid_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='paymentmethod',
            name='expiry_date',
        ),
        migrations.RemoveField(
            model_name='paymentmethod',
            name='user',
        ),
        migrations.AddField(
            model_name='paymentmethod',
            name='brand',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Brand'),
        ),
        migrations.AddField(
            model_name='paymentmethod',
            name='exp_month',
            field=models.IntegerField(blank=True, null=True, verbose_name='Expiry Month'),
        ),
        migrations.AddField(
            model_name='paymentmethod',
            name='exp_year',
            field=models.IntegerField(blank=True, null=True, verbose_name='Expiry Year'),
        ),
        migrations.CreateModel(
            name='StripeCustomer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('customer_id', models.CharField(max_length=255, unique=True, verbose_name='Customer ID')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='stripe_customer', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Stripe Customer',
                'verbose_name_plural': 'Stripe Customers',
                'db_table': 'stripe_customers',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='paymentmethod',
            name='stripe_customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payment_methods', to='payment.stripecustomer', verbose_name='Stripe Customer'),
        ),
    ]
