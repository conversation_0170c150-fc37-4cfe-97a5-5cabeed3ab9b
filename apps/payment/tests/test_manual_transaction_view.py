from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from apps.payment.models import ManualTransaction, Plan
from apps.payment.api.v1.serializers import ManualTransactionListSerializer
from common.constants import StatusChoicesPayment
import uuid

User = get_user_model()


class ManualTransactionViewTest(TestCase):
    """Test suite for the ManualTransactionView API."""

    def setUp(self):
        """Set up test data."""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            username='test',
        )

        # Create a test plan
        self.plan = Plan.objects.create(
            name='Test Plan',
            code='test_plan',
            price=100.00,
            currency='USD',
            interval='month'
        )

        # Create a test transaction
        self.transaction = ManualTransaction.objects.create(
            user=self.user,
            plan=self.plan,
            payment_method='Bank Transfer',
            transaction_id=str(uuid.uuid4()),
            description='Test transaction'
        )

        # Set up the API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        # URLs
        self.list_url = reverse('payments:manual-transaction-list')
        self.detail_url = reverse('payments:manual-transaction-detail', args=[self.transaction.id])

    def test_get_transaction_list(self):
        """Test retrieving a list of transactions."""
        response = self.client.get(self.list_url)

        # Get the transactions from the database
        transactions = ManualTransaction.objects.filter(user=self.user).order_by('-created_at')
        serializer = ManualTransactionListSerializer(transactions, many=True)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(len(response.data['data']), 1)

    def test_get_transaction_detail(self):
        """Test retrieving a single transaction."""
        response = self.client.get(self.detail_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['id'], self.transaction.id)

    def test_get_nonexistent_transaction(self):
        """Test retrieving a transaction that doesn't exist."""
        url = reverse('payments:manual-transaction-detail', args=[999])  # Non-existent ID
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_transaction(self):
        """Test creating a new transaction."""
        data = {
            'plan': self.plan.id,
            'payment_method': 'Credit Card',
            'transaction_id': str(uuid.uuid4()),
            'description': 'New test transaction'
        }

        response = self.client.post(self.list_url, data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('data', response.data)
        self.assertEqual(ManualTransaction.objects.count(), 2)

        # Verify the transaction was created with the correct data
        new_transaction = ManualTransaction.objects.latest('created_at')
        self.assertEqual(new_transaction.user, self.user)
        self.assertEqual(new_transaction.plan, self.plan)
        self.assertEqual(new_transaction.payment_method, data['payment_method'])
        self.assertEqual(new_transaction.transaction_id, data['transaction_id'])
        self.assertEqual(new_transaction.description, data['description'])
        self.assertEqual(new_transaction.status, StatusChoicesPayment.PENDING.value)

    def test_create_transaction_invalid_data(self):
        """Test creating a transaction with invalid data."""
        # Missing required fields
        data = {
            'description': 'Invalid transaction'
        }

        response = self.client.post(self.list_url, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Ensure no new transaction was created
        self.assertEqual(ManualTransaction.objects.count(), 1)

    def test_unauthenticated_access(self):
        """Test that unauthenticated users cannot access the API."""
        # Logout
        self.client.logout()

        # Try to access the list endpoint
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        # Try to access the detail endpoint
        response = self.client.get(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        # Try to create a transaction
        data = {
            'plan': self.plan.id,
            'payment_method': 'Credit Card',
            'transaction_id': str(uuid.uuid4()),
            'description': 'New test transaction'
        }
        response = self.client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
