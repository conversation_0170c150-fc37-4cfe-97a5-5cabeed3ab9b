from django.test import TestCase
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
import uuid

from django.contrib.auth import get_user_model
from apps.payment.models import ManualTransaction, Plan, Subscription
from apps.payment.services.manual_transaction_service import ManualTransactionService
from common.constants import StatusChoicesPayment, SubscriptionType

User = get_user_model()


class ManualTransactionServiceTest(TestCase):
    """Test suite for the ManualTransactionService."""

    def setUp(self):
        """Set up test data."""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            username='test',
            subscription_type=SubscriptionType.FREE.value
        )

        # Create a test plan
        self.plan = Plan.objects.create(
            name='Test Plan',
            code='test_plan',
            price=100.00,
            currency='USD',
            interval='monthly',
            tokens_included=1000
        )

        # Create a test transaction
        self.transaction = ManualTransaction.objects.create(
            user=self.user,
            plan=self.plan,
            payment_method='Bank Transfer',
            transaction_id=str(uuid.uuid4()),
            description='Test transaction',
            status=StatusChoicesPayment.PENDING.value
        )

        # Initialize the service
        self.service = ManualTransactionService()

    def test_update_transaction_status_to_completed(self):
        """Test updating a transaction status to completed."""
        # Update the transaction status to completed
        transaction, message = self.service.update_transaction_status(
            transaction_id=self.transaction.id,
            new_status=StatusChoicesPayment.COMPLETED.value
        )

        # Refresh the user from the database
        self.user.refresh_from_db()

        # Check that the transaction status was updated
        self.assertEqual(transaction.status, StatusChoicesPayment.COMPLETED.value)
        
        # Check that a subscription was created
        subscriptions = Subscription.objects.filter(user=self.user, plan=self.plan)
        self.assertEqual(subscriptions.count(), 1)
        
        # Check that the subscription has the correct data
        subscription = subscriptions.first()
        self.assertEqual(subscription.status, 'active')
        self.assertEqual(subscription.tokens_balance, self.plan.tokens_included)
        
        # Check that the current_period_end is set to approximately 30 days from now
        expected_end = timezone.now() + timedelta(days=30)
        self.assertIsNotNone(subscription.current_period_end)
        date_diff = abs((subscription.current_period_end - expected_end).total_seconds())
        self.assertLess(date_diff, 60)  # Allow for a small difference due to test execution time
        
        # Check that the user's subscription type was updated
        self.assertEqual(self.user.subscription_type, self.plan.code)

    def test_update_transaction_status_already_set(self):
        """Test updating a transaction status to the same value."""
        # Update the transaction status to the same value
        transaction, message = self.service.update_transaction_status(
            transaction_id=self.transaction.id,
            new_status=StatusChoicesPayment.PENDING.value
        )

        # Check that the message indicates the status is already set
        self.assertEqual(message, "Transaction status is already set to this value")
        
        # Check that no subscription was created
        self.assertEqual(Subscription.objects.count(), 0)

    def test_update_transaction_status_not_found(self):
        """Test updating a non-existent transaction."""
        # Update a non-existent transaction
        transaction, message = self.service.update_transaction_status(
            transaction_id=9999,
            new_status=StatusChoicesPayment.COMPLETED.value
        )

        # Check that the transaction is None and the message indicates not found
        self.assertIsNone(transaction)
        self.assertEqual(message, "Transaction not found")
        
        # Check that no subscription was created
        self.assertEqual(Subscription.objects.count(), 0)
