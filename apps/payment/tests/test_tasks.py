from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import timedelta

from apps.payment.models import Plan, Subscription
from apps.payment.tasks import change_user_to_free_plan
from common.constants import SubscriptionType

User = get_user_model()


class TasksTestCase(TestCase):
    """Test suite for payment tasks."""

    def setUp(self):
        """Set up test data."""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            username='test',
            subscription_type='pro'  # Start with a paid plan
        )

        # Create test plans
        self.pro_plan = Plan.objects.create(
            name='Pro Plan',
            code='pro',
            price=100.00,
            currency='USD',
            interval='monthly',
            tokens_included=1000
        )

        self.starter_plan = Plan.objects.create(
            name='Starter Plan',
            code='starter',
            price=50.00,
            currency='USD',
            interval='monthly',
            tokens_included=500
        )

    def test_change_user_to_free_plan_with_no_other_subscriptions(self):
        """Test that the task correctly cancels an expired subscription and changes user to free plan."""
        # Create an expired subscription
        expired_subscription = Subscription.objects.create(
            user=self.user,
            plan=self.pro_plan,
            status='active',
            tokens_balance=1000,
            current_period_end=timezone.now() - timedelta(days=1)  # Expired 1 day ago
        )

        # Execute the task
        result = change_user_to_free_plan(self.user.id)

        # Refresh the user from the database
        self.user.refresh_from_db()

        # Refresh the subscription from the database
        expired_subscription.refresh_from_db()

        # Check that the user's subscription type was changed to free
        self.assertEqual(self.user.subscription_type, SubscriptionType.FREE.value)

        # Check that the subscription status was changed to canceled
        self.assertEqual(expired_subscription.status, 'canceled')

        # Check that the canceled_at timestamp was set
        self.assertIsNotNone(expired_subscription.canceled_at)

        # Check that the task returned a success message
        self.assertTrue("Successfully changed user" in result)

    def test_change_user_to_free_plan_with_newer_subscription(self):
        """Test that the task correctly handles a user with a newer active subscription."""
        # Create an expired subscription
        expired_subscription = Subscription.objects.create(
            user=self.user,
            plan=self.pro_plan,
            status='active',
            tokens_balance=1000,
            current_period_end=timezone.now() - timedelta(days=1)  # Expired 1 day ago
        )

        # Create a newer active subscription
        new_subscription = Subscription.objects.create(
            user=self.user,
            plan=self.starter_plan,
            status='active',
            tokens_balance=500,
            current_period_end=timezone.now() + timedelta(days=29)  # Valid for 29 more days
        )

        # Execute the task
        result = change_user_to_free_plan(self.user.id)

        # Refresh the objects from the database
        self.user.refresh_from_db()
        expired_subscription.refresh_from_db()
        new_subscription.refresh_from_db()

        # Check that the expired subscription was canceled
        self.assertEqual(expired_subscription.status, 'canceled')
        self.assertIsNotNone(expired_subscription.canceled_at)

        # Check that the new subscription is still active
        self.assertEqual(new_subscription.status, 'active')

        # Check that the user's subscription type was updated to the new plan's code
        self.assertEqual(self.user.subscription_type, self.starter_plan.code)

        # Check that the task returned the appropriate message
        self.assertTrue(f"Updated user {self.user.id} subscription to {self.starter_plan.code}" in result)

    def test_change_user_to_free_plan_with_active_subscription(self):
        """Test that the task doesn't affect future subscriptions."""
        # Create an active subscription that hasn't expired yet
        active_subscription = Subscription.objects.create(
            user=self.user,
            plan=self.pro_plan,
            status='active',
            tokens_balance=1000,
            current_period_end=timezone.now() + timedelta(days=15)  # Still valid
        )

        # Execute the task
        change_user_to_free_plan(self.user.id)

        # Refresh the objects from the database
        self.user.refresh_from_db()
        active_subscription.refresh_from_db()

        # Check that the subscription is still active
        self.assertEqual(active_subscription.status, 'active')

        # Check that the user's subscription type is still pro
        self.assertEqual(self.user.subscription_type, self.pro_plan.code)
