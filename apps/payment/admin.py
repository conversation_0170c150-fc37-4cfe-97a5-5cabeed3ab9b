from django.contrib import admin
from django.utils.html import format_html
from apps.payment.models import (
    PaymentMethod, Payment, Refund, PaymentLog, StripeCustomer,
    Plan, Feature, Subscription, StripeWebhookLog, ManualTransaction, 
    PaymentMethodDetails, SubscriptionRequest
)
from common.config import BaseAdmin
from common.constants import StatusChoicesPayment

from django.utils.translation import gettext_lazy as _
from apps.payment.services import SubscriptionRequestService


@admin.register(PaymentMethodDetails)
class PaymentMethodDetailsAdmin(BaseAdmin):
    list_display = ('name', 'type', 'pay_to', 'account_type', 'created_at')
    list_filter = ('type', 'created_at')
    search_fields = ('name', 'pay_to', 'account_type')
    ordering = ('-created_at',)


# --------------------------------------------------------
# PAYMENT METHOD ADMIN CONFIGURATION
# # --------------------------------------------------------
# @admin.register(PaymentMethod)
# class PaymentMethodAdmin(BaseAdmin):
#     list_display = ('stripe_customer', 'type', 'provider', 'last_digits', 'is_default', 'created_at')
#     list_filter = ('type', 'provider', 'is_default')
#     search_fields = ('stripe_customer__user__email', 'last_digits', 'token_id')
#     readonly_fields = ('token_id',)
#     ordering = ('-created_at',)


# # --------------------------------------------------------
# # PAYMENT ADMIN CONFIGURATION
# # --------------------------------------------------------
# @admin.register(Payment)
# class PaymentAdmin(BaseAdmin):
#     list_display = ('transaction_id', 'user', 'get_amount', 'currency', 'status', 'created_at')
#     list_filter = ('status', 'currency', 'created_at')
#     search_fields = ('user__email', 'transaction_id', 'description')
#     readonly_fields = ('transaction_id', 'provider_response', 'error_message')
#     ordering = ('-created_at',)
#     date_hierarchy = 'created_at'

#     def get_amount(self, obj):
#         color = 'green' if obj.status == 'completed' else 'red' if obj.status == 'failed' else 'orange'
#         return format_html(
#             '<span style="color: {};">{} {}</span>',
#             color, obj.amount, obj.currency
#         )
#     get_amount.short_description = 'Amount'
#     get_amount.admin_order_field = 'amount'


# --------------------------------------------------------
# REFUND ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(Refund)
class RefundAdmin(BaseAdmin):
    list_display = ('refund_id', 'payment', 'amount', 'status', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('refund_id', 'payment__transaction_id', 'reason')
    readonly_fields = ('refund_id', 'provider_response', 'error_message')
    ordering = ('-created_at',)


# # --------------------------------------------------------
# # PAYMENT LOG ADMIN CONFIGURATION
# # --------------------------------------------------------
# @admin.register(PaymentLog)
# class PaymentLogAdmin(BaseAdmin):
#     list_display = ('payment', 'action', 'status', 'created_at')
#     list_filter = ('action', 'status', 'created_at')
#     search_fields = ('payment__transaction_id', 'message')
#     readonly_fields = ('payment', 'action', 'status', 'message', 'metadata')
#     ordering = ('-created_at',)


# --------------------------------------------------------
# STRIPE CUSTOMER ADMIN CONFIGURATION
# --------------------------------------------------------
# @admin.register(StripeCustomer)
# class StripeCustomerAdmin(BaseAdmin):
#     list_display = ('user', 'customer_id', 'created_at')
#     search_fields = ('user__email', 'customer_id')
#     readonly_fields = ('customer_id',)
#     ordering = ('-created_at',)

# --------------------------------------------------------
# FEATURE ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(Feature)
class FeatureAdmin(BaseAdmin):
    list_display = ('name', 'description', 'created_at')
    search_fields = ('name', 'description')
    ordering = ('-created_at',)


# --------------------------------------------------------
# PLAN ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(Plan)
class PlanAdmin(BaseAdmin):
    list_display = ('name', 'code', 'price', 'interval', 'daily_message_limit','daily_file_upload_limit', 'daily_image_generation_limit', 'tokens_included', 'created_at')
    list_filter = ('interval', 'created_at', 'features')
    search_fields = ('name', 'code', 'stripe_price_id', 'stripe_product_id')
    readonly_fields = ('stripe_price_id', 'stripe_product_id')
    ordering = ('-created_at',)
    filter_horizontal = ('features',)


# --------------------------------------------------------
# SUBSCRIPTION ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(Subscription)
class SubscriptionAdmin(BaseAdmin):
    list_display = ('user', 'plan', 'get_status', 'daily_message_limit','daily_file_upload_limit', 'daily_image_generation_limit', 'tokens_balance', 'current_period_end', 'created_at')
    list_filter = ('status', 'trial_end', 'current_period_end', 'canceled_at', 'created_at')
    search_fields = ('user__email', 'plan__name', 'stripe_subscription_id')
    readonly_fields = ('stripe_subscription_id',)
    ordering = ('-created_at',)

    def get_status(self, obj):
        color_map = {
            'active': 'green',
            'canceled': 'red',
            'past_due': 'orange',
            'incomplete': 'blue',
            'incomplete_expired': 'gray',
        }
        color = color_map.get(obj.status, 'black')
        return format_html(
            '<span style="color: {};">{}</span>',
            color, obj.get_status_display()
        )
    get_status.short_description = 'Status'
    get_status.admin_order_field = 'status'

# # --------------------------------------------------------
# # STRIPE WEBHOOK LOG ADMIN CONFIGURATION
# # --------------------------------------------------------
# @admin.register(StripeWebhookLog)
# class StripeWebhookLogAdmin(BaseAdmin):
#     list_display = ('customer_id', 'event_id', 'event_type', 'created_at')
#     search_fields = ('customer_id', 'event_id', 'event_type')
#     readonly_fields = ('customer_id', 'event_id', 'event_type', 'event_data')
#     ordering = ('-created_at',)


# --------------------------------------------------------
# MANUAL TRANSACTION ADMIN CONFIGURATION
# --------------------------------------------------------
@admin.register(ManualTransaction)
class ManualTransactionAdmin(BaseAdmin):
    list_display = ('transaction_id', 'user', 'plan', 'get_status', 'payment_method', 'mobile_number', 'created_at', 'actions_buttons')
    list_filter = ('status', 'payment_method', 'created_at')
    search_fields = ('transaction_id', 'user__email', 'plan__name', 'description', 'mobile_number')
    readonly_fields = ('transaction_id', 'metadata')
    ordering = ('-created_at',)

    def get_urls(self):
        from django.urls import path
        urls = super().get_urls()
        custom_urls = []

        # Add URL patterns for each status
        for status in StatusChoicesPayment:
            custom_urls.append(
                path(
                    f'<path:object_id>/change-status/{status.value}/',
                    self.admin_site.admin_view(self.change_status),
                    name=f'payment_manualtransaction_change_status_{status.value}',
                    kwargs={'new_status': status.value}
                ),
            )

        return custom_urls + urls

    def change_status(self, request, object_id, new_status):
        from django.contrib import messages
        from django.shortcuts import redirect
        from django.urls import reverse
        from apps.payment.services.manual_transaction_service import ManualTransactionService

        service = ManualTransactionService()
        transaction, message = service.update_transaction_status(
            transaction_id=object_id,
            new_status=new_status
        )

        status_display = dict(StatusChoicesPayment.choices()).get(new_status, new_status)

        if transaction:
            messages.success(request, f"Transaction status changed to {status_display}. {message}")
        else:
            messages.error(request, f"Failed to update transaction. {message}")

        return redirect(reverse('admin:payment_manualtransaction_changelist'))

    def actions_buttons(self, obj):
        from django.urls import reverse
        from django.utils.safestring import mark_safe

        # Don't show any actions if there are no status changes available
        available_statuses = [status for status in StatusChoicesPayment if status.value != obj.status]
        if not available_statuses:
            return ""

        # Create a form with a dropdown and a submit button
        form_html = f'''
        <form method="GET" style="display: flex; align-items: center; gap: 5px;">
            <select name="status_action" style="padding: 4px 6px; border: 1px solid #ccc; border-radius: 4px; background-color: #fff;">
                <option value="">-- Change Status --</option>
        '''

        # Add options for each status
        for status in available_statuses:
            status_display = dict(StatusChoicesPayment.choices()).get(status.value, status.value)
            url = reverse(f'admin:payment_manualtransaction_change_status_{status.value}', args=[obj.pk])
            form_html += f'<option value="{url}">{status_display}</option>'

        form_html += '''
            </select>
            <button type="button" onclick="changeStatus(this.form)"
                    style="background-color: #79aec8; color: white; padding: 4px 10px;
                           border: none; border-radius: 4px; cursor: pointer;">Go</button>
        </form>

        <script>
            function changeStatus(form) {
                var url = form.status_action.value;
                if (url) {
                    window.location.href = url;
                }
            }
        </script>
        '''

        return mark_safe(form_html)
    actions_buttons.short_description = 'Actions'

    def get_status(self, obj):
        from django.utils.safestring import mark_safe

        color_map = {
            StatusChoicesPayment.PENDING.value: 'orange',
            StatusChoicesPayment.PROCESSING.value: 'blue',
            StatusChoicesPayment.COMPLETED.value: 'green',
            StatusChoicesPayment.FAILED.value: 'red',
            StatusChoicesPayment.REFUNDED.value: 'purple',
            StatusChoicesPayment.CANCELLED.value: 'gray',
        }
        color = color_map.get(obj.status, 'black')
        return mark_safe(
            f'<span style="color: {color};">{obj.get_status_display()}</span>'
        )
    get_status.short_description = 'Status'
    get_status.admin_order_field = 'status'

@admin.register(SubscriptionRequest)
class SubscriptionRequestAdmin(BaseAdmin):
    list_display = ['id', 'user_email', 'type', 'status', 'submitted_at', 'processed_at']
    list_filter = ['type', 'status', 'submitted_at', 'processed_at']
    search_fields = ['user__email', 'message', 'admin_note']
    readonly_fields = ['user', 'type', 'message', 'submitted_at']
    actions = ['approve_requests', 'reject_requests']
    
    def user_email(self, obj):
        return obj.user.email
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('user')
    
    def approve_requests(self, request, queryset):
        service = SubscriptionRequestService()
        count = 0
        
        for req in queryset.filter(status='pending'):
            try:
                service.process_request(req.id, 'approved', 'Approved by admin')
                count += 1
            except Exception as e:
                self.message_user(request, f"Error processing request {req.id}: {str(e)}")
        
        self.message_user(request, f"Successfully approved {count} requests")
    approve_requests.short_description = _("Approve selected requests")
    
    def reject_requests(self, request, queryset):
        service = SubscriptionRequestService()
        count = 0
        
        for req in queryset.filter(status='pending'):
            try:
                service.process_request(req.id, 'rejected', 'Rejected by admin')
                count += 1
            except Exception as e:
                self.message_user(request, f"Error processing request {req.id}: {str(e)}")
        
        self.message_user(request, f"Successfully rejected {count} requests")
    reject_requests.short_description = _("Reject selected requests")
