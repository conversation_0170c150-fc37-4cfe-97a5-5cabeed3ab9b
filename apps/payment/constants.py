from django.utils.translation import gettext_lazy as _


INTERVAL_CHOICE = [
    ("monthly", _("Monthly")), 
    ("yearly", _("Yearly"))
]

SUBSCRIPTION_STATUS = [
    ('active', _('Active')),
    ('canceled', _('Canceled')),
    ('past_due', _('Past Due')),
    ('incomplete', _('Incomplete')),
    ('incomplete_expired', _('Incomplete Expired')),
]

REQUEST_TYPE_CHOICES = [
    ('downgrade', _('Downgrade')),
    ('cancel', _('Cancel')),
]

REQUEST_STATUS_CHOICES = [
    ('pending', _('Pending')),
    ('approved', _('Approved')),
    ('rejected', _('Rejected')),
]