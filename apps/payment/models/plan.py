from django.db import models
from django.utils.translation import gettext_lazy as _

from apps.payment.constants import INTERVAL_CHOICE
from common.models import BaseModel


class Feature(BaseModel):
    name = models.CharField(
        max_length=255, 
        verbose_name=_("Feature Name")
    )
    description = models.TextField(
        blank=True, 
        null=True, 
        verbose_name=_("Feature Description")
    )

    def __str__(self):
        return self.name

    class Meta:
        db_table = "payment_features"
        ordering = ["-id"]
        verbose_name = _("Feature")
        verbose_name_plural = _("Features")


class Plan(BaseModel):
    name = models.CharField(
        max_length=255, 
        verbose_name=_("Plan Name"), 
        unique=True
    )
    code = models.CharField(
        max_length=25, 
        verbose_name=_("Plan Code"), 
        unique=True
    )
    price = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        verbose_name=_("Price")
    )
    currency = models.CharField(
        max_length=3, 
        default="BDT", 
        verbose_name=_("Currency")
    )
    interval = models.CharField(
        max_length=20, 
        choices=INTERVAL_CHOICE, 
        default=INTERVAL_CHOICE[0][0],
        verbose_name=_("Billing Interval")
    )
    stripe_price_id = models.CharField(
        max_length=255, 
        unique=True, 
        blank=True, 
        null=True, 
        verbose_name=_("Stripe Price ID")
    ) 
    stripe_product_id = models.CharField(
        max_length=255, 
        blank=True, 
        null=True, 
        verbose_name=_("Stripe Product ID")
    ) 
    daily_message_limit = models.IntegerField(
        default=0, 
        verbose_name=_("Message Limit")
    )
    daily_file_upload_limit = models.IntegerField(
        default=0, 
        verbose_name=_("File Upload Limit")
    )
    daily_image_generation_limit = models.IntegerField(
        default=0, 
        verbose_name=_("Image Generation Limit")
    )
    tokens_included = models.IntegerField(
        default=0, 
        verbose_name=_("Token Included")
    ) 
    trial_period_days = models.IntegerField(
        default=0, 
        verbose_name=_("Trial Period (Days)")
    ) 
    features = models.ManyToManyField(
        Feature,
        related_name="plans",
        verbose_name=_("Features"),
        blank=True,
    )

    def __str__(self):
        return f"{self.name} ({self.interval})"
    
    class Meta:
        db_table = "payment_plans"
        ordering = ["-created_at"]
        verbose_name = _("Plan")
        verbose_name_plural = _("Plans")
