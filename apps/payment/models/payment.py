from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _


from common.constants import StatusChoicesPayment, PaymentMethodType
from common.models import BaseModel
from apps.payment.models.stripe_customer import StripeCustomer

User = get_user_model()

class PaymentMethod(BaseModel):
    stripe_customer = models.ForeignKey(
        StripeCustomer,
        on_delete=models.SET_NULL,
        related_name="payment_methods",
        null=True,
        blank=True,
        verbose_name=_("Stripe Customer"),
    )
    type = models.CharField(
        max_length=20,
        choices=PaymentMethodType.choices(),
        db_index=True,
        verbose_name=_("Type"),
    )
    provider = models.CharField(max_length=50, verbose_name=_("Provider"))
    token_id = models.CharField(max_length=255, verbose_name=_("Token ID"))
    last_digits = models.CharField(
        max_length=4, null=True, blank=True, verbose_name=_("Last 4 digits")
    )
    brand = models.CharField(max_length=50, null=True, blank=True, verbose_name=_("Brand"))
    exp_month = models.IntegerField(null=True, blank=True, verbose_name=_("Expiry Month"))
    exp_year = models.IntegerField(null=True, blank=True, verbose_name=_("Expiry Year"))

    is_default = models.BooleanField(default=False, verbose_name=_("Default"))
    metadata = models.JSONField(default=dict, verbose_name=_("Meta Data"))
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("Company"),
    )

    class Meta:
        db_table = "payment_methods"
        ordering = ["-created_at"]
        verbose_name = _("Payment method")
        verbose_name_plural = _("Payment methods")

    def __str__(self):
        return f"{self.stripe_customer.user.email if self.stripe_customer else 'No User'} - {self.brand} ****{self.last_digits}"
    

class Payment(BaseModel):
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("User"),
    )
    payment_method = models.ForeignKey(
        PaymentMethod,
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("Payment Method"),
    )
    amount = models.DecimalField(
        max_digits=10, decimal_places=2, verbose_name=_("Amount")
    )
    currency = models.CharField(
        max_length=3, default="USD", db_index=True, verbose_name=_("Currency")
    )
    status = models.CharField(
        max_length=20,
        choices=StatusChoicesPayment.choices(),
        default=StatusChoicesPayment.PENDING.value,
        db_index=True,
        verbose_name=_("Status"),
    )
    transaction_id = models.CharField(
        max_length=255,
        unique=True,
        db_index=True,
        verbose_name=_("Transaction ID"),
    )
    description = models.TextField(
        null=True, blank=True, verbose_name=_("Description")
    )
    provider_response = models.JSONField(
        default=dict, verbose_name=_("Provider Response")
    )
    error_message = models.TextField(
        null=True, blank=True, verbose_name=_("Error Message")
    )
    metadata = models.JSONField(default=dict, verbose_name=_("Meta Data"))
    refunded_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_("Refunded Amount"),
    )
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("Company"),
    )

    class Meta:
        db_table = "payment_payments"
        ordering = ["-created_at"]
        verbose_name = _("Payment")
        verbose_name_plural = _("Payments")


class PaymentLog(BaseModel):
    payment = models.ForeignKey(
        Payment,
        on_delete=models.SET_NULL,
        null=True,
        related_name="logs",
        verbose_name=_("Payment"),
    )
    action = models.CharField(max_length=50, verbose_name=_("Action"))
    status = models.CharField(
        max_length=20, db_index=True, verbose_name=_("Status")
    )
    message = models.TextField(verbose_name=_("Message"))
    metadata = models.JSONField(default=dict, verbose_name=_("Metadata"))
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("Company"),
    )

    class Meta:
        db_table = "payment_logs"
        ordering = ["-created_at"]
        verbose_name = _("Payment log")
        verbose_name_plural = _("Payment logs")


class PaymentMethodDetails(BaseModel):
    name = models.CharField(
        max_length=255, 
        unique=True,
        verbose_name=_("Name")
    )
    description = models.TextField(
        blank=True, 
        null=True, 
        verbose_name=_("Description")
    )
    logo = models.ImageField(
        upload_to="payment_method_logos/",
        verbose_name=_("Logo"),
        null=True,
        blank=True,
    )
    pay_to = models.CharField(
        max_length=255, 
        verbose_name=_("Pay To"),
        help_text=_("The account number / mobile number / card number to which payments should be made")
    )
    account_type = models.CharField(
        max_length=255, 
        verbose_name=_("Account Type"),
        help_text=_("The type of account (e.g., savings, checking, personal, agent etc.)")
    )
    type = models.CharField(
        max_length=20,
        choices=PaymentMethodType.choices(),
        db_index=True,
        verbose_name=_("Type"),
    )

    class Meta:
        db_table = "payment_method_details"
        verbose_name = _("Payment Method Details")
        verbose_name_plural = _("Payment Method Details")

    def __str__(self):
        return self.name