from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from common.models import BaseModel

User = get_user_model()


class StripeCustomer(BaseModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="stripe_customer", verbose_name=_("User"),)
    customer_id = models.CharField(max_length=255, unique=True, verbose_name=_("Customer ID"))

    def __str__(self):
        return f"{self.user.email} - {self.customer_id}"
    
    class Meta:
        db_table = "stripe_customers"
        ordering = ["-created_at"]
        verbose_name = _("Stripe Customer")
        verbose_name_plural = _("Stripe Customers")

class StripeWebhookLog(BaseModel):
    customer_id = models.CharField(max_length=255, unique=True, verbose_name=_("Stripe Customer ID"))
    event_id = models.Char<PERSON>ield(max_length=255, unique=True, verbose_name=_("Stripe Event ID"))
    event_type = models.Char<PERSON>ield(max_length=255, verbose_name=_("Stripe Event Type"))
    event_data = models.JSONField(verbose_name=_("Stripe Event Data"))

    def __str__(self):
        return f"{self.customer_id} - {self.event_type}"
    
    class Meta:
        db_table = "stripe_webhook_logs"
        ordering = ["-created_at"]
        verbose_name = _("Stripe Webhook Log")
        verbose_name_plural = _("Stripe Webhook Logs")
