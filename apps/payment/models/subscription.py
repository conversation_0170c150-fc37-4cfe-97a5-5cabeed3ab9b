from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from apps.payment.constants import SUBSCRIPTION_STATUS
from apps.payment.models.plan import Plan
from common.models import BaseModel

User = get_user_model()


class Subscription(BaseModel):
    """Stores user subscriptions"""
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name="subscriptions", 
        verbose_name=_("User")
    )
    plan = models.ForeignKey(
        Plan, 
        on_delete=models.PROTECT, 
        related_name="subscriptions", 
        verbose_name=_("Plan")
    )
    stripe_subscription_id = models.CharField(
        null=True,
        blank=True,                                    
        max_length=255,  
        unique=True,  
        verbose_name=_("Stripe Subscription ID")
    )
    status = models.CharField( 
        max_length=50,  
        choices=SUBSCRIPTION_STATUS,  
        verbose_name=_("Subscription Status")
    )
    daily_message_limit = models.PositiveIntegerField( 
        default=0,  
        verbose_name=_("Daily Message Limit")
    )
    daily_file_upload_limit = models.IntegerField(
        default=0, 
        verbose_name=_("File Upload Limit")
    )
    daily_image_generation_limit = models.IntegerField(
        default=0, 
        verbose_name=_("Image Generation Limit")
    )
    tokens_balance = models.PositiveIntegerField( 
        default=0,  
        verbose_name=_("Tokens Balance")
    )
    trial_end = models.DateTimeField( 
        null=True,  
        blank=True,  
        verbose_name=_("Trial End Date")
    )
    current_period_end = models.DateTimeField( 
        null=True,  
        blank=True,  
        verbose_name=_("Current Period End Date")
    )
    canceled_at = models.DateTimeField( 
        null=True,  
        blank=True,  
        verbose_name=_("Cancellation Date")
    )

    def __str__(self):
        return f"{self.user.email} - {self.plan.name} ({self.status})"

    class Meta:
        db_table = "payment_subscriptions"
        ordering = ["-created_at"]
        verbose_name = _("Subscription")
        verbose_name_plural = _("Subscriptions")
