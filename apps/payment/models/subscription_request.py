from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model

from apps.payment.constants import REQUEST_TYPE_CHOICES, REQUEST_STATUS_CHOICES
from common.models import BaseModel

User = get_user_model()


class SubscriptionRequest(BaseModel):
    """Stores user subscription downgrade/cancellation requests"""
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name="subscription_requests", 
        verbose_name=_("User")
    )
    type = models.CharField(
        max_length=20,
        choices=REQUEST_TYPE_CHOICES,
        verbose_name=_("Request Type")
    )
    status = models.CharField(
        max_length=20,
        choices=REQUEST_STATUS_CHOICES,
        default='pending',
        verbose_name=_("Status")
    )
    message = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("User Message")
    )
    admin_note = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Admin Note")
    )
    submitted_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Submitted At")
    )
    processed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Processed At")
    )

    class Meta:
        verbose_name = _("Subscription Request")
        verbose_name_plural = _("Subscription Requests")
        ordering = ['-submitted_at']
        db_table = "payment_subscription_requests"

    def __str__(self):
        return f"{self.user.email} - {self.get_type_display()} - {self.get_status_display()}"
