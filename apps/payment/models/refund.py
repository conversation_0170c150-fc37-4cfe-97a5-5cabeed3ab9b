from django.db import models
from django.utils.translation import gettext_lazy as _

from apps.payment.models.payment import Payment
from common.constants import StatusChoicesRefund
from common.models import BaseModel


class Refund(BaseModel):
    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name="refunds",
        verbose_name=_("Payment"),
    )
    amount = models.DecimalField(
        max_digits=10, decimal_places=2, verbose_name=_("Amount")
    )
    reason = models.TextField(verbose_name=_("Reason"))
    status = models.CharField(
        max_length=20,
        choices=StatusChoicesRefund.choices(),
        default=StatusChoicesRefund.PENDING.value,
        verbose_name=_("Status"),
    )
    refund_id = models.CharField(
        max_length=255, unique=True, db_index=True, verbose_name=_("Refund ID")
    )
    provider_response = models.JSONField(
        default=dict, verbose_name=_("Provider Response")
    )
    error_message = models.TextField(
        null=True, blank=True, verbose_name=_("Error Message")
    )
    metadata = models.JSONField(default=dict, verbose_name=_("Meta Data"))
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_("Company"),
    )

    class Meta:
        db_table = "payment_refunds"
        ordering = ["-created_at"]
        verbose_name = _("Refund")
        verbose_name_plural = _("Refunds")
