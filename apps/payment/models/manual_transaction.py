from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import RegexValidator

from dxh_libraries.translation import gettext_lazy as _
from common.constants import StatusChoicesPayment, UPLOAD_PATH
from common.models import BaseModel

User = get_user_model()


class ManualTransaction(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        db_index=True,
        verbose_name=_("Company"),
    )
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("User"),
    )
    plan = models.ForeignKey(
        "payment.Plan",
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("Plan"),
    )
    payment_method = models.ForeignKey(
        "payment.PaymentMethodDetails",
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("Payment Method"),
    )
    mobile_number = models.CharField(
        max_length=20,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message=_("Mobile number must be entered in the format: '+*********'. Up to 15 digits allowed.")
            ),
        ],
        verbose_name=_("Mobile Number"),
        help_text=_("Mobile number used for payment verification"),
        null=True,
        blank=True,
    )
    payment_proof = models.ImageField(
        upload_to=UPLOAD_PATH,
        verbose_name=_("Payment Proof"),
        help_text=_("Screenshot or image of payment confirmation"),
        null=True,
        blank=True,
    )
    status = models.CharField(
        max_length=20,
        choices=StatusChoicesPayment.choices(),
        default=StatusChoicesPayment.PENDING.value,
        db_index=True,
        verbose_name=_("Status"),
    )
    transaction_id = models.CharField(
        max_length=255,
        unique=True,
        db_index=True,
        verbose_name=_("Transaction ID"),
    )
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Description")
    )
    refunded_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_("Refunded Amount"),
    )
    metadata = models.JSONField(
        default=dict,
        verbose_name=_("Meta Data")
    )

    class Meta:
        db_table = "payment_manual_transactions"
        verbose_name = _("Manual Transaction")
        verbose_name_plural = _("Manual Transactions")
        unique_together = ("user", "transaction_id")

    def __str__(self):
        return f"{self.user} - {self.transaction_id}"
