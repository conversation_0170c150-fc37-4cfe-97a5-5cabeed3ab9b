from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils.timezone import now

from apps.payment.models import Plan, Feature, Subscription

User = get_user_model()

class Command(BaseCommand):
    help = 'Creates static plans with specified features.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force-remove',
            action='store_true',
            help='Force remove old plans that are not in the new set',
        )

    def handle(self, *args, **options):
        self.force_remove = options.get('force_remove', False)
        self.create_plans()

    def should_remove_old_plans(self, existing_plan_codes, plan_codes_to_create):
        """Determine if we should remove old plans that aren't in the new set."""
        # If force_remove is True, always remove old plans
        if self.force_remove:
            return True

        # Check if there are any plans that exist but aren't in the new set
        plans_to_remove = existing_plan_codes - plan_codes_to_create
        if plans_to_remove:
            # Ask for confirmation
            self.stdout.write(self.style.WARNING(f'The following plans will be removed: {plans_to_remove}'))
            confirm = input('Do you want to continue? (y/n): ')
            return confirm.lower() == 'y'

        return False

    def create_plans(self):
        # We'll use update_or_create to handle existing plans
        # This way, plans won't be recreated if they already exist

        # Define features
        features_data = [
            "Access to GPT-4o with limited usage (10 messages/day)",
            "Basic features",
            "Increased usage limits (50 messages/day)",
            "Priority support",
            "Higher usage limits (150 messages/day)",
            "Access to advanced features",
            "Unlimited usage",
            "Premium features",
            "Dedicated support",
        ]

        # Create or get features
        features = {}
        for name in features_data:
            feature, created = Feature.objects.get_or_create(name=name)
            features[name] = feature

        # Define plans and their associated features
        plans = [
            {
                "name": "Free",
                "code": "free",
                "price": 0,
                "interval": "monthly",
                "stripe_price_id": None,
                "stripe_product_id": None,
                "tokens_included": 1000,
                "trial_period_days": 0,
                "daily_message_limit": 10,
                "daily_file_upload_limit": 0,
                "daily_image_generation_limit": 0,
                "features": [
                    "Access to GPT-4o with limited usage (10 messages/day)",
                    "Basic features",
                ],
            },
            {
                "name": "Starter",
                "code": "starter",
                "price": 100,
                "interval": "monthly",
                "stripe_price_id": None,
                "stripe_product_id": None,
                "tokens_included": 5000,
                "trial_period_days": 0,
                "daily_message_limit": 50,
                "daily_file_upload_limit": 2,
                "daily_image_generation_limit": 0,
                "features": [
                    "Increased usage limits (50 messages/day)",
                    "Priority support",
                ],
            },
            {
                "name": "Regular",
                "code": "regular",
                "price": 200,
                "interval": "monthly",
                "stripe_price_id": None,
                "stripe_product_id": None,
                "tokens_included": 15000,
                "trial_period_days": 0,
                "daily_message_limit": 150,
                "daily_file_upload_limit": 2,
                "daily_image_generation_limit": 1,
                "features": [
                    "Higher usage limits (150 messages/day)",
                    "Access to advanced features",
                ],
            },
            {
                "name": "Pro",
                "code": "pro",
                "price": 500,
                "interval": "monthly",
                "stripe_price_id": None,
                "stripe_product_id": None,
                "tokens_included": 50000,
                "trial_period_days": 0,
                "daily_message_limit": 2147483647,
                "daily_file_upload_limit": 2,
                "daily_image_generation_limit": 2,
                "features": [
                    "Unlimited usage",
                    "Premium features",
                    "Dedicated support",
                ],
            },
        ]

        # Check if we need to create or update plans
        existing_plan_codes = set(Plan.objects.values_list('code', flat=True))
        plan_codes_to_create = {plan_data['code'] for plan_data in plans}

        # Check if we need to remove any old plans that aren't in the new set
        if self.should_remove_old_plans(existing_plan_codes, plan_codes_to_create):
            # Remove plans that aren't in the new set
            Plan.objects.exclude(code__in=plan_codes_to_create).delete()
            self.stdout.write(self.style.WARNING('Removed old plans that are no longer needed'))

        # Create or update plans and associate features
        for plan_data in plans:
            plan, created = Plan.objects.update_or_create(
                code=plan_data["code"],  # Use code as the unique identifier
                defaults={
                    "name": plan_data["name"],
                    "price": plan_data["price"],
                    "interval": plan_data["interval"],
                    "stripe_price_id": plan_data["stripe_price_id"],
                    "stripe_product_id": plan_data["stripe_product_id"],
                    "tokens_included": plan_data["tokens_included"],
                    "trial_period_days": plan_data["trial_period_days"],
                    "daily_message_limit": plan_data["daily_message_limit"],
                    "daily_file_upload_limit": plan_data["daily_file_upload_limit"],
                    "daily_image_generation_limit": plan_data["daily_image_generation_limit"],
                }
            )

            # Clear existing features to avoid duplicates
            plan.features.clear()

            # Add features to the plan
            for feature_name in plan_data["features"]:
                feature = features[feature_name]
                plan.features.add(feature)

            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created plan "{plan.name}"'))
            else:
                self.stdout.write(self.style.WARNING(f'Successfully updated plan "{plan.name}"'))