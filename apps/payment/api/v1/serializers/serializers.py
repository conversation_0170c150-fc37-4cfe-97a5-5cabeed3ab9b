from dxh_libraries.rest_framework import serializers
from dxh_common.base.base_serializer import BaseModelSerializer

from apps.payment.models import (
    PaymentMethod, Payment, Refund, PaymentLog,
    Subscription, Plan, Feature
)


class PaymentMethodSerializer(BaseModelSerializer):
    class Meta:
        model = PaymentMethod
        fields = (
            'id', 'type', 'provider', 'token_id', 'last_digits', 'expiry_date',
            'is_default', 'metadata', 'created_at', 'updated_at'
        )
        read_only_fields = ('id', 'created_at', 'updated_at')


class PaymentSerializer(BaseModelSerializer):
    class Meta:
        model = Payment
        fields = (
            'id', 'user', 'payment_method', 'amount', 'currency', 'status',
            'transaction_id', 'description', 'provider_response',
            'error_message', 'metadata', 'refunded_amount',
            'created_at', 'updated_at'
        )
        read_only_fields = (
            'id', 'status', 'transaction_id',
            'created_at', 'updated_at'
        )


class RefundSerializer(BaseModelSerializer):
    class Meta:
        model = Refund
        fields = ('id', 'payment', 'amount', 'reason', 'status', 'refund_id')
        read_only_fields = ('id', 'status', 'refund_id')


class PaymentLogSerializer(BaseModelSerializer):
    class Meta:
        model = PaymentLog
        fields = (
            'id', 'payment', 'action', 'status',
            'message', 'metadata', 'created_at'
        )
        read_only_fields = ('id', 'created_at')


class SubscriptionSerializer(BaseModelSerializer):
    class Meta:
        model = Subscription
        fields = '__all__'


class PlanSerializer(BaseModelSerializer):
    features = serializers.StringRelatedField(many=True)
    is_user_plan = serializers.SerializerMethodField()

    class Meta:
        model = Plan
        fields = '__all__'

    def get_is_user_plan(self, obj):
        request = self.context.get("request")
        user = request.user
        if request and user.is_authenticated:
            has_subscription = user.subscriptions.filter(status="active").exists()
            
            if not has_subscription:
                return obj.code == "free"
            
            return user.subscriptions.filter(plan=obj, status="active").exists()
        
        return False


class FeatureSerializer(BaseModelSerializer):
    class Meta:
        model = Feature
        fields = '__all__'
