import re
from django.contrib.auth import get_user_model
from django.conf import settings
from dxh_libraries.rest_framework import serializers
from dxh_common.base.base_serializer import BaseModelSerializer
from django.utils.translation import gettext_lazy as _

from apps.payment.models import ManualTransaction

User = get_user_model()


class ManualTransactionListSerializer(BaseModelSerializer):
    plan_name = serializers.SerializerMethodField()
    user_email = serializers.SerializerMethodField()
    payment_proof_url = serializers.SerializerMethodField()

    class Meta:
        model = ManualTransaction
        fields = [
            'id', 'transaction_id', 'status', 'payment_method', 'mobile_number',
            'description', 'plan', 'plan_name', 'user', 'user_email',
            'payment_proof_url', 'created_at', 'updated_at'
        ]
        read_only_fields = fields

    def get_plan_name(self, obj):
        return obj.plan.name if obj.plan else None

    def get_user_email(self, obj):
        return obj.user.email if obj.user else None

    def get_payment_proof_url(self, obj):
        if obj.payment_proof and hasattr(obj.payment_proof, 'url'):
            request = self.context.get('request')
            return f"{settings.MEDIA_BASE_URL}{obj.payment_proof.url}" if obj.payment_proof else None
        
        return None


class ManualTransactionSerializer(BaseModelSerializer):
    payment_proof = serializers.ImageField(required=False, allow_null=True)
    transaction_id = serializers.CharField(
        error_messages={
            "required": _("Transaction ID is required."),
            "blank": _("Transaction ID cannot be blank."),
            "invalid": _("Invalid Transaction ID format."),
        }
    )

    class Meta:
        model = ManualTransaction
        fields = "__all__"
        read_only_fields = [
            'id', 'status', 'refunded_amount', 'created_at', 'updated_at'
        ]

    def validate_mobile_number(self, value):
        if value and not re.match(r'^\+?1?\d{9,15}$', value):
            raise serializers.ValidationError(
                "Mobile number must be entered in the format: '+999999999'. Up to 15 digits allowed."
            )
        return value

    def validate_payment_proof(self, value):
        if value:
            max_size = getattr(settings, 'MAX_UPLOAD_SIZE', 5 * 1024 * 1024)
            if value.size > max_size:
                raise serializers.ValidationError(
                    f"Image size cannot exceed {max_size/1024/1024}MB"
                )

            allowed_formats = getattr(settings, 'ALLOWED_IMAGE_FORMATS',
                                    ['image/jpeg', 'image/png', 'image/jpg'])
            if value.content_type not in allowed_formats:
                raise serializers.ValidationError(
                    f"Unsupported file format. Allowed formats: {', '.join(allowed_formats)}"
                )
        return value

    def validate_transaction_id(self, value):
        if ManualTransaction.objects.filter(transaction_id=value).exists():
            raise serializers.ValidationError(_("This Transaction ID is already used. Contact support if this is an error."))
        return value

    def validate(self, data):
        if 'plan' not in data or data['plan'] is None:
            raise serializers.ValidationError("Plan is required.")

        if ('mobile_number' not in data or not data['mobile_number']):
            raise serializers.ValidationError({
                "mobile_number": f"Mobile number is required for {data['payment_method']} payments."
            })

        return data
