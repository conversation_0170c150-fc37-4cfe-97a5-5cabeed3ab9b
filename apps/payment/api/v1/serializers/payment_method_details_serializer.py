from django.conf import settings
from dxh_common.base.base_serializer import BaseModelSerializer
from dxh_libraries.rest_framework import serializers
from apps.payment.models import PaymentMethodDetails


class PaymentMethodDetailsSerializer(BaseModelSerializer):
    logo_url = serializers.SerializerMethodField()

    class Meta:
        model = PaymentMethodDetails
        fields = (
            'id', 'name', 'description', 'logo_url', 'pay_to', 'account_type', 'type',
            'created_at', 'updated_at'
        )
        read_only_fields = fields
    
    def get_logo_url(self, obj):
        if obj.logo:
            return f"{settings.MEDIA_BASE_URL}{obj.logo.url}"
        return None
