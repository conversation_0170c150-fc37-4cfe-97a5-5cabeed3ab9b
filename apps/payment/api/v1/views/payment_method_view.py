from dxh_libraries.rest_framework import status, Response
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.logger import Logger

from apps.payment.services import PaymentService

logger = Logger(__name__)


class PaymentMethodView(BaseApiView):
    def __init__(self):
        self.payment_service = PaymentService()
        
    def get(self, request):
        methods, error = self.payment_service.get_payment_methods(request.user)
        if error:
            result = {
                "message": _("Invalid payment method or customer not found."),
                "errors": error
            }

            return Response(result, status=status.HTTP_400_BAD_REQUEST)
        
        result = {
            "message": _("Payment methods retrieved successfully"),
            "data": methods
        }

        return Response(result, status=status.HTTP_200_OK)

    def post(self, request):
        try:
            payment_token = request.data.get('token')
            if not payment_token:
                return Response(
                    {"message": _("Payment token required")},
                    status=status.HTTP_400_BAD_REQUEST
                )

            customer_id, error = self.payment_service.create_payment_method(
                request.user,
                payment_token
            )
            if error:
                return Response({"message": error}, status=status.HTTP_400_BAD_REQUEST)

            return Response({ 
                "message": _("Payment method added successfully"),
                "data": {'customer_id': customer_id}
            })
        
        except Exception as e:
            logger.error(f"Payment method error: {str(e)}")
            return Response({"message": f"Error adding payment method: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request):
        """Set a specific payment method as default."""
        try:
            payment_method_id = request.data.get('payment_method_id')
            if not payment_method_id:
                return Response(
                    {"message": _("Payment method ID required")},
                    status=status.HTTP_400_BAD_REQUEST
                )

            success, error = self.payment_service.set_default_payment_method(
                request.user,
                payment_method_id
            )
            if error:
                return Response({"message": error}, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                "message": _("Default payment method updated successfully"),
                "data": {"payment_method_id": payment_method_id}
            })

        except Exception as e:
            logger.error(f"Error setting default payment method: {str(e)}")
            return Response({"message": f"Error updating default payment method: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)