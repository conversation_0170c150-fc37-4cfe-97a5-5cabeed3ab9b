from dxh_libraries.rest_framework import status, Response
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.logger import Logger

from apps.payment.services import PaymentService, PlanService, SubscriptionService
from apps.payment.api.v1.serializers import PlanSerializer

logger = Logger(__name__)


class SubscriptionView(BaseApiView):
    def __init__(self):
        self.payment_service = PaymentService()
        self.plan_service = PlanService()
        self.subscription_serice = SubscriptionService()
    
    def get(self, request, id=None):
        try:
            if id:
                plan = self.plan_service.get(id=id)
                result = {
                    "message": _("Subscription Plan retrieved successfully"),
                    "data": PlanSerializer(plan, context={"request": request}).data
                }
                return Response(result, status=status.HTTP_200_OK)
            
            plans = self.plan_service.get_all() 
            result = {
                "message": _("Subscription Plans retrieved successfully"),
                "data": PlanSerializer(plans, many=True, context={"request": request}).data
            }

            return Response(result, status=status.HTTP_200_OK)
        
        except Exception as e:
            logger.error(f"Plans retrieval error: {str(e)}")
            return Response(
                {"message": _("Error retrieving Plans"), "errors": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def post(self, request):
        try:
            price_id = request.data.get("price_id")
            if not price_id:
                return Response(
                    {"message": _("Price ID is required")},
                    status=status.HTTP_400_BAD_REQUEST
                )

            subscription_id, error = self.payment_service.create_subscription(request.user, price_id)
            if error:
                return Response(
                    {"message": _("Error creating subscription"), "errors": error},
                    status=status.HTTP_400_BAD_REQUEST
                )

            return Response(
                {"message": _("Subscription created successfully"), "data": {"subscription_id": subscription_id}},
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            logger.error(f"Subscription creation error: {str(e)}")
            return Response(
                {"message": _("Error creating subscription"), "errors": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    def delete(self, request):
        try:
            subscription_id = request.query_params.get("subscription_id", None)
            if not subscription_id:
                return Response(
                    {"message": _("Subscription ID is required")},
                    status=status.HTTP_400_BAD_REQUEST
                )
            subscription = self.subscription_serice.get(id=subscription_id)
            success, error = self.payment_service.cancel_subscription(subscription.stripe_subscription_id)

            if error:
                return Response(
                    {"message": _("Error canceling subscription"), "errors": error},
                    status=status.HTTP_400_BAD_REQUEST
                )

            return Response(
                {"message": _("Your subscription will end after this currrent period.")},
                status=status.HTTP_200_OK
            )

        except Exception as e:
            logger.error(f"Subscription cancellation error: {str(e)}")
            return Response(
                {"message": _("Error canceling subscription"), "errors": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )