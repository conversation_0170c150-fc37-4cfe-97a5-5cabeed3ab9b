from dxh_libraries.rest_framework import status, Response
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.logger import Logger

from apps.payment.services import PaymentService

logger = Logger(__name__)


class ProcessPaymentView(BaseApiView):
    def post(self, request):
        amount = request.data.get('amount')
        currency = request.data.get('currency')
        payment_method_id = request.data.get('payment_method_id')

        payment_intent_id, error = PaymentService.process_payment(
            amount,
            currency,
            payment_method_id
        )

        if error:
            return Response(message=error)

        return Response(
            message="Payment processed successfully",
            data={'payment_intent_id': payment_intent_id}
        )
