from dxh_libraries.rest_framework import status, Response, MultiPart<PERSON>arser, FormParser
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.logger import Logger

from apps.payment.services import ManualTransactionService
from apps.cross_cutting import EmailService
from apps.payment.api.v1.serializers import ManualTransactionSerializer, ManualTransactionListSerializer

logger = Logger(__name__)


class ManualTransactionView(BaseApiView):
    parser_classes = [MultiPartParser, FormParser]

    def __init__(self):
        self.service = ManualTransactionService()
        self.email_service = EmailService()

    def get(self, request, id=None):
        try:
            if id:
                transaction = self.service.get(id=id, user=request.user)
                if not transaction:
                    result = {
                        "message": _("Transaction not found or unauthorized"),
                        "errors": {"transaction_id": f"No transaction found with ID {id}."}
                    }
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)

                serializer = ManualTransactionSerializer(transaction, context={'request': request})
                result = {
                    "message": _("Transaction retrieved successfully"),
                    "data": serializer.data
                }
                return Response(result, status=status.HTTP_200_OK)

            transactions = self.service.list(user=request.user).order_by("-created_at")
            serializer = ManualTransactionListSerializer(transactions, many=True, context={'request': request})
            result = {
                "message": _("Transactions retrieved successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ManualTransactionView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def post(self, request):
        # Create a mutable copy of the request data
        payload = request.data.copy()
        payload['user'] = request.user.id

        serializer = ManualTransactionSerializer(data=payload, context={'request': request})
        if not serializer.is_valid():
            first_error_message = serializer.errors[next(iter(serializer.errors))][0]
            result = {
                "message": first_error_message,
                "errors": serializer.errors
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        try:
            transaction_data = serializer.validated_data
            transaction = self.service.create(**transaction_data)

            # Create a dictionary with only the needed transaction attributes
            transaction_context = {
                "id": transaction.id,
                "transaction_id": transaction.transaction_id,
                "status": transaction.status,
                "created_at": transaction.created_at,
                "payment_method": {
                    "name": str(transaction.payment_method)
                },
                "plan": {
                    "name": transaction.plan.name if transaction.plan else None,
                    "price": str(transaction.amount) if hasattr(transaction, 'amount') else str(transaction.plan.price) if transaction.plan else "",
                    "currency": transaction.plan.currency if transaction.plan else ""
                },
                "user": {
                    "email": transaction.user.email,
                    "name": transaction.user.get_full_name() or transaction.user.email.split('@')[0].capitalize()
                }
            }

            # Log before sending email
            logger.info(f"Attempting to send payment pending approval email to {transaction.user.email}")
            
            try:
                # Use the correct template name - "payment_pending_approval" without "email/" prefix
                self.email_service.send_email(
                    to_email=transaction.user.email,
                    subject="Your PIOO Payment is Complete - Approval Pending",
                    template_name="payment_pending_approval",
                    context={"transaction": transaction_context}
                )
                logger.info(f"Successfully queued payment pending approval email to {transaction.user.email}")
            except Exception as email_error:
                logger.error(f"Failed to send payment pending approval email: {str(email_error)}")
                # Continue execution even if email fails

            serializer = ManualTransactionSerializer(transaction, context={'request': request})
            result = {
                "message": _("Your Transaction is listed successfully. Please wait for the admin to process it."),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "ManualTransactionView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e
