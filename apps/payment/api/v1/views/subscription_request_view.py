from rest_framework import status
from dxh_common.base.base_api_view import BaseApiView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils.translation import gettext_lazy as _
from dxh_common.logger import Logger

from apps.payment.api.v1.serializers import (
    SubscriptionRequestSerializer,
    CreateSubscriptionRequestSerializer
)
from apps.payment.services import SubscriptionRequestService

logger = Logger(__name__)


class SubscriptionRequestView(BaseApiView):
    permission_classes = [IsAuthenticated]
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.service = SubscriptionRequestService()
    
    def post(self, request):
        """Create a new subscription request"""
        try:
            serializer = CreateSubscriptionRequestSerializer(data=request.data)
            if not serializer.is_valid():
                result = {"message": _("Invalid request data"), "errors": serializer.errors}
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            request_type = serializer.validated_data['type']
            message = serializer.validated_data.get('message', '')
            
            subscription_request, error = self.service.create_request(
                user=request.user,
                request_type=request_type,
                message=message
            )
            
            if error:
                result = {"message": _(error)}
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            response_serializer = SubscriptionRequestSerializer(subscription_request)
            
            result = {
                "message": _("Your request has been submitted and is pending review"),
                "data": response_serializer.data
            }
            
            return Response(result, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error({"event": "SubscriptionRequestView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e
    
    def get(self, request):
        """Get user's subscription requests"""
        try:
            # Fix: Use repository.filter instead of service.filter
            requests = self.service.repository.filter(user=request.user)
            serializer = SubscriptionRequestSerializer(requests, many=True)
            
            result = {
                "message": _("Subscription requests retrieved successfully"),
                "data": serializer.data
            }
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error({"event": "SubscriptionRequestView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e
