from dxh_libraries.rest_framework import status, Response
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.logger import Logger

from apps.payment.services import PaymentMethodDetailsService
from apps.payment.api.v1.serializers import PaymentMethodDetailsSerializer

logger = Logger(__name__)


class PaymentMethodDetailsView(BaseApiView):
    def __init__(self):
        self.service = PaymentMethodDetailsService()

    def get(self, request):
        try:
            payment_method_details = self.service.get_all().order_by("-created_at")
            serializer = PaymentMethodDetailsSerializer(payment_method_details, many=True)
            result = {
                "message": _("Payment method details retrieved successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "PaymentMethodDetailsView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e
