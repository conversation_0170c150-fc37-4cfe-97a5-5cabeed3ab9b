import stripe
from django.conf import settings
from dxh_libraries.rest_framework import status, Response
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_api_view import PublicApiView
from dxh_common.logger import Logger

from apps.payment.services.payment_service import PaymentService
from apps.payment.services import StripeWebhookService

logger = Logger(__name__)


class StripeWebhookView(PublicApiView):
    def __init__(self):
        self.endpoint_secret = settings.STRIPE_WEBHOOK_SECRET
        self.webhook_service = StripeWebhookService()

    def post(self, request, *args, **kwargs):
        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
        event = None

        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, self.endpoint_secret
            )
        except ValueError as e:
            logger.error(f"Invalid payload: {e}")
            return Response({"errors": "Invalid payload"}, status=status.HTTP_400_BAD_REQUEST)
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Invalid signature: {e}")
            return Response({"errors": "Invalid signature"}, status=status.HTTP_400_BAD_REQUEST)

        self.webhook_service.handle_subscription_webhook(event)

        return Response(
            {"message": "Webhook received successfully"}, 
            status=status.HTTP_200_OK
        )