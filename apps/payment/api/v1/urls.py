from django.urls import path
from apps.payment.api.v1 import views


urlpatterns = [
    # path('payments/methods/', views.PaymentMethodView.as_view(), name='payment-methods'),
    # path('payments/process/', views.ProcessPaymentView.as_view(), name='payment-process'),
    path('payments/refund/', views.RefundPaymentView.as_view(), name='payment-refund'),
    path('payments/history/', views.PaymentHistoryView.as_view(), name='payment-history'),
    path('subscription/', views.SubscriptionView.as_view(), name='subscription-list'),
    path('subscription/<int:id>/', views.SubscriptionView.as_view(), name='subscription-details'),
    # path('stripe/webhook/', views.StripeWebhookView.as_view(), name='stripe-webhook'),

    # Manual Transaction endpoints
    path('transactions/', views.ManualTransactionView.as_view(), name='manual-transaction-list'),
    path('transactions/<int:id>/', views.ManualTransactionView.as_view(), name='manual-transaction-detail'),

    path('payments/methods/details/', views.PaymentMethodDetailsView.as_view(), name='payment-method-details'),

    # Subscription Request endpoints
    path('subscription/downgrade/', views.SubscriptionRequestView.as_view(), name='subscription-downgrade'),
]
