from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from dxh_common.base.base_service import BaseService, ServiceError
from dxh_common.logger import Logger

from apps.cross_cutting import EmailService
from apps.payment.repositories import ManualTransactionRepository
from apps.payment.services.subscription_service import SubscriptionService
from apps.payment.tasks import change_user_to_free_plan
from common.constants import StatusChoicesPayment

logger = Logger(__name__)


class ManualTransactionService(BaseService):
    def __init__(self):
        super().__init__(ManualTransactionRepository())
        self.subscription_service = SubscriptionService()
        self.email_service = EmailService()

    def update_transaction_status(self, transaction_id, new_status):
        try:
            # Get the transaction
            transaction = self.get(id=transaction_id)
            if not transaction:
                return None, "Transaction not found"

            # If status is already the same, no need to update
            if transaction.status == new_status:
                return transaction, "Transaction status is already set to this value"

            # Update the transaction status
            transaction = self.update(transaction, status=new_status)

            # If the transaction is marked as completed, create a subscription
            if new_status == StatusChoicesPayment.COMPLETED.value:
                return self._handle_completed_transaction(transaction)
            else:
                subscription = self.subscription_service.get(plan=transaction.plan, user=transaction.user)
                subscription.status = 'canceled'
                subscription.save(update_fields=['status'])
                user = transaction.user
                user.subscription_type = 'free'
                user.save(update_fields=['subscription_type'])

            return transaction, "Transaction status updated successfully"

        except Exception as e:
            logger.error(f"Error updating transaction status: {str(e)}")
            return None, f"Error updating transaction status: {str(e)}"

    def _handle_completed_transaction(self, transaction):
        try:
            user = transaction.user
            plan = transaction.plan

            if not user or not plan:
                return transaction, "Transaction completed but user or plan is missing"

            current_period_end = self._calculate_period_end(plan)

            self.subscription_service.create(
                user=user,
                plan=plan,
                status="active",
                daily_message_limit=plan.daily_message_limit,
                daily_file_upload_limit=plan.daily_file_upload_limit,
                daily_image_generation_limit=plan.daily_image_generation_limit,
                tokens_balance=plan.tokens_included,
                current_period_end=current_period_end
            )

            user.subscription_type = plan.code
            user.save(update_fields=['subscription_type'])

            change_user_to_free_plan.apply_async(
                args=[user.id],
                eta=current_period_end
            )

            # Create a serializable transaction context
            transaction_context = {
                "id": transaction.id,
                "transaction_id": transaction.transaction_id,
                "status": transaction.status,
                "created_at": transaction.created_at,
                "payment_method": {
                    "name": str(transaction.payment_method)
                },
                "plan": {
                    "name": plan.name,
                    "price": str(transaction.amount) if hasattr(transaction, 'amount') else str(plan.price),
                    "currency": plan.currency
                },
                "user": {
                    "email": user.email,
                    "name": user.get_full_name() or user.email.split('@')[0].capitalize()
                }
            }

            # Add user_name directly to the context for the template
            context = {
                "transaction": transaction_context, 
                "expire_date": current_period_end,
                "user_name": user.get_full_name() or user.email.split('@')[0].capitalize(),
            }

            self.email_service.send_email(
                to_email=user.email,
                subject="Your PIOO Payment is Complete",
                template_name="payment_complete",
                context=context
            )

            return transaction, f"Transaction completed and subscription created. Plan: {plan.name}, End date: {current_period_end.strftime('%Y-%m-%d')}"

        except Exception as e:
            logger.error(f"Error handling completed transaction: {str(e)}")
            raise ServiceError(f"Error handling completed transaction: {str(e)}")

    def _calculate_period_end(self, plan):
        now = timezone.now()

        interval = plan.interval if hasattr(plan, 'interval') else 'monthly'

        if interval == 'yearly':
            return now + timedelta(days=365)
        elif interval == 'quarterly':
            return now + timedelta(days=90)
        else: 
            return now + timedelta(days=30)
