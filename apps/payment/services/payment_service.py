import stripe
from datetime import datetime, timezone
from django.conf import settings
from dxh_common.logger import Logger

from apps.payment.services import PlanService, SubscriptionService, StripeCustomerService

logger = Logger(__name__)


class PaymentService:
    def __init__(self):
        stripe.api_key = settings.STRIPE_SECRET_KEY
        self.stripe_customer_service = StripeCustomerService()
        self.plan_service = PlanService()
        self.subscrption_service = SubscriptionService()

    def get_or_create_stripe_customer(self, user):
        try:
            stripe_customer = getattr(user, "stripe_customer", None)

            if not stripe_customer or not stripe_customer.customer_id:
                customer = stripe.Customer.create(email=user.email)
                self.stripe_customer_service.create(user=user, customer_id=customer.id)
            else:
                customer = stripe.Customer.retrieve(stripe_customer.customer_id)

            return customer
        
        except stripe.error.StripeError as e:
            logger.error(f"Error in create_payment_method: {str(e)}")
            return None, str(e)
    
    def get_plan_by_price_id(self, price_id):
        """Retrieve the Plan object by price_id."""
        try:
            plan = self.plan_service.get(stripe_price_id=price_id)

            return plan
        
        except Exception as e:
            logger.error(f"Error getting plan: {str(e)}")
            return None

    def create_payment_method(self, user, token):
        try:
            customer = self.get_or_create_stripe_customer(user)

            # Create a new payment method
            payment_method = stripe.PaymentMethod.create(type="card", card={"token": token})

            # List existing payment methods
            existing_methods = stripe.PaymentMethod.list(customer=customer.id, type="card")

            # Check if the card already exists (by fingerprint)
            for method in existing_methods.data:
                if method.card.fingerprint == payment_method.card.fingerprint:
                    
                    return customer.id, None  # Card already exists

            # Attach new payment method
            stripe.PaymentMethod.attach(payment_method.id, customer=customer.id)

            if len(existing_methods.data) == 0:
                stripe.Customer.modify(
                    customer.id,
                    invoice_settings={"default_payment_method": payment_method.id}
                )
                
            invoices = stripe.Invoice.list(customer=customer.id, status="open")
            if invoices.data:
                invoice = invoices.data[0]
                stripe.Invoice.pay(invoice.id)

            return customer.id, None

        except stripe.error.StripeError as e:
            logger.error(f"Error in create_payment_method: {str(e)}")
            return None, str(e)


    def process_payment(self, amount, currency, payment_method_id, user):
        """Process a payment with an attached customer."""
        try:
            stripe_customer = user.stripe_customer.customer_id
            payment_intent = stripe.PaymentIntent.create(
                amount=amount,
                currency=currency,
                payment_method=payment_method_id,
                customer=stripe_customer,
                confirm=True
            )

            return payment_intent.id, None

        except stripe.error.StripeError as e:
            logger.error(f"Error in process_payment: {str(e)}")
            return None, str(e)

    def refund_payment(self, payment_intent_id, amount=None):
        """Issue a refund for a payment."""
        try:
            refund = stripe.Refund.create(
                payment_intent=payment_intent_id,
                amount=amount
            )

            return refund.id, None

        except stripe.error.StripeError as e:
            logger.error(f"Error in refund_payment: {str(e)}")
            return None, str(e)

    def get_payment_methods(self, user):
        """Retrieve all payment methods for a user."""
        try:
            stripe_customer = getattr(user, "stripe_customer", None)
   
            if not stripe_customer:
                return None, "No Stripe customer found"
            
            methods = stripe.PaymentMethod.list(
                customer=stripe_customer.customer_id, 
                type="card"
            )

            return methods.data, None

        except stripe.error.StripeError as e:
            logger.error(f"Error in get_payment_methods: {str(e)}")
            return None, str(e)

    def attach_payment_method(self, user, payment_method_id):
        """Attach a payment method to a customer."""
        try:
            stripe_customer = getattr(user, "stripe_customer", None)
            if not stripe_customer:
                return None, "No Stripe customer found"
            
            stripe.PaymentMethod.attach(
                payment_method_id, 
                customer=stripe_customer.customer_id
            )

            return True, None

        except stripe.error.StripeError as e:
            logger.error(f"Error in attach_payment_method: {str(e)}")
            return False, str(e)

    def set_default_payment_method(self, user, payment_method_id):
        """Set a payment method as the default for invoices."""
        try:
            stripe_customer = getattr(user, "stripe_customer", None)
            if not stripe_customer:
                return None, "No Stripe customer found"
            
            # Verify that the payment method belongs to the customer
            payment_method = stripe.PaymentMethod.retrieve(payment_method_id)
            if payment_method.customer != stripe_customer.customer_id:
                return False, "Payment method does not belong to the customer"

            # Set the payment method as default
            stripe.Customer.modify(
                stripe_customer.customer_id,
                invoice_settings={"default_payment_method": payment_method_id}
            )

            return True, None

        except stripe.error.StripeError as e:
            logger.error(f"Error in set_default_payment_method: {str(e)}")
            return False, str(e)

    def check_existing_subscription(self, customer_id):
        active_subscriptions = stripe.Subscription.list(
            customer=customer_id,
            status="active"
        ).data

        if active_subscriptions:
            subscription = active_subscriptions[0]

            if subscription.cancel_at_period_end:
                stripe.Subscription.modify(subscription.id, cancel_at_period_end=False)
                return subscription.id, None  
            
            return subscription.id, "User is already subscribed."

        return None, None
    
    def store_subscription_in_db(self, user, plan, stripe_subscription):
        current_period_end = None
        if stripe_subscription.current_period_end:
            try:
                current_period_end = datetime.fromtimestamp(
                    stripe_subscription.current_period_end, tz=timezone.utc
                )
            except Exception as e:
                logger.error(f"Error converting current_period_end: {str(e)}")
        return self.subscrption_service.create(
            user=user,
            plan=plan,
            stripe_subscription_id=stripe_subscription.id,
            status="incomplete",
            tokens_balance=plan.tokens_included,
            current_period_end=current_period_end,
        )
    
    def create_subscription(self, user, price_id):
        try:
            customer = self.get_or_create_stripe_customer(user)

            # Check if a subscription already exists
            existing_subscription_id, error = self.check_existing_subscription(customer.id)
            if existing_subscription_id or error:
                return existing_subscription_id, error
            
            plan = self.get_plan_by_price_id(price_id)
            if not plan:
                return None, "Subscription Plan not found."
            
            # Create the subscription WITHOUT a payment method
            stripe_subscription = stripe.Subscription.create(
                customer=customer.id,
                items=[{"price": price_id}],
                payment_behavior="default_incomplete",
                expand=["latest_invoice.payment_intent"]
            )
            subscription = self.store_subscription_in_db(user, plan, stripe_subscription)

            return subscription.id, None

        except stripe.error.StripeError as e:
            logger.error(f"Error in create_subscription: {str(e)}")
            return None, str(e)

        
    def cancel_subscription(self, subscription_id):
        """Cancel an active subscription."""
        try:
            stripe.Subscription.modify(subscription_id, cancel_at_period_end=True)

            return True, None

        except stripe.error.StripeError as e:
            logger.error(f"Error in cancel_subscription: {str(e)}")
            return False, str(e)
