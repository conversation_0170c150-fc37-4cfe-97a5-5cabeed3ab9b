from django.utils import timezone
from dxh_common.base.base_service import BaseService, ServiceError
from dxh_common.logger import Logger
from apps.payment.repositories import SubscriptionRequestRepository
from apps.payment.services import SubscriptionService
from apps.cross_cutting.email_service import EmailService
from common.constants import SubscriptionType

logger = Logger(__name__)

class SubscriptionRequestService(BaseService):
    def __init__(self):
        super().__init__(SubscriptionRequestRepository())
        self.subscription_service = SubscriptionService()
        self.email_service = EmailService()

    def create_request(self, user, request_type, message=None):
        """Create a new subscription request"""
        try:
            # Check if user has an active subscription
            active_subscription = self.subscription_service.get(user=user, status='active')
            if not active_subscription:
                raise ServiceError("No active subscription found for this user")

            # Check if there's already a pending request
            existing_request = self.repository.filter(
                user=user,
                status='pending',
                type=request_type
            ).first()
            
            if existing_request:
                return existing_request, "You already have a pending request"

            # Create new request
            subscription_request = self.create(
                user=user,
                type=request_type,
                status='pending',
                message=message
            )

            # Send notification email to admin
            self._notify_admin_of_new_request(subscription_request)
            
            return subscription_request, None
            
        except Exception as e:
            logger.error(f"Error creating subscription request: {str(e)}")
            raise ServiceError(f"Error creating subscription request: {str(e)}")

    def process_request(self, request_id, status, admin_note=None):
        """Process a subscription request (approve/reject)"""
        try:
            subscription_request = self.get(id=request_id)
            if not subscription_request:
                raise ServiceError("Subscription request not found")
                
            if subscription_request.status != 'pending':
                raise ServiceError("This request has already been processed")
                
            subscription_request.status = status
            subscription_request.admin_note = admin_note
            subscription_request.processed_at = timezone.now()
            subscription_request.save()
            
            # If approved, update the user's subscription
            if status == 'approved':
                if subscription_request.type == 'cancel':
                    self._process_cancellation(subscription_request)
                elif subscription_request.type == 'downgrade':
                    self._process_downgrade(subscription_request)
            
            # Notify user about the request status
            self._notify_user_of_request_status(subscription_request)
            
            return subscription_request, None
            
        except Exception as e:
            logger.error(f"Error processing subscription request: {str(e)}")
            raise ServiceError(f"Error processing subscription request: {str(e)}")
    
    def _process_cancellation(self, subscription_request):
        """Handle the cancellation of a subscription"""
        user = subscription_request.user
        subscription = self.subscription_service.get(user=user, status='active')
        
        if subscription:
            # Check if eligible for refund (within 48 hours)
            time_diff = timezone.now() - subscription.created_at
            eligible_for_refund = time_diff.total_seconds() < (48 * 3600)
            
            # Update subscription status
            subscription.status = 'canceled'
            subscription.canceled_at = timezone.now()
            subscription.save()
            
            # Update user subscription type
            user.subscription_type = SubscriptionType.FREE.value
            user.save(update_fields=['subscription_type'])
            
            # Add refund info to admin note if eligible
            if eligible_for_refund:
                note = subscription_request.admin_note or ""
                subscription_request.admin_note = f"{note}\nEligible for 70% refund (within 48 hours)"
                subscription_request.save()
    
    def _process_downgrade(self, subscription_request):
        """Handle the downgrade of a subscription"""
        user = subscription_request.user
        subscription = self.subscription_service.get(user=user, status='active')
        
        if subscription:
            # Set user to free plan
            user.subscription_type = SubscriptionType.FREE.value
            user.save(update_fields=['subscription_type'])
            
            # Update subscription status
            subscription.status = 'canceled'
            subscription.canceled_at = timezone.now()
            subscription.save()
    
    def _notify_admin_of_new_request(self, subscription_request):
        """Send notification email to admin about new request"""
        try:
            context = {
                "user_email": subscription_request.user.email,
                "request_type": subscription_request.get_type_display(),
                "submitted_at": subscription_request.submitted_at,
                "message": subscription_request.message or "No message provided"
            }
            
            self.email_service.send_email(
                to_email="<EMAIL>",  # Replace with actual admin email
                subject=f"New Subscription {subscription_request.get_type_display()} Request",
                template_name="subscription_request_admin_notification",
                context=context
            )
        except Exception as e:
            logger.error(f"Error sending admin notification: {str(e)}")
    
    def _notify_user_of_request_status(self, subscription_request):
        """Notify user about their request status"""
        try:
            context = {
                "request_type": subscription_request.get_type_display(),
                "status": subscription_request.get_status_display(),
                "submitted_at": subscription_request.submitted_at,
                "processed_at": subscription_request.processed_at,
                "admin_note": subscription_request.admin_note or "No additional notes provided"
            }
            
            self.email_service.send_email(
                to_email=subscription_request.user.email,
                subject=f"Your Subscription {subscription_request.get_type_display()} Request Update",
                template_name="subscription_request_status_update",
                context=context
            )
        except Exception as e:
            logger.error(f"Error sending user notification: {str(e)}")