from celery import shared_task
from django.utils import timezone
from django.db import models
from datetime import timedelta
from django.contrib.auth import get_user_model
from common.constants import SubscriptionType
from common.logger import Logger

from apps.payment.models import Subscription
from apps.cross_cutting.email_service import EmailService

logger = Logger(__name__)
User = get_user_model()


@shared_task
def change_user_to_free_plan(user_id):
    try:
        user = User.objects.get(id=user_id)
        now = timezone.now()

        ended_subscriptions = user.subscriptions.filter(
            status='active',
            current_period_end__lte=now
        )

        for subscription in ended_subscriptions:
            subscription.status = 'canceled'
            subscription.canceled_at = now
            subscription.save(update_fields=['status', 'canceled_at'])

        remaining_active_subscriptions = user.subscriptions.filter(
            status='active',
            current_period_end__gt=now
        ).order_by('-created_at')

        if remaining_active_subscriptions.exists():
            newest_subscription = remaining_active_subscriptions.first()
            user.subscription_type = newest_subscription.plan.code
            user.save(update_fields=['subscription_type'])
            return f"Updated user {user_id} subscription to {newest_subscription.plan.code}"

        user.subscription_type = SubscriptionType.FREE.value
        user.save(update_fields=['subscription_type'])
        
        return f"Successfully changed user {user_id} to free plan"
        
    except User.DoesNotExist:
        logger.error(f"Failed to change subscription: User with ID {user_id} not found")
        return f"Error: User with ID {user_id} not found"
    except Exception as e:
        logger.error(f"Error changing user {user_id} to free plan: {str(e)}")
        return f"Error changing user {user_id} to free plan: {str(e)}"


@shared_task
def send_subscription_renewal_reminders():
    """
    Send reminder emails to users with subscriptions that are about to expire.
    Runs once daily.
    """
    try:

        email_service = EmailService()
        now = timezone.now()
        today = now.date()
        
        logger.info(f"Running subscription reminder check at {now.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Get subscriptions expiring in 3 days
        expiry_date_3days = today + timedelta(days=3)
        
        # Find manual subscriptions (no stripe_subscription_id) that expire in 3 days
        expiring_soon = Subscription.objects.filter(
            status='active',
            current_period_end__date=expiry_date_3days,
            stripe_subscription_id__isnull=True
        )
        
        # Get subscriptions expiring today
        expiring_today = Subscription.objects.filter(
            status='active',
            current_period_end__date=today,
            stripe_subscription_id__isnull=True
        )
        
        logger.info(f"Found {expiring_soon.count()} subscriptions expiring in 3 days")
        logger.info(f"Found {expiring_today.count()} subscriptions expiring today")
        
        # Send 3-day reminders
        for subscription in expiring_soon:
            logger.info(f"Sending 3-day reminder for subscription {subscription.id} (user: {subscription.user.email})")
            _send_renewal_reminder(subscription, email_service, days_left=3)
            
        # Send same-day reminders
        for subscription in expiring_today:
            logger.info(f"Sending today reminder for subscription {subscription.id} (user: {subscription.user.email})")
            _send_renewal_reminder(subscription, email_service, days_left=0)
            
        return f"Checked for expiring subscriptions: Found {expiring_soon.count()} expiring in 3 days and {expiring_today.count()} expiring today"
        
    except Exception as e:
        logger.error(f"Error sending subscription renewal reminders: {str(e)}")
        return f"Error sending subscription renewal reminders: {str(e)}"

def _send_renewal_reminder(subscription, email_service, days_left):
    """Helper function to send the actual reminder email"""
    user = subscription.user
    plan = subscription.plan
    
    if days_left == 0:
        subject = "Your subscription expires today"
        template = "subscription_expiring_today"
    else:
        subject = f"Your subscription expires in {days_left} days"
        template = "subscription_expiring_soon"
    
    context = {
        "user": user,
        "plan": plan,
        "expiry_date": subscription.current_period_end.strftime('%Y-%m-%d'),
        "days_left": days_left
    }
    
    email_service.send_email(
        to_email=user.email,
        subject=subject,
        template_name=template,
        context=context
    )
    
    logger.info(f"Sent renewal reminder to {user.email} for subscription expiring in {days_left} days")


@shared_task
def send_subscription_renewal_reminders_new():
    """
    New implementation of subscription renewal reminders without ReminderLog dependency.
    """
    try:

        # Get the Subscription model directly to avoid any imports that might reference ReminderLog
        Subscription = models.get_model('payment', 'Subscription')
        
        email_service = EmailService()
        
        now = timezone.now()
        today = now.date()
        
        logger.info(f"Running subscription reminder check at {now.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Get subscriptions expiring in 3 days
        expiry_date_3days = today + timedelta(days=3)
        
        # Find manual subscriptions (no stripe_subscription_id) that expire in 3 days
        expiring_soon = Subscription.objects.filter(
            status='active',
            current_period_end__date=expiry_date_3days,
            stripe_subscription_id__isnull=True
        )
        
        # Get subscriptions expiring today
        expiring_today = Subscription.objects.filter(
            status='active',
            current_period_end__date=today,
            stripe_subscription_id__isnull=True
        )
        
        logger.info(f"Found {expiring_soon.count()} subscriptions expiring in 3 days")
        logger.info(f"Found {expiring_today.count()} subscriptions expiring today")
        
        # Send emails directly without using the helper function
        for subscription in expiring_soon:
            user = subscription.user
            plan = subscription.plan
            subject = f"Your subscription expires in 3 days"
            template = "subscription_expiring_soon"
            
            context = {
                "user": user,
                "plan": plan,
                "expiry_date": subscription.current_period_end.strftime('%Y-%m-%d'),
                "days_left": 3
            }
            
            email_service.send_email(
                to_email=user.email,
                subject=subject,
                template_name=template,
                context=context
            )
            
            logger.info(f"Sent 3-day reminder to {user.email}")
            
        # Send same-day reminders
        for subscription in expiring_today:
            user = subscription.user
            plan = subscription.plan
            subject = "Your subscription expires today"
            template = "subscription_expiring_today"
            
            context = {
                "user": user,
                "plan": plan,
                "expiry_date": subscription.current_period_end.strftime('%Y-%m-%d'),
                "days_left": 0
            }
            
            email_service.send_email(
                to_email=user.email,
                subject=subject,
                template_name=template,
                context=context
            )
            
            logger.info(f"Sent today reminder to {user.email}")
            
        return f"Checked for expiring subscriptions: Found {expiring_soon.count()} expiring in 3 days and {expiring_today.count()} expiring today"
        
    except Exception as e:
        logger.error(f"Error sending subscription renewal reminders: {str(e)}")
        return f"Error sending subscription renewal reminders: {str(e)}"
