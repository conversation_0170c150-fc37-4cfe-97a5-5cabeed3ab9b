[{"model": "payment.paymentmethod", "pk": 1, "fields": {"user": 1, "type": "CREDIT_CARD", "provider": "stripe", "token_id": "pm_1234567890", "last_digits": "4242", "expiry_date": "2025-12-31", "is_default": true, "metadata": {"card_brand": "visa", "card_type": "credit"}, "company": 1, "is_active": true, "created_at": "2024-01-30T08:00:00Z", "updated_at": "2024-01-30T08:00:00Z"}}, {"model": "payment.payment", "pk": 1, "fields": {"user": 1, "payment_method": 1, "amount": "199.99", "currency": "USD", "status": "COMPLETED", "transaction_id": "txn_1234567890", "description": "Premium Plan Subscription", "provider_response": {"charge_id": "ch_1234567890", "payment_intent": "pi_1234567890"}, "error_message": null, "metadata": {"subscription_id": "sub_1234567890"}, "refunded_amount": "0.00", "company": 1, "is_active": true, "created_at": "2024-01-30T08:00:00Z", "updated_at": "2024-01-30T08:00:00Z"}}, {"model": "payment.refund", "pk": 1, "fields": {"payment": 1, "amount": "50.00", "reason": "Partial refund requested by customer", "status": "COMPLETED", "refund_id": "re_1234567890", "provider_response": {"refund_id": "ref_1234567890", "status": "succeeded"}, "error_message": null, "metadata": {"refund_reason_code": "customer_request"}, "company": 1, "is_active": true, "created_at": "2024-01-30T09:00:00Z", "updated_at": "2024-01-30T09:00:00Z"}}, {"model": "payment.paymentlog", "pk": 1, "fields": {"payment": 1, "action": "PAYMENT_INITIATED", "status": "SUCCESS", "message": "Payment successfully processed", "metadata": {"ip_address": "***********", "user_agent": "Mozilla/5.0"}, "company": 1, "is_active": true, "created_at": "2024-01-30T08:00:00Z", "updated_at": "2024-01-30T08:00:00Z"}}]