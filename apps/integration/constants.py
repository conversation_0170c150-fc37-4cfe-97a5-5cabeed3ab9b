from dxh_libraries.translation import gettext_lazy as _


PROVIDER_TYPE_CHOICES = [
    ("oauth", _("OAuth2 Provider")),
    ("api_key", _("API Key Provider")),
]

PROVIDER_CHOICES = [
    ("google", _("Google")),
    ("facebook", _("Facebook")),
    ("twitter", _("Twitter")),
    ("linkedin", _("LinkedIn")),
    ("github", _("GitHub")),
    ("stripe", _("Stripe")),
    ("openai", _("OpenAI")),
    ("deepseek", _("DeepSeek")),
    ("qwen", _("Qwen")),
    ("lama", _("<PERSON>")),
]

USERNAME_SUFFIX_CHARS = "abcdefghijklmnopqrstuvwxyz0123456789"
MAX_USERNAME_SUFFIX_LENGTH = 4
USERNAME_MIN_LENGTH = 3
# Cache timeout in seconds (5 minutes)
CACHE_TIMEOUT = 300