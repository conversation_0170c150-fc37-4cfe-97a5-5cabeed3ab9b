from django.conf import settings
from django.core.cache import cache
from dxh_common.logger import Logger

from apps.integration.services import IntegrationProviderService, AppIntegrationService
from apps.core.services import SystemSettingService
from apps.integration.constants import CACHE_TIMEOUT

logger = Logger(__name__)


class ConfigHelper:
    def __init__(self):
        self.integration_provider_service = IntegrationProviderService()
        self.app_integration_service = AppIntegrationService()
        self.system_setting_service = SystemSettingService()

    def get_integration_setting(self, provider_name, setting_key, default=None):
        cache_key = f"integration_setting:{provider_name}:{setting_key}"

        cached_value = cache.get(cache_key)
        if cached_value:
            return cached_value

        try:
            provider = self.integration_provider_service.get(name=provider_name)
            if not provider:
                logger.warning(f"Provider '{provider_name}' not found in database")
                return self._get_from_settings(provider_name, setting_key, default)

            app_integration = self.app_integration_service.get(provider=provider)
            if not app_integration:
                logger.warning(f"No AppIntegration found for provider '{provider_name}'")
                return self._get_from_settings(provider_name, setting_key, default)

            if setting_key == 'client_id':
                value = app_integration.client_id
            elif setting_key == 'client_secret':
                value = app_integration.client_secret
            elif setting_key == 'api_key':
                value = app_integration.api_key
            elif setting_key in app_integration.custom_credentials:
                value = app_integration.custom_credentials.get(setting_key)
            else:
                logger.warning(f"Setting '{setting_key}' not found for provider '{provider_name}'")
                return self._get_from_settings(provider_name, setting_key, default)

            if value:
                cache.set(cache_key, value, CACHE_TIMEOUT)
                return value

            return self._get_from_settings(provider_name, setting_key, default)

        except Exception as e:
            logger.error(f"Error getting integration setting: {e}")
            return self._get_from_settings(provider_name, setting_key, default)


    def _get_from_settings(self, provider_name, setting_key, default=None):
        settings_map = {
            'google': {
                'client_id': 'SOCIAL_AUTH_GOOGLE_OAUTH2_KEY',
                'client_secret': 'SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET',
            },
            'facebook': {
                'client_id': 'SOCIAL_AUTH_FACEBOOK_KEY',
                'client_secret': 'SOCIAL_AUTH_FACEBOOK_SECRET',
            },
            'stripe': {
                'public_key': 'STRIPE_PUBLIC_KEY',
                'secret_key': 'STRIPE_SECRET_KEY',
                'webhook_secret': 'STRIPE_WEBHOOK_SECRET',
            },
            'openai': {
                'api_key': 'OPENAI_API_KEY',
            },
            'deepseek': {
                'api_key': 'DEEPSEEK_API_KEY',
            },
        }
        try:
            if provider_name in settings_map and setting_key in settings_map[provider_name]:
                setting_name = settings_map[provider_name][setting_key]
                return getattr(settings, setting_name, default)

            if hasattr(settings, f"{provider_name.upper()}_{setting_key.upper()}"):
                return getattr(settings, f"{provider_name.upper()}_{setting_key.upper()}", default)

            return default

        except Exception as e:
            logger.error(f"Error getting setting from settings.py: {e}")
            raise e

    def clear_cache(self):
        try:
            providers = self.integration_provider_service.get_all()
            for provider in providers:
                for setting_key in ['client_id', 'client_secret', 'api_key']:
                    cache_key = f"integration_setting:{provider.name}:{setting_key}"
                    cache.delete(cache_key)
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            raise e