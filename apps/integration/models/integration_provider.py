from django.db import models
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel
from apps.integration.constants import PROVIDER_TYPE_CHOICES, PROVIDER_CHOICES


class IntegrationProvider(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="company_integration_providers",
        verbose_name=_("Company")
    )
    name = models.CharField(
        max_length=100, 
        unique=True, 
        choices=PROVIDER_CHOICES,
        verbose_name=_("Provider Name"),
    )
    provider_type = models.CharField(
        max_length=10, 
        choices=PROVIDER_TYPE_CHOICES, 
        default=PROVIDER_TYPE_CHOICES[0][0],
        verbose_name=_("Provider Type"),
    )
    auth_url = models.URLField(
        blank=True,
        null=True, 
        help_text="OAuth authorization endpoint"
    )
    token_url = models.URLField(
        blank=True,
        null=True, 
        verbose_name=_("Token URL"),
    )
    api_base_url = models.URLField(
        blank=True, 
        null=True, 
        verbose_name=_("API Base URL"),
    )
    redirect_uri = models.URLField(
        blank=True, 
        null=True, 
        verbose_name=_("Redirect URI"),
    )
    scopes = models.TextField(
        blank=True, 
        null=True,
        help_text="Comma-separated list of scopes e,g: (read, write)",
        verbose_name=_('Scopes'),
    )
    api_key_param = models.CharField(
        max_length=100, 
        blank=True, 
        null=True, 
        verbose_name=_("API Key Param"),
    )

    class Meta:
        db_table = "integration_providers"
        verbose_name = "Integration Provider"
        verbose_name_plural = "Integration Providers"

    def __str__(self):
        return f"{self.name} ({self.provider_type})"
