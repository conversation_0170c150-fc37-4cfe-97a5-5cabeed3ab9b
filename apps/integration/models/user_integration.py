from django.db import models
from django.utils.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel
from dxh_libraries.encrypted_model_fields import EncryptedCharField
from apps.integration.models.app_integration import AppIntegration


class UserIntegration(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="company_user_integrations",
        verbose_name=_("Company")
    )
    user = models.ForeignKey(
        "identity.User",
        on_delete=models.SET_NULL,
        null=True,
        related_name="user_integrations",
        verbose_name=_("User"),
    )
    app_integration = models.ForeignKey(
        AppIntegration,
        on_delete=models.SET_NULL,
        null=True,
        related_name="user_app_integrations",
        verbose_name=_("App Integration"),
    )
    access_token = EncryptedCharField(
        max_length=512, 
        null=True, 
        verbose_name=_("Access Token"),
        help_text="OAuth access token for the integration",
    )
    refresh_token = EncryptedCharField(
        max_length=512, 
        null=True, 
        blank=True, 
        verbose_name=_("Refresh Token"),
        help_text="OAuth refresh token for getting new access tokens",
    )
    expires_at = models.DateTimeField(
        null=True, 
        blank=True, 
        verbose_name=_("Token Expiry Time"),
        help_text="Expiration time of the access token",
    )
    platform_user_id = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Platform User ID"),
        help_text="User ID assigned by the platform (e.g., Facebook, LinkedIn)",
    )
    custom_user_credentials = models.JSONField(
        blank=True, 
        null=True, 
        help_text="User-specific credentials",
        verbose_name=_("Custom User Credentials"),
    )

    class Meta:
        db_table = "integration_user_integrations"
        verbose_name = "User Integration"
        verbose_name_plural = "User Integrations"
        unique_together = ("user", "app_integration")  

    def __str__(self):
        return f"{self.user} - {self.app_integration}"
