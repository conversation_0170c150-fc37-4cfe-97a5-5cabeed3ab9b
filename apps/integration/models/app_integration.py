from django.db import models
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel
from apps.integration.models.integration_provider import IntegrationProvider


class AppIntegration(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="company_app_integrations",
        verbose_name=_("Company")
    )
    provider = models.ForeignKey(
        IntegrationProvider, 
        on_delete=models.SET_NULL,
        null=True, 
        help_text="Integration provider"
    )
    client_id = models.CharField(
        max_length=255, 
        null=True, 
        blank=True,
        verbose_name=_("Client ID"),
    )
    client_secret = models.CharField(
        max_length=255, 
        null=True, 
        blank=True,
        verbose_name=_("Client Secret"),
    )
    api_key = models.CharField(
        max_length=255, 
        blank=True, 
        null=True, 
        help_text="API key for API-based authentication",
        verbose_name=_("API Key"),
    )
    custom_credentials = models.JSONField(
        blank=True, 
        null=True, 
        help_text="Any other credentials required",
        verbose_name=_("Custom Credentials"),
    )

    class Meta:
        db_table = "integration_app_integrations"
        verbose_name = "App Integration"
        verbose_name_plural = "App Integrations"

    def __str__(self):
        return f"{self.provider.name}"