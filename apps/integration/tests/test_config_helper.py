"""
Tests for the integration configuration helper.
"""

from unittest import mock
from django.test import TestCase
from django.conf import settings
from django.core.cache import cache

from apps.integration.models import IntegrationProvider, AppIntegration
from apps.integration.utils import ConfigHelper


class ConfigHelperTests(TestCase):
    def setUp(self):
        # Create a config helper instance
        self.config_helper = ConfigHelper()

        # Clear the cache before each test
        self.config_helper.clear_cache()
        cache.clear()

        # Create a test provider
        self.provider = IntegrationProvider.objects.create(
            name='test_provider',
            provider_type='api_key',
            api_base_url='https://api.test.com',
            api_key_param='Authorization'
        )

        # Create a test app integration
        self.app_integration = AppIntegration.objects.create(
            provider=self.provider,
            client_id='test_client_id',
            client_secret='test_client_secret',
            api_key='test_api_key',
            custom_credentials={
                'webhook_secret': 'test_webhook_secret',
                'max_tokens': 100
            }
        )

    def test_get_integration_setting_from_db(self):
        # Test getting settings from the database
        self.assertEqual(self.config_helper.get_integration_setting('test_provider', 'client_id'), 'test_client_id')
        self.assertEqual(self.config_helper.get_integration_setting('test_provider', 'client_secret'), 'test_client_secret')
        self.assertEqual(self.config_helper.get_integration_setting('test_provider', 'api_key'), 'test_api_key')
        self.assertEqual(self.config_helper.get_integration_setting('test_provider', 'webhook_secret'), 'test_webhook_secret')
        self.assertEqual(self.config_helper.get_integration_setting('test_provider', 'max_tokens'), 100)

    def test_get_integration_setting_default(self):
        # Test getting a setting that doesn't exist
        self.assertIsNone(self.config_helper.get_integration_setting('test_provider', 'nonexistent_setting'))
        self.assertEqual(self.config_helper.get_integration_setting('test_provider', 'nonexistent_setting', 'default_value'), 'default_value')

    def test_get_integration_setting_nonexistent_provider(self):
        # Test getting a setting for a provider that doesn't exist
        self.assertIsNone(self.config_helper.get_integration_setting('nonexistent_provider', 'client_id'))
        self.assertEqual(self.config_helper.get_integration_setting('nonexistent_provider', 'client_id', 'default_value'), 'default_value')

    @mock.patch('apps.integration.utils.config_helper.ConfigHelper._get_from_settings')
    def test_get_integration_setting_fallback(self, mock_get_from_settings):
        # Test fallback to settings.py
        mock_get_from_settings.return_value = 'fallback_value'

        # Delete the app integration to force fallback
        self.app_integration.delete()

        self.assertEqual(self.config_helper.get_integration_setting('test_provider', 'client_id'), 'fallback_value')
        mock_get_from_settings.assert_called_with('test_provider', 'client_id', None)

    def test_get_integration_setting_caching(self):
        # Test that values are cached
        # First call to cache the value
        self.assertEqual(self.config_helper.get_integration_setting('test_provider', 'client_id'), 'test_client_id')

        # Mock the get method to verify it's not called again
        with mock.patch.object(self.config_helper.integration_provider_service, 'get') as mock_get:
            # Second call should use the cache
            self.assertEqual(self.config_helper.get_integration_setting('test_provider', 'client_id'), 'test_client_id')
            # The get method should not be called again
            mock_get.assert_not_called()

    def test_clear_cache(self):
        # Test clearing the cache
        # First call to cache the value
        self.assertEqual(self.config_helper.get_integration_setting('test_provider', 'client_id'), 'test_client_id')

        # Update the value in the database
        self.app_integration.client_id = 'new_client_id'
        self.app_integration.save()

        # Should still get the cached value
        self.assertEqual(self.config_helper.get_integration_setting('test_provider', 'client_id'), 'test_client_id')

        # Clear the cache
        self.config_helper.clear_cache()

        # Should get the new value
        self.assertEqual(self.config_helper.get_integration_setting('test_provider', 'client_id'), 'new_client_id')
