# Generated by Django 5.1.5 on 2025-05-16 14:12

import django.db.models.deletion
import dxh_libraries.encrypted_model_fields
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0003_address_uuid_company_uuid_contactinfo_uuid_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='IntegrationProvider',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('name', models.CharField(choices=[('google', 'Google'), ('facebook', 'Facebook'), ('twitter', 'Twitter'), ('linkedin', 'LinkedIn'), ('github', 'GitHub'), ('stripe', 'Stripe'), ('openai', 'OpenAI'), ('deepseek', 'DeepSeek'), ('qwen', 'Qwen'), ('lama', 'Lama')], max_length=100, unique=True, verbose_name='Provider Name')),
                ('provider_type', models.CharField(choices=[('oauth', 'OAuth2 Provider'), ('api_key', 'API Key Provider')], default='oauth', max_length=10, verbose_name='Provider Type')),
                ('auth_url', models.URLField(blank=True, help_text='OAuth authorization endpoint', null=True)),
                ('token_url', models.URLField(blank=True, null=True, verbose_name='Token URL')),
                ('api_base_url', models.URLField(blank=True, null=True, verbose_name='API Base URL')),
                ('redirect_uri', models.URLField(blank=True, null=True, verbose_name='Redirect URI')),
                ('scopes', models.TextField(blank=True, help_text='Comma-separated list of scopes e,g: (read, write)', null=True, verbose_name='Scopes')),
                ('api_key_param', models.CharField(blank=True, max_length=100, null=True, verbose_name='API Key Param')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='company_integration_providers', to='core.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Integration Provider',
                'verbose_name_plural': 'Integration Providers',
                'db_table': 'integration_providers',
            },
        ),
        migrations.CreateModel(
            name='AppIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('client_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='Client ID')),
                ('client_secret', models.CharField(blank=True, max_length=255, null=True, verbose_name='Client Secret')),
                ('api_key', models.CharField(blank=True, help_text='API key for API-based authentication', max_length=255, null=True, verbose_name='API Key')),
                ('custom_credentials', models.JSONField(blank=True, help_text='Any other credentials required', null=True, verbose_name='Custom Credentials')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='company_app_integrations', to='core.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
                ('provider', models.ForeignKey(help_text='Integration provider', null=True, on_delete=django.db.models.deletion.SET_NULL, to='integration.integrationprovider')),
            ],
            options={
                'verbose_name': 'App Integration',
                'verbose_name_plural': 'App Integrations',
                'db_table': 'integration_app_integrations',
            },
        ),
        migrations.CreateModel(
            name='UserIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('access_token', dxh_libraries.encrypted_model_fields.EncryptedCharField(help_text='OAuth access token for the integration', null=True, verbose_name='Access Token')),
                ('refresh_token', dxh_libraries.encrypted_model_fields.EncryptedCharField(blank=True, help_text='OAuth refresh token for getting new access tokens', null=True, verbose_name='Refresh Token')),
                ('expires_at', models.DateTimeField(blank=True, help_text='Expiration time of the access token', null=True, verbose_name='Token Expiry Time')),
                ('platform_user_id', models.CharField(blank=True, help_text='User ID assigned by the platform (e.g., Facebook, LinkedIn)', max_length=255, null=True, verbose_name='Platform User ID')),
                ('custom_user_credentials', models.JSONField(blank=True, help_text='User-specific credentials', null=True, verbose_name='Custom User Credentials')),
                ('app_integration', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user_app_integrations', to='integration.appintegration', verbose_name='App Integration')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='company_user_integrations', to='core.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user_integrations', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'User Integration',
                'verbose_name_plural': 'User Integrations',
                'db_table': 'integration_user_integrations',
                'unique_together': {('user', 'app_integration')},
            },
        ),
    ]
