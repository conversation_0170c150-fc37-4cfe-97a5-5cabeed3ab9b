from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_libraries.translation import gettext_lazy as _

from apps.integration.services.Integration_provider_service import IntegrationProviderService
from apps.integration.api.v1.serializers import IntegrationProviderSerializer

logger = Logger(__name__)


class IntegrationProviderListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.service = IntegrationProviderService()

    def get(self, request, id):
        try:
            obj = self.service.get(id=id)
            if not obj:
                result = {
                    "message": _("Not found"), 
                    "errors": {"id": f"No IntegrationProvider found with ID {id}."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            serializer = IntegrationProviderSerializer(obj)
            result = {
                "message": "Provider details retrived successfully", 
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "IntegrationProviderListView:get", 
                          "message": "Unexpected error", "error": str(e), "id": id})
            raise e
