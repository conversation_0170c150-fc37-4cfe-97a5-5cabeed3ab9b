from django.contrib import admin
from django.utils.html import format_html
from common.config import BaseAdmin

from apps.integration.models.integration_provider import IntegrationProvider
from apps.integration.models.app_integration import AppIntegration
from apps.integration.models.user_integration import UserIntegration
from apps.integration.utils import ConfigHelper


@admin.register(IntegrationProvider)
class IntegrationProviderAdmin(BaseAdmin):
    list_display = ('name', 'provider_type', 'company', 'api_base_url')
    list_filter = ('provider_type',)
    search_fields = ('name', 'api_base_url')
    fieldsets = (
        (None, {
            'fields': ('company', 'name', 'provider_type')
        }),
        ('OAuth2 Settings', {
            'fields': ('auth_url', 'token_url', 'redirect_uri', 'scopes'),
            'classes': ('collapse',),
            'description': 'Settings specific to OAuth2 providers'
        }),
        ('API Settings', {
            'fields': ('api_base_url', 'api_key_param'),
            'classes': ('collapse',),
            'description': 'Settings for API-based providers'
        }),
    )

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        config_helper = ConfigHelper()
        config_helper.clear_cache()


class CustomCredentialsInline(admin.TabularInline):
    model = AppIntegration
    fields = ('provider', 'client_id', 'client_secret', 'api_key', 'custom_credentials')
    readonly_fields = ('provider',)
    extra = 0
    max_num = 0
    can_delete = False


@admin.register(AppIntegration)
class AppIntegrationAdmin(BaseAdmin):
    list_display = ('provider_name', 'provider_type', 'has_client_id', 'has_api_key', 'has_custom_credentials')
    list_filter = ('provider__provider_type',)
    search_fields = ('provider__name',)
    fieldsets = (
        (None, {
            'fields': ('company', 'provider', )
        }),
        ('OAuth2 Credentials', {
            'fields': ('client_id', 'client_secret'),
            'description': 'Client credentials for OAuth2 providers'
        }),
        ('API Credentials', {
            'fields': ('api_key', 'custom_credentials'),
            'description': 'API keys and other credentials for API-based providers'
        }),
    )

    def provider_name(self, obj):
        return obj.provider.name if obj.provider else '-'
    provider_name.short_description = 'Provider'
    provider_name.admin_order_field = 'provider__name'

    def provider_type(self, obj):
        return obj.provider.get_provider_type_display() if obj.provider else '-'
    provider_type.short_description = 'Type'
    provider_type.admin_order_field = 'provider__provider_type'

    def has_client_id(self, obj):
        return bool(obj.client_id)
    has_client_id.short_description = 'Client ID'
    has_client_id.boolean = True

    def has_api_key(self, obj):
        return bool(obj.api_key)
    has_api_key.short_description = 'API Key'
    has_api_key.boolean = True

    def has_custom_credentials(self, obj):
        return bool(obj.custom_credentials)
    has_custom_credentials.short_description = 'Custom Creds'
    has_custom_credentials.boolean = True

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        config_helper = ConfigHelper()
        config_helper.clear_cache()


@admin.register(UserIntegration)
class UserIntegrationAdmin(BaseAdmin):
    list_display = ("user", "app_integration_name", "expires_at", "is_expired")
    list_filter = ("app_integration__provider__provider_type", "expires_at")
    search_fields = ("user__email", "user__username", "app_integration__provider__name")
    readonly_fields = ("is_expired",)

    def app_integration_name(self, obj):
        if obj.app_integration and obj.app_integration.provider:
            return obj.app_integration.provider.name
        return '-'
    app_integration_name.short_description = 'Integration'
    app_integration_name.admin_order_field = 'app_integration__provider__name'

    def is_expired(self, obj):
        if not obj.expires_at:
            return None
        from django.utils import timezone
        is_expired = obj.expires_at < timezone.now()
        color = 'red' if is_expired else 'green'
        text = 'Expired' if is_expired else 'Valid'
        return format_html('<span style="color: {color};">{text}</span>', color=color, text=text)
    is_expired.short_description = 'Status'