from django.db import models
from django.utils.translation import gettext_lazy as _
from common.constants import ElementGroup
from common.models import BaseModel

class Element(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        related_name="company_elements",
        verbose_name=_("Company")
    )
    name = models.CharField(
        max_length=100,
        db_index=True,
        verbose_name=_("Element Name")
    )
    code = models.CharField(
        max_length=50,
        unique=True,
        db_index=True,
        verbose_name=_("Element Code")
    )
    group = models.CharField(
        max_length=10,
        choices=ElementGroup.choices(),
        db_index=True,
        verbose_name=_("Element Group")
    )
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Description")
    )

    class Meta:
        db_table = "core_elements"
        verbose_name = _("Element")
        verbose_name_plural = _("Elements")

    def __str__(self):
        return f"{self.name} ({self.group})"
