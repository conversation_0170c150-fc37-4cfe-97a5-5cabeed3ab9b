from django.db import models
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel
from apps.core.constants import ContactType

class ContactInfo(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        related_name="company_contacts",
        verbose_name=_("Company")
    )
    name = models.CharField(
        max_length=255,
        db_index=True, 
        verbose_name=_("Contact Name")
    )
    phone_number = models.Char<PERSON>ield(
        null=True,
        blank=True,
        max_length=20,
        verbose_name=_("Phone Number")
    )
    mobile_no = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        verbose_name=_("Mobile Number")
    )
    email = models.EmailField(
        null=True,
        blank=True,
        verbose_name=_("Email Address")
    )
    type = models.CharField(
        max_length=20,
        choices=ContactType.choices(),
        blank=True,
        null=True,
        verbose_name=_("Contact Type")
    )
    is_default = models.BooleanField(
        default=False,
        verbose_name=_("Is Default Contact")
    )

    class Meta:
        db_table = "core_contact_infos"
        verbose_name = _("Contact info")
        verbose_name_plural = _("Contact infos")

    def __str__(self):
        return f"{self.email} - {self.phone_number}"
