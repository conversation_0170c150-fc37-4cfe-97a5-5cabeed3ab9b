from django.db import models
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel

class State(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        related_name="company_states",
        verbose_name=_("Company")
    )
    name = models.Char<PERSON>ield(
        max_length=255,
        verbose_name=_("State Name")
    )
    code = models.CharField(
        max_length=10, 
        unique=True,
        db_index=True,
        verbose_name=_("State Code")
    )
    country = models.ForeignKey(
        "core.Country",
        on_delete=models.SET_NULL,
        null=True,
        related_name="country_states",
        db_index=True,
        verbose_name=_("Country")
    )

    class Meta:
        db_table = "core_states"
        verbose_name = _("State")
        verbose_name_plural = _("States")

    def __str__(self):
        return f"{self.name} ({self.code})"
