from django.db import models
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel


class Timezone(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        related_name="company_timezones",
        null=True,
        verbose_name=_("Company")
    )
    name = models.Char<PERSON>ield(
        max_length=100, 
        unique=True, 
        db_index=True,
        verbose_name=_("Timezone Name")
    )
    code = models.CharField(
        max_length=50,
        unique=True,
        db_index=True,
        verbose_name=_("Timezone Code")
    )
    offset = models.CharField(
        max_length=50,
        verbose_name=_("UTC Offset")
    )
    country = models.ForeignKey(
        "core.Country",
        on_delete=models.SET_NULL,
        related_name="country_timezones",
        null=True,
        db_index=True,
        verbose_name=_("Country")
    )

    class Meta:
        db_table = "core_timezones"
        verbose_name = _("Timezone")
        verbose_name_plural = _("Timezones")

    def __str__(self):
        return f"{self.name} ({self.offset})"
