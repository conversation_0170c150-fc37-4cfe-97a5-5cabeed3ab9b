from django.db import models
from django.utils.translation import gettext_lazy as _
from apps.core.constants import AddressType
from common.models import BaseModel


class Address(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        related_name="company_addresses",
        verbose_name=_("Company"),
    )
    address_type = models.CharField(
        max_length=10,
        choices=AddressType.choices(),
        null=True,
        blank=True,
        verbose_name=_("Address Type"),
        help_text=_("Type of address, e.g., 'billing' or 'shipping'."),
    )
    country = models.ForeignKey(
        "core.Country",
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_("Country"),
        related_name="country_addresses",
    )
    state = models.ForeignKey(
        "core.State",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("State"),
        related_name="state_addresses",
    )
    city = models.Char<PERSON>ield(
        max_length=100, 
        null=True, 
        blank=True, 
        verbose_name=_("City")
    )
    address_line_1 = models.CharField(
        max_length=255, 
        null=True, 
        blank=True, 
        verbose_name=_("Address Line 1")
    )
    address_line_2 = models.CharField(
        max_length=255, 
        null=True, 
        blank=True, 
        verbose_name=_("Address Line 2")
    )
    postal_code = models.CharField(
        max_length=20,
        verbose_name=_("Postal Code"),
        db_index=True,
    )

    class Meta:
        db_table = "core_addresses"
        verbose_name = _("Address")
        verbose_name_plural = _("Addresses")

    def __str__(self):
        return f'{self.postal_code or ""}, {self.city or ""}, {self.country or ""}'.strip(", ")
