from django.db import models
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel

class Company(BaseModel):
    name = models.CharField(
        max_length=200,
        unique=True,
        db_index=True,
        verbose_name=_("Company Name")
    )
    short_name = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name=_("Short Name")
    )
    slogan = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Company Slogan")
    )
    logo = models.ImageField(
        upload_to="logo/",
        null=True,
        blank=True,
        verbose_name=_("Company Logo")
    )
    url = models.URLField(
        null=True,
        blank=True,
        verbose_name=_("Website URL")
    )
    email = models.EmailField(
        unique=True,
        verbose_name=_("Company Email")
    )
    mobile_no = models.CharField(
        null=True,
        blank=True,
        max_length=20,
        verbose_name=_("Mobile Number")
    )
    phone_no = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        verbose_name=_("Phone Number")
    )
    country = models.CharField(
        max_length=255,
        verbose_name=_("Country")
    )
    state = models.CharField(
        max_length=255,
        verbose_name=_("State/Province")
    )
    city = models.CharField(
        max_length=255,
        verbose_name=_("City")
    )
    address = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Street Address")
    )

    class Meta:
        db_table = "core_companies"
        verbose_name = _("Company")
        verbose_name_plural = _("Companies")

    def __str__(self) -> str:
        return self.name
