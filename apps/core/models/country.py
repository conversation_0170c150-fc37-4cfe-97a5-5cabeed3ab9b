from django.db import models
from django.utils.translation import gettext_lazy as _
from common.models import BaseModel

class Country(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        related_name="company_countries",
        verbose_name=_("Company")
    )
    name = models.CharField(
        max_length=100, 
        db_index=True,
        verbose_name=_("Country Name")
    )
    code = models.CharField(
        max_length=100, 
        unique=True,
        db_index=True,
        verbose_name=_("Country Code")
    )
    native = models.Char<PERSON>ield(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_("Native Name")
    )
    phone_code = models.CharField(
        max_length=20,  
        verbose_name=_("Phone Code")
    )
    capital = models.CharField(
        max_length=100, 
        null=True,
        blank=True,
        verbose_name=_("Capital City")
    )
    continent = models.CharField(
        max_length=2, 
        null=True,
        blank=True,
        verbose_name=_("Continent Code")
    )

    class Meta:
        db_table = "core_countries"
        verbose_name = _("Country")
        verbose_name_plural = _("Countries")

    def __str__(self):
        return self.name
