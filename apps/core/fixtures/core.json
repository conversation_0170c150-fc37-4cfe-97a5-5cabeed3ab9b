[{"model": "core.company", "pk": "1", "fields": {"name": "FinTech Revolution Ltd.", "short_name": "FinRevo", "slogan": "Revolutionizing Financial Solutions", "logo": null, "url": "https://www.fintechrevolution.com", "email": "<EMAIL>", "mobile_no": "+91-98765-43210", "phone_no": "+91-98765-43211", "country": "India", "state": "Maharashtra", "city": "Mumbai", "address": "12 Tech Park Road, Mumbai, MH, 400001", "created_at": "2025-01-23T12:00:00Z", "updated_at": "2025-01-23T12:00:00Z"}}, {"model": "core.SystemSetting", "pk": "1", "fields": {"company": 1, "app_name": "devx<PERSON>b", "app_version": "1.0.0", "default_date_format": "DD/MM/YYYY", "default_datetime_formate": "DD/MM/YYYY HH:mm:ss", "time_format": "24-hour", "default_currency": "USD", "default_language": "en", "decimal_precision": 2, "default_ordering": "asc", "default_timezone": "UTC", "maintenance_mode_enabled": false, "maintenance_message": "The system is currently under maintenance.", "session_timeout": 30, "pagination_size": 10, "api_rate_limit": 1000, "max_upload_size": 5242880, "caching_enabled": true, "cache_expiry_time": 300, "logging_enabled": true, "log_level": "INFO", "terms_of_service_url": "", "privacy_policy_url": "", "password_format": null, "login_attempts": 3, "created_at": "2025-01-23T12:00:00Z", "updated_at": "2025-01-23T12:00:00Z"}}, {"model": "core.Element", "pk": "1", "fields": {"company": 1, "name": "English", "code": "EN", "group": "language", "description": "Represents the English language", "created_at": "2025-01-01T12:00:00Z", "updated_at": "2025-01-01T12:00:00Z"}}, {"model": "core.state", "pk": "1", "fields": {"company": 1, "name": "California", "code": "CA", "country": null, "created_at": "2023-01-01T10:00:00Z", "updated_at": "2023-01-01T12:00:00Z"}}, {"model": "core.Address", "pk": "1", "fields": {"company": 1, "address_type": "billing", "country": null, "state": 1, "city": "San Francisco", "address_line_1": "123 Market St", "address_line_2": "Suite 100", "postal_code": "94103", "created_at": "2025-01-01T12:00:00Z", "updated_at": "2025-01-01T12:00:00Z"}}, {"model": "core.ContactInfo", "pk": "1", "fields": {"company": 1, "name": "<PERSON>", "phone_number": "************", "mobile_no": "************", "email": "<EMAIL>", "type": "billing", "is_default": true, "created_at": "2023-01-01T10:00:00Z", "updated_at": "2023-01-01T12:00:00Z"}}]