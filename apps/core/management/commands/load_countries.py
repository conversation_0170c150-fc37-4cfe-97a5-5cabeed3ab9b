import json
from django.core.management.base import BaseCommand
from django.db.utils import IntegrityError
from apps.core.models import Country


class Command(BaseCommand):
    help = "Load countries data from a JSON file into the database"

    def add_arguments(self, parser):
        parser.add_argument(
            "--file",
            type=str,
            default="apps/core/assets/countries.json",
            help="Path to the JSON file containing country data",
        )

    def handle(self, *args, **options):
        file_path = options["file"]

        try:
            with open(file_path, "r", encoding="utf-8") as file:
                data = json.load(file)

        except FileNotFoundError:
            self.stdout.write(self.style.ERROR(f"File not found: {file_path}"))
            return

        except json.JSONDecodeError as e:
            self.stdout.write(self.style.ERROR(f"Error decoding JSON: {e}"))
            return

        created_count, skipped_count = self._load_countries(data)

        self.stdout.write(
            self.style.SUCCESS(
                f"{created_count} countries created successfully."
            )
        )
        self.stdout.write(
            self.style.WARNING(
                f"{skipped_count} countries skipped (already exist)."
            )
        )

    def _load_countries(self, data):
        created_count = 0
        skipped_count = 0

        for code, details in data.items():
            try:
                phone_code = ", ".join(map(str, details.get("phone", [])))
                languages = details.get("languages", [])
                language = languages[0] if languages else None

                _, created = Country.objects.update_or_create(
                    code=code,
                    defaults={
                        "name": details["name"],
                        "native": details.get("native"),
                        "phone_code": phone_code,
                        "continent": details["continent"],
                        "capital": details.get("capital"),
                    },
                )

                if created:
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Country '{details['name']}' created."
                        )
                    )
                else:
                    skipped_count += 1
                    self.stdout.write(
                        self.style.WARNING(
                            f"Country '{details['name']}' already exists. Skipping."
                        )
                    )

            except KeyError as e:
                self.stdout.write(
                    self.style.ERROR(f"Missing required field in data: {e}")
                )

            except IntegrityError as e:
                self.stdout.write(self.style.ERROR(f"Database error: {e}"))

        return created_count, skipped_count
