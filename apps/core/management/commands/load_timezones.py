import json
from django.core.management.base import BaseCommand
from django.db.utils import IntegrityError
from apps.core.models import Timezone


class Command(BaseCommand):
    help = "Load timezone data from timezones.json into the Timezone model"

    def add_arguments(self, parser):
        parser.add_argument(
            "--file",
            type=str,
            default="apps/core/assets/timezones.json",
            help="Path to the JSON file containing timezone data",
        )

    def handle(self, *args, **options):
        file_path = options["file"]

        try:
            with open(file_path, "r", encoding="utf-8") as file:
                data = json.load(file)
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Loaded {len(data)} timezone entries from {file_path}."
                    )
                )

            created_count, skipped_count = self._load_timezones(data)
            self.stdout.write(
                self.style.SUCCESS(
                    f"{created_count} timezones created successfully."
                )
            )

            self.stdout.write(
                self.style.WARNING(
                    f"{skipped_count} timezones skipped (already exist)."
                )
            )

        except FileNotFoundError:
            self.stdout.write(
                self.style.ERROR(
                    f"File {file_path} not found. Please check the file path."
                )
            )

        except json.JSONDecodeError:
            self.stdout.write(
                self.style.ERROR(
                    "Invalid JSON format. Please check the file content."
                )
            )

    def _load_timezones(self, data):
        created_count = 0
        skipped_count = 0

        for timezone in data:
            try:
                name = timezone["value"]
                offset = timezone["offset"]
                hours = int(offset)
                minutes = int((abs(offset) - abs(hours)) * 60)
                formatted_offset = f"UTC{hours:+03d}:{minutes:02d}"
                utc_list = timezone.get("utc", [])

                if not utc_list:
                    self.stdout.write(
                        self.style.WARNING(
                            f"Skipping timezone '{name}' as 'utc' field is empty."
                        )
                    )
                    skipped_count += 1

                    continue

                code = utc_list[0]
                _, created = Timezone.objects.update_or_create(
                    name=name,
                    defaults={
                        "code": code,
                        "offset": formatted_offset,
                        "country": None,  #
                    },
                )

                if created:
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Timezone '{name}' created with code '{code}'."
                        )
                    )
                else:
                    skipped_count += 1
                    self.stdout.write(
                        self.style.WARNING(
                            f"Timezone '{name}' already exists. Skipping."
                        )
                    )

            except KeyError as e:
                self.stdout.write(
                    self.style.ERROR(f"Missing required field in data: {e}")
                )

            except IntegrityError as e:
                self.stdout.write(self.style.ERROR(f"Database error: {e}"))

        return created_count, skipped_count
