from django.contrib import admin

from config.admin import *
from config.unfold import *
from common.config import BaseAdmin
from apps.core.models.system_setting import SystemSetting
from apps.core.models.company import Company
from apps.core.models.country import Country
from apps.core.models.contact_info import ContactInfo
from apps.core.models.state import State
from apps.core.models.address import Address
from apps.core.models.timezone import Timezone
from apps.core.models.element import Element


class SystemSettingAdmin(BaseAdmin):
    list_display = ("app_name", "app_version")
    search_fields = ("app_name", "app_version")


class CompanyAdmin(BaseAdmin):
    list_display = ("name", "email", "phone_no", "mobile_no", "country")
    search_fields = ("name", "email")


class CountryAdmin(BaseAdmin):
    list_display = (
        "name",
        "code",
        "native",
    )
    search_fields = ("name", "code")


class StateAdmin(BaseAdmin):
    list_display = ("name", "code", "country", "company")
    search_fields = ("name", "code")
    list_filter = ("country", "company")


class ContactInfoAdmin(BaseAdmin):
    list_display = (
        "name",
        "company",
        "phone_number",
        "mobile_no",
        "email",
        "type",
        "is_default",
    )
    search_fields = ("name", "phone_number", "mobile_no", "email")
    list_filter = ("type", "is_default")


class AddressAdmin(BaseAdmin):
    list_display = (
        "company",
        "city",
        "state",
        "postal_code",
        "country",
    )
    search_fields = ("city", "postal_code")


class TimezoneAdmin(BaseAdmin):
    list_display = (
        "name",
        "code",
        "offset",
    )
    search_fields = ("name", "code")


class ElementAdmin(BaseAdmin):
    list_display = ("name", "code", "group", "company")
    search_fields = ("name", "code")
    list_filter = ("group", "company")


admin.site.register(SystemSetting, SystemSettingAdmin)
admin.site.register(Company, CompanyAdmin)
admin.site.register(Country, CountryAdmin)
admin.site.register(ContactInfo, ContactInfoAdmin)
admin.site.register(State, StateAdmin)
admin.site.register(Address, AddressAdmin)
admin.site.register(Timezone, TimezoneAdmin)
admin.site.register(Element, ElementAdmin)
