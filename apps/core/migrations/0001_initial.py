# Generated by Django 5.1.4 on 2025-01-28 12:43

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Address",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_index=True,
                        verbose_name="Created at",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="Updated at"
                    ),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Deleted at",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False, verbose_name="Is deleted"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, verbose_name="Is active"
                    ),
                ),
                (
                    "address_type",
                    models.Char<PERSON>ield(
                        blank=True,
                        choices=[
                            ("ship to", "Ship To"),
                            ("bill to", "Bill To"),
                        ],
                        help_text="Type of address, e.g., 'billing' or 'shipping'.",
                        max_length=10,
                        null=True,
                        verbose_name="Address Type",
                    ),
                ),
                (
                    "city",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="City",
                    ),
                ),
                (
                    "address_line_1",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Address Line 1",
                    ),
                ),
                (
                    "address_line_2",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Address Line 2",
                    ),
                ),
                (
                    "postal_code",
                    models.CharField(
                        db_index=True,
                        max_length=20,
                        verbose_name="Postal Code",
                    ),
                ),
            ],
            options={
                "verbose_name": "Address",
                "verbose_name_plural": "Addresses",
                "db_table": "core_addresses",
            },
        ),
        migrations.CreateModel(
            name="Company",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_index=True,
                        verbose_name="Created at",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="Updated at"
                    ),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Deleted at",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False, verbose_name="Is deleted"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, verbose_name="Is active"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        db_index=True,
                        max_length=200,
                        unique=True,
                        verbose_name="Company Name",
                    ),
                ),
                (
                    "short_name",
                    models.CharField(
                        blank=True,
                        max_length=10,
                        null=True,
                        verbose_name="Short Name",
                    ),
                ),
                (
                    "slogan",
                    models.TextField(
                        blank=True, null=True, verbose_name="Company Slogan"
                    ),
                ),
                (
                    "logo",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="logo/",
                        verbose_name="Company Logo",
                    ),
                ),
                (
                    "url",
                    models.URLField(
                        blank=True, null=True, verbose_name="Website URL"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        max_length=254,
                        unique=True,
                        verbose_name="Company Email",
                    ),
                ),
                (
                    "mobile_no",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        null=True,
                        verbose_name="Mobile Number",
                    ),
                ),
                (
                    "phone_no",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        null=True,
                        verbose_name="Phone Number",
                    ),
                ),
                (
                    "country",
                    models.CharField(max_length=255, verbose_name="Country"),
                ),
                (
                    "state",
                    models.CharField(
                        max_length=255, verbose_name="State/Province"
                    ),
                ),
                (
                    "city",
                    models.CharField(max_length=255, verbose_name="City"),
                ),
                (
                    "address",
                    models.TextField(
                        blank=True, null=True, verbose_name="Street Address"
                    ),
                ),
            ],
            options={
                "verbose_name": "Company",
                "verbose_name_plural": "Companies",
                "db_table": "core_companies",
            },
        ),
        migrations.CreateModel(
            name="ContactInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_index=True,
                        verbose_name="Created at",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="Updated at"
                    ),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Deleted at",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False, verbose_name="Is deleted"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, verbose_name="Is active"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        db_index=True,
                        max_length=255,
                        verbose_name="Contact Name",
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        null=True,
                        verbose_name="Phone Number",
                    ),
                ),
                (
                    "mobile_no",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        null=True,
                        verbose_name="Mobile Number",
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True,
                        max_length=254,
                        null=True,
                        verbose_name="Email Address",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("support", "Support"),
                            ("billing", "Billing"),
                            ("feedback", "Feedback"),
                            ("general", "General"),
                            ("sales", "Sales"),
                            ("technical", "Technical"),
                            ("financial", "Financial"),
                        ],
                        max_length=20,
                        null=True,
                        verbose_name="Contact Type",
                    ),
                ),
                (
                    "is_default",
                    models.BooleanField(
                        default=False, verbose_name="Is Default Contact"
                    ),
                ),
            ],
            options={
                "verbose_name": "Contact info",
                "verbose_name_plural": "Contact infos",
                "db_table": "core_contact_infos",
            },
        ),
        migrations.CreateModel(
            name="Country",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_index=True,
                        verbose_name="Created at",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="Updated at"
                    ),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Deleted at",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False, verbose_name="Is deleted"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, verbose_name="Is active"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        db_index=True,
                        max_length=100,
                        verbose_name="Country Name",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        db_index=True,
                        max_length=100,
                        unique=True,
                        verbose_name="Country Code",
                    ),
                ),
                (
                    "native",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Native Name",
                    ),
                ),
                (
                    "phone_code",
                    models.CharField(max_length=20, verbose_name="Phone Code"),
                ),
                (
                    "capital",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Capital City",
                    ),
                ),
                (
                    "continent",
                    models.CharField(
                        blank=True,
                        max_length=2,
                        null=True,
                        verbose_name="Continent Code",
                    ),
                ),
            ],
            options={
                "verbose_name": "Country",
                "verbose_name_plural": "Countries",
                "db_table": "core_countries",
            },
        ),
        migrations.CreateModel(
            name="Element",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_index=True,
                        verbose_name="Created at",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="Updated at"
                    ),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Deleted at",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False, verbose_name="Is deleted"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, verbose_name="Is active"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        db_index=True,
                        max_length=100,
                        verbose_name="Element Name",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        db_index=True,
                        max_length=50,
                        unique=True,
                        verbose_name="Element Code",
                    ),
                ),
                (
                    "group",
                    models.CharField(
                        choices=[
                            ("language", "Language"),
                            ("gender", "Gender"),
                            ("currency", "Currency"),
                        ],
                        db_index=True,
                        max_length=10,
                        verbose_name="Element Group",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, null=True, verbose_name="Description"
                    ),
                ),
            ],
            options={
                "verbose_name": "Element",
                "verbose_name_plural": "Elements",
                "db_table": "core_elements",
            },
        ),
        migrations.CreateModel(
            name="State",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_index=True,
                        verbose_name="Created at",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="Updated at"
                    ),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Deleted at",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False, verbose_name="Is deleted"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, verbose_name="Is active"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=255, verbose_name="State Name"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        db_index=True,
                        max_length=10,
                        unique=True,
                        verbose_name="State Code",
                    ),
                ),
            ],
            options={
                "verbose_name": "State",
                "verbose_name_plural": "States",
                "db_table": "core_states",
            },
        ),
        migrations.CreateModel(
            name="SystemSetting",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_index=True,
                        verbose_name="Created at",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="Updated at"
                    ),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Deleted at",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False, verbose_name="Is deleted"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, verbose_name="Is active"
                    ),
                ),
                (
                    "app_name",
                    models.CharField(
                        default="My Application",
                        help_text="The name of the application.",
                        max_length=100,
                        verbose_name="Application Name",
                    ),
                ),
                (
                    "app_version",
                    models.CharField(
                        default="1.0.0",
                        help_text="Version of the application.",
                        max_length=20,
                        verbose_name="Application Version",
                    ),
                ),
                (
                    "default_date_format",
                    models.CharField(
                        choices=[
                            ("MM/DD/YYYY", "MM/DD/YYYY (e.g., 04/03/2024)"),
                            ("DD.MM.YYYY", "DD.MM.YYYY (e.g., 03.04.2024)"),
                            ("DD/MM/YYYY", "DD/MM/YYYY (e.g., 03/04/2024)"),
                            ("MMM/DD/YYYY", "MMM/DD/YYYY (e.g., Apr/03/2024)"),
                            ("DD.MMM.YYYY", "DD.MMM.YYYY (e.g., 03.Apr.2024)"),
                            ("DD/MMM/YYYY", "DD/MMM/YYYY (e.g., 03/Apr/2024)"),
                        ],
                        max_length=100,
                        verbose_name="Default Date Format",
                    ),
                ),
                (
                    "default_datetime_formate",
                    models.CharField(
                        choices=[
                            (
                                "MM/DD/YYYY hh:mm:ss A",
                                "MM/DD/YYYY HH:MM:SS AM/PM (e.g., 04/03/2024 08:15:30 PM)",
                            ),
                            (
                                "MM/DD/YYYY hh:mm A",
                                "MM/DD/YYYY HH:MM AM/PM (e.g., 04/03/2024 08:15 PM)",
                            ),
                            (
                                "DD.MM.YYYY HH:mm:ss",
                                "DD.MM.YYYY HH:MM:SS (24-hour format, e.g., 03.04.2024 20:15:30)",
                            ),
                            (
                                "DD.MM.YYYY HH:mm",
                                "DD.MM.YYYY HH:MM (24-hour format, e.g., 03.04.2024 20:15)",
                            ),
                            (
                                "DD.MM.YYYY hh:mm:ss A",
                                "DD.MM.YYYY HH:MM:SS AM/PM (12-hour format, e.g., 03.04.2024 08:15:30 PM)",
                            ),
                            (
                                "DD.MM.YYYY hh:mm A",
                                "DD.MM.YYYY HH:MM AM/PM (12-hour format, e.g., 03.04.2024 08:15 PM)",
                            ),
                            (
                                "DD/MM/YYYY HH:mm:ss",
                                "DD/MM/YYYY HH:MM:SS (e.g., 03/04/2024 20:15:30)",
                            ),
                            (
                                "DD/MM/YYYY HH:mm",
                                "DD/MM/YYYY HH:MM (e.g., 03/04/2024 20:15)",
                            ),
                            (
                                "MMM/DD/YYYY hh:mm:ss A",
                                "MMM/DD/YYYY HH:MM:SS AM/PM (e.g., Apr/03/2024 08:15:30 PM)",
                            ),
                            (
                                "MMM/DD/YYYY hh:mm A",
                                "MMM/DD/YYYY HH:MM AM/PM (e.g., Apr/03/2024 08:15 PM)",
                            ),
                            (
                                "DD.MMM.YYYY HH:mm:ss",
                                "DD.MMM.YYYY HH:MM:SS (24-hour format, e.g., 03.Apr.2024 20:15:30)",
                            ),
                            (
                                "DD.MMM.YYYY HH:mm",
                                "DD.MMM.YYYY HH:MM (24-hour format, e.g., 03.Apr.2024 20:15)",
                            ),
                            (
                                "DD/MMM/YYYY HH:mm:ss",
                                "DD/MMM/YYYY HH:MM:SS (e.g., 03/Apr/2024 20:15:30)",
                            ),
                            (
                                "DD/MMM/YYYY HH:mm",
                                "DD/MMM/YYYY HH:MM (e.g., 03/Apr/2024 20:15)",
                            ),
                        ],
                        max_length=100,
                        verbose_name="Default DateTime Format",
                    ),
                ),
                (
                    "time_format",
                    models.CharField(
                        choices=[
                            (
                                "hh:mm:ss A",
                                "HH:MM:SS AM/PM (e.g., 08:15:30 PM)",
                            ),
                            ("hh:mm A", "HH:MM AM/PM (e.g., 08:15 PM)"),
                            (
                                "HH:mm:ss",
                                "HH:MM:SS (24-hour format, e.g., 20:15:30)",
                            ),
                            ("HH:mm", "HH:MM (24-hour format, e.g., 20:15)"),
                        ],
                        max_length=20,
                        verbose_name="Time Format",
                    ),
                ),
                (
                    "default_currency",
                    models.CharField(
                        default="USD",
                        help_text="Default currency for transactions.",
                        max_length=10,
                        verbose_name="Default Currency",
                    ),
                ),
                (
                    "default_language",
                    models.CharField(
                        default="en",
                        help_text="Default language for the system.",
                        max_length=10,
                        verbose_name="Default Language",
                    ),
                ),
                (
                    "decimal_precision",
                    models.PositiveIntegerField(
                        default=2,
                        help_text="Number of decimal places for numeric values.",
                        verbose_name="Decimal Precision",
                    ),
                ),
                (
                    "default_ordering",
                    models.CharField(
                        choices=[("asc", "Ascending"), ("desc", "Descending")],
                        default="asc",
                        max_length=50,
                        verbose_name="Default Ordering",
                    ),
                ),
                (
                    "default_timezone",
                    models.CharField(
                        default="UTC",
                        help_text="Default timezone for the application.",
                        max_length=50,
                        verbose_name="Default Timezone",
                    ),
                ),
                (
                    "maintenance_mode_enabled",
                    models.BooleanField(
                        default=False,
                        help_text="Enable or disable maintenance mode.",
                        verbose_name="Maintenance Mode Enabled",
                    ),
                ),
                (
                    "maintenance_message",
                    models.TextField(
                        default="The system is currently under maintenance.",
                        help_text="Message displayed during maintenance.",
                        verbose_name="Maintenance Message",
                    ),
                ),
                (
                    "session_timeout",
                    models.PositiveIntegerField(
                        default=30,
                        help_text="Session timeout duration in minutes.",
                        verbose_name="Session Timeout (Minutes)",
                    ),
                ),
                (
                    "pagination_size",
                    models.PositiveIntegerField(
                        default=10,
                        help_text="Default number of items per page.",
                        verbose_name="Pagination Size",
                    ),
                ),
                (
                    "api_rate_limit",
                    models.PositiveIntegerField(
                        default=1000,
                        help_text="API rate limit (requests per hour).",
                        verbose_name="API Rate Limit",
                    ),
                ),
                (
                    "max_upload_size",
                    models.PositiveIntegerField(
                        default=5242880,
                        help_text="Maximum upload size in bytes.",
                        verbose_name="Max Upload Size (Bytes)",
                    ),
                ),
                (
                    "caching_enabled",
                    models.BooleanField(
                        default=True,
                        help_text="Enable or disable caching.",
                        verbose_name="Caching Enabled",
                    ),
                ),
                (
                    "cache_expiry_time",
                    models.PositiveIntegerField(
                        default=300,
                        help_text="Default cache expiry time in seconds.",
                        verbose_name="Cache Expiry Time (Seconds)",
                    ),
                ),
                (
                    "logging_enabled",
                    models.BooleanField(
                        default=True,
                        help_text="Enable or disable logging.",
                        verbose_name="Logging Enabled",
                    ),
                ),
                (
                    "log_level",
                    models.CharField(
                        choices=[
                            ("DEBUG", "Debug"),
                            ("INFO", "Info"),
                            ("WARNING", "Warning"),
                            ("ERROR", "Error"),
                        ],
                        default="INFO",
                        max_length=20,
                        verbose_name="Log Level",
                    ),
                ),
                (
                    "terms_of_service_url",
                    models.URLField(
                        blank=True,
                        default="",
                        help_text="URL for the terms of service.",
                        verbose_name="Terms of Service URL",
                    ),
                ),
                (
                    "privacy_policy_url",
                    models.URLField(
                        blank=True,
                        default="",
                        help_text="URL for the privacy policy.",
                        verbose_name="Privacy Policy URL",
                    ),
                ),
                (
                    "password_format",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        null=True,
                        verbose_name="Password Format",
                    ),
                ),
                (
                    "login_attempts",
                    models.IntegerField(
                        default=3, verbose_name="Allowed Login Attempts"
                    ),
                ),
            ],
            options={
                "verbose_name": "System settings",
                "verbose_name_plural": "System settings",
                "db_table": "core_system_settings",
            },
        ),
        migrations.CreateModel(
            name="Timezone",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_index=True,
                        verbose_name="Created at",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="Updated at"
                    ),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Deleted at",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False, verbose_name="Is deleted"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, verbose_name="Is active"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        db_index=True,
                        max_length=100,
                        unique=True,
                        verbose_name="Timezone Name",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        db_index=True,
                        max_length=50,
                        unique=True,
                        verbose_name="Timezone Code",
                    ),
                ),
                (
                    "offset",
                    models.CharField(max_length=50, verbose_name="UTC Offset"),
                ),
            ],
            options={
                "verbose_name": "Timezone",
                "verbose_name_plural": "Timezones",
                "db_table": "core_timezones",
            },
        ),
    ]
