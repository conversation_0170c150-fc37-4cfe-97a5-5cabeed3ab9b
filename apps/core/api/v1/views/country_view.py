from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView

from apps.core.services import CountryService
from apps.core.api.v1.serializers import CountrySerializer

logger = Logger(__name__)


class CountryListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.country_service = CountryService()

    def get(self, request, company_id):
        logger.info({"event": "CountryListView:get", "message": "Fetching country data", "path": request.path, "company_id": company_id})

        try:
            # Retrieve countries by the company id
            countries = self.country_service.list(company__id=company_id)

            if not countries:
                result = {
                    "message": _("No countries found for this company"),
                    "errors": {"id": f"No company found with ID {company_id}."},
                }
                logger.warning({"event": "CountryListView:get", "message": "No countries found", "company_id": company_id})
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            # Successfully retrieved country list
            result = {
                "message": _("Countries retrieved successfully"),
                "data": CountrySerializer(countries, many=True).data,
            }
            logger.info({"event": "CountryListView:get", "message": "Countries retrieved successfully", "company_id": company_id})
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CountryListView:get", "message": "Unexpected error occurred", "error": str(e), "company_id": company_id})
            raise e