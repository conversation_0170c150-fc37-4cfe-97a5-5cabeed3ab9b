from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView

from apps.core.services import ContactInfoService
from apps.core.api.v1.serializers import ContactInfoSerializer

logger = Logger(__name__)


class ContactInfoListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.contact_info_service = ContactInfoService()

    def get(self, request, company_id):
        logger.info({"event": "ContactInfoListView:get", "message": "Fetching company contact info data", "path": request.path, "company_id": company_id})

        try:
            contact_info = self.contact_info_service.get(company__id=company_id)
            serializer = ContactInfoSerializer(contact_info, many=True)

            result = {
                "message": _("Contact info retrieved successfully"),
                "data": serializer.data
            }
            logger.info({"event": "ContactInfoListView:get", "message": "Company contact info retrieved successfully", "company_id": company_id})
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ContactInfoListView:get", "message": "Unexpected error occurred", "error": str(e), "company_id": company_id})
            raise e
