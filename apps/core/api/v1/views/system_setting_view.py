from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView

from apps.core.services import SystemSettingService
from apps.core.api.v1.serializers import SystemSettingSerializer

logger = Logger(__name__)

class SystemSettingListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.system_setting_service = SystemSettingService()

    def get(self, request, company_id):
        logger.info({"event": "SystemSettingListView:get", "message": "Fetching system setting data", "path": request.path, "company_id": company_id})

        try:
            # Retrieve system setting based on company id
            system_setting = self.system_setting_service.get(company__id=company_id)

            if not system_setting:
                result = {
                    "message": _("No system setting found for this company"),
                    "errors": {"company_id": f"No system setting found for company ID {company_id}."},
                }
                logger.warning({"event": "SystemSettingListView:get", "message": "No system setting found", "company_id": company_id})
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            # Successfully retrieved system setting
            result = {
                "message": _("System setting retrieved successfully"),
                "data": SystemSettingSerializer(system_setting).data,
            }
            logger.info({"event": "SystemSettingListView:get", "message": "System setting retrieved successfully", "company_id": company_id})
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "SystemSettingListView:get", "message": "Unexpected error occurred", "error": str(e), "company_id": company_id})
            raise e
