from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView

from apps.core.services import TimezoneService
from apps.core.api.v1.serializers import TimezoneSerializer

logger = Logger(__name__)

class TimezoneListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.timezone_service = TimezoneService()

    def get(self, request, company_id):
        logger.info({"event": "TimezoneListView:get", "message": "Fetching timezone data", "path": request.path, "company_id": company_id})

        try:
            # Retrieve timezones based on company id
            timezones = self.timezone_service.list(company__id=company_id)

            if not timezones.exists():
                result = {
                    "message": _("No timezones found for this company"),
                    "errors": {"company_id": f"No timezones found for company ID {company_id}."},
                }
                logger.warning({"event": "TimezoneListView:get", "message": "No timezones found", "company_id": company_id})
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            # Successfully retrieved timezone list
            result = {
                "message": _("Timezones retrieved successfully"),
                "data": TimezoneSerializer(timezones, many=True).data,
            }
            logger.info({"event": "TimezoneListView:get", "message": "Timezones retrieved successfully", "company_id": company_id})
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TimezoneListView:get", "message": "Unexpected error occurred", "error": str(e), "company_id": company_id})
            raise e
