from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView

from apps.core.services import StateService
from apps.core.api.v1.serializers import StateSerializer

logger = Logger(__name__)


class StateListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_service = StateService()

    def get(self, request, company_id):
        logger.info({"event": "StateListView:get", "message": "Fetching state data", "path": request.path, "company_id": company_id})

        try:
            # Retrieve states based on company id
            states = self.state_service.list(company__id=company_id)

            if not states:  # Check if the queryset is empty
                result = {
                    "message": _("No states found for this company"),
                    "errors": {"company_id": f"No states found for company ID {company_id}."},
                }
                logger.warning({"event": "StateListView:get", "message": "No states found", "company_id": company_id})
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            # Successfully retrieved state list
            result = {
                "message": _("States retrieved successfully"),
                "data": StateSerializer(states, many=True).data,
            }
            logger.info({"event": "StateListView:get", "message": "States retrieved successfully", "company_id": company_id})
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "StateListView:get", "message": "Unexpected error occurred", "error": str(e), "company_id": company_id})
            raise e