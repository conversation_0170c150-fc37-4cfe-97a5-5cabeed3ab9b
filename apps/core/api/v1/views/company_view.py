from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView

from apps.core.services.company_service import CompanyService
from apps.core.api.v1.serializers import CompanySerializer

logger = Logger(__name__)


class CompanyListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.company_service = CompanyService()

    def get(self, request, id):
        logger.info({"event": "CompanyListView:get", "message": "Fetching company data", "path": request.path, "id": id})

        try:
            company = self.company_service.get(id=id)

            # Retrieve the company by id
            if not company:
                result = {
                    "message": _("Company not found"),
                    "errors": {"id": f"No company found with ID {id}."},
                }
                logger.warning({"event": "CompanyListView:get", "message": "Company not found", "id": id})
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            # Successfully retrieved company
            result = {
                "message": _("Company retrieved successfully"),
                "data": CompanySerializer(company).data,
            }
            logger.info({"event": "CompanyListView:get", "message": "Company retrieved successfully", "id": id})
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CompanyListView:get", "message": "Unexpected error occurred", "error": str(e), "id": id})
            raise e
