from django.urls import path
from apps.core.api.v1 import views

urlpatterns = [
    path('companies/<int:id>/', views.CompanyListView.as_view(), name='companies'),
    path('countries/<int:company_id>/', views.CountryListView.as_view(), name='countries'),
    path('states/<int:company_id>/', views.StateListView.as_view(), name='states'),
    path('timezones/<int:company_id>/', views.TimezoneListView.as_view(), name='timezones'),
    path('system-settings/<int:company_id>/', views.SystemSettingListView.as_view(), name='system-settings'),
    path('contact-infos/<int:company_id>/', views.ContactInfoListView.as_view(), name='contacts'),
]
