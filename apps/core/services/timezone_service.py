from common.repository import RepositoryError
from common.service import BaseService, ServiceError
from apps.core.repositories import TimezoneRepository


class TimezoneService(BaseService):
    def __init__(self):
        super().__init__(TimezoneRepository())
    
    def get_default_timezone(self):
        try:
            return self.repository.get()
        except RepositoryError as e:
            raise ServiceError(f"Service error during get operation: {e}")
