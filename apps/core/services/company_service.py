from common.logger import Logger
from common.repository import RepositoryError
from common.service import BaseService, ServiceError
from apps.core.repositories import CompanyRepository

logger = Logger(__name__)

class CompanyService(BaseService):
    def __init__(self):
        super().__init__(CompanyRepository())
        
    def get_default_company(self):
        try:
            return self.repository.get()
        except RepositoryError as e:
            raise ServiceError(f"Service error during get operation: {e}")
