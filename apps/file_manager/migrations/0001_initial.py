# Generated by Django 5.1.5 on 2025-02-27 10:03

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FileUpload',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('file', models.FileField(upload_to='uploads/%Y/%m/%d/', verbose_name='File')),
                ('file_type', models.CharField(choices=[('pdf', 'Pdf'), ('jpg', 'Jpeg'), ('png', 'Png'), ('doc', 'Doc'), ('other', 'Other')], db_index=True, max_length=50, verbose_name='File Type')),
                ('original_name', models.CharField(db_index=True, max_length=255, verbose_name='Original Name')),
                ('size', models.BigIntegerField(verbose_name='Size')),
                ('company', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='company_file_uploads', to='core.company', verbose_name='Company')),
            ],
            options={
                'verbose_name': 'File upload',
                'verbose_name_plural': 'File uploads',
                'db_table': 'file_manager_file_uploads',
            },
        ),
    ]
