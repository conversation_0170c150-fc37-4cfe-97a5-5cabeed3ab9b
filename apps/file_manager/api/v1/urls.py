from django.urls import path
from apps.file_manager.api.v1 import views


urlpatterns = [
    # File Collection
    path('files/', views.FileListView.as_view(), name='file-list'),
    path('files/upload/', views.FileUploadView.as_view(), name='file-upload'),
    path('files/search/', views.FileSearchView.as_view(), name='file-search'),

    # File Detail Operations
    path('files/<int:file_id>/', views.FileDetailView.as_view(), name='file-detail'),
    path('files/<int:file_id>/download/', views.FileDownloadView.as_view(), name='file-download'),
    path('files/<int:file_id>/', views.FileDetailView.as_view(), name='file-delete'),

    # Batch Operations
    path('files/batch/', views.BatchFileDeleteView.as_view(), name='batch-file-delete'),
    path('files/batch/download/', views.BatchFileDownloadView.as_view(), name='batch-file-download'),

    # Generated Images
    path('generated-images/<str:image_id>/download/', views.GeneratedImageDownloadView.as_view(), name='generated-image-download'),
]
