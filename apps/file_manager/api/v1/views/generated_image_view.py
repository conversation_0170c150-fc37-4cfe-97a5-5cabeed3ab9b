from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.translation import gettext_lazy as _

from apps.file_manager.services import FileService
from apps.llm_manager.services import ImageGenerationService

logger = Logger(__name__)


class GeneratedImageDownloadView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.file_service = FileService()
        self.image_gen_service = ImageGenerationService()
    
    def get(self, request, image_id):
        try:
            generated_image = self.image_gen_service.get(uuid=image_id)
            
            if not generated_image:
                result = {
                    "message": _("Generated image not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            if not generated_image.file:
                result = {
                    "message": _("Image file is not yet available. Please try again later."),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            file_obj = generated_image.file
            
            return self.file_service.download_file(file_obj.id)
            
        except Exception as e:
            logger.error({"event": "GeneratedImageDownloadView:get", 
                         "message": "Unexpected error occurred", "error": str(e)})
            raise e
