from django.conf import settings
from dxh_common.base.base_serializer import BaseModelSerializer
from dxh_libraries.rest_framework import serializers
from apps.file_manager.models import FileUpload


class FileSerializer(BaseModelSerializer):
    file_url = serializers.SerializerMethodField()
    file = serializers.FileField(required=True, write_only=True)

    class Meta:
        model = FileUpload
        fields = (
            'id', 'file', 'file_url', 'file_type',
            'original_name', 'size', 'created_at'
        )

    def get_file_url(self, obj):
        if obj.file:
            return settings.MEDIA_BASE_URL +obj.file.url
        
        return None
