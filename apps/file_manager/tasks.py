from django.conf import settings
import requests
from django.core.files.base import ContentFile
from dxh_libraries.celery import shared_task
from celery.utils.log import get_task_logger

from apps.file_manager.services import FileService
from apps.llm_manager.services import ConversationService
from apps.llm_manager.repositories import MessageRepository, ImageGenerationRepository

logger = get_task_logger(__name__)


@shared_task(bind=True, max_retries=3)
def download_generated_image_task(self, conversation_id, message_id, user_id, image_id, url):
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()

        file_type = "png"
        if url and "." in url.split("?")[0]:
            extension = url.split("?")[0].split(".")[-1].lower()
            if extension in ["png", "jpg", "jpeg", "webp", "gif"]:
                file_type = extension

        file_content = ContentFile(response.content)
        filename = f"generated_image_{image_id}.{file_type}"

        file_service = FileService()
        file_obj = file_service.save_external_file(
            user_id=user_id,
            file_content=file_content,
            filename=filename,
            file_type=file_type,
            size=len(response.content)
        )

        conversation_service = ConversationService()
        message_repository = MessageRepository()
        conversation = conversation_service.get(user_id=user_id, uuid=conversation_id)
        message_content = f"{settings.MEDIA_BASE_URL}{file_obj.file.url}"
        try:
            message = message_repository.get(uuid=message_id, conversation=conversation)
            message.content = message_content
            message.save()
        except Exception as e:
            logger.error(f"Error updating message: {str(e)}")

        image_repository = ImageGenerationRepository()
        try:
            image_repository.update_image_file(image_id, file_obj)
        except Exception as e:
            logger.error(f"Error linking file to GeneratedImage: {str(e)}")


    except requests.RequestException as e:
        logger.error(f"Retrying: Error downloading generated image: {str(e)}")
        self.retry(exc=e, countdown=10)

    except Exception as e:
        logger.error(f"Error processing generated image download: {str(e)}")
        raise e