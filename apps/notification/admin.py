from django.contrib import admin
from common.config import BaseAdmin
from apps.notification.models import (
    NotificationPreference, NotificationType, NotificationTemplate,
    Notification, EmailTemplate, NotificationLog
)


@admin.register(NotificationPreference)
class NotificationPreferenceAdmin(BaseAdmin):
    list_display = ('user', 'email', 'push', 'sms')
    search_fields = ('user__email',)


@admin.register(NotificationType)
class NotificationTypeAdmin(BaseAdmin):
    list_display = ('name', 'code', 'created_at')
    search_fields = ('name', 'code')
    ordering = ('name',)


@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(BaseAdmin):
    list_display = ('type', 'subject')
    search_fields = ('type__name', 'subject', 'body')
    ordering = ('type__name',)


@admin.register(Notification)
class NotificationAdmin(BaseAdmin):
    list_display = ('user', 'type', 'title', 'is_read', 'created_at')
    list_filter = ('type', 'is_read')
    search_fields = ('user__email', 'title', 'message')
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)


@admin.register(EmailTemplate)
class EmailTemplateAdmin(BaseAdmin):
    list_display = ('name', 'subject', 'is_active', 'created_at')
    list_filter = ('is_active',)
    search_fields = ('name', 'subject', 'content')
    readonly_fields = ('created_at',)


@admin.register(NotificationLog)
class NotificationLogAdmin(BaseAdmin):
    list_display = ('notification', 'status', 'created_at')
    list_filter = ('status',)
    search_fields = ('notification__title', 'error')
    readonly_fields = ('created_at',)
