from django.db import models
from django.utils.timezone import now
from django.template import Template, Context
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from common.models import BaseModel
from common.constants import StatusChoicesNotificationLog

User = get_user_model()


class NotificationType(models.Model):
    name = models.CharField(
        max_length=100, db_index=True, verbose_name=_("Notification Type")
    )
    code = models.CharField(
        max_length=50,
        unique=True,
        db_index=True,
        verbose_name=_("Notification Code"),
    )
    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name=_("Created At")
    )

    class Meta:
        db_table = "notification_types"
        verbose_name = _("Notification type")
        verbose_name_plural = _("Notification types")
        ordering = ["-created_at"]

    def __str__(self):
        return self.name


class NotificationPreference(models.Model):
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        related_name="notification_preference",
        verbose_name=_("User"),
    )
    notification_type = models.ForeignKey(
        NotificationType, 
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="notification_type",
        verbose_name=_("Notification Types")
    )
    email = models.BooleanField(
        default=True, verbose_name=_("Receive Email Notifications")
    )
    push = models.BooleanField(
        default=True, verbose_name=_("Receive Push Notifications")
    )
    sms = models.BooleanField(
        default=False, verbose_name=_("Receive SMS Notifications")
    )
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("Company"),
    )

    class Meta:
        db_table = "notification_preferences"
        verbose_name = _("Notification preference")
        verbose_name_plural = _("Notification preferences")

    def __str__(self):
        return f"{self.user.email}'s Preferences"


class NotificationTemplate(models.Model):
    type = models.ForeignKey(
        NotificationType,
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("Notification Type"),
    )
    subject = models.CharField(max_length=255, verbose_name=_("Email Subject"))
    body = models.TextField(verbose_name=_("Email Body"))
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("Company"),
    )

    class Meta:
        db_table = "notification_templates"
        verbose_name = _("Notification template")
        verbose_name_plural = _("Notification templates")

    def __str__(self):
        return self.type.name


class Notification(models.Model):
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("User"),
    )
    type = models.ForeignKey(
        NotificationType,
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("Notification Type"),
    )
    template = models.ForeignKey(
        NotificationTemplate,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Template"),
    )
    title = models.CharField(max_length=255, verbose_name=_("Title"))
    message = models.TextField(verbose_name=_("Message"))
    is_read = models.BooleanField(default=False, verbose_name=_("Read"))
    created_at = models.DateTimeField(default=now, verbose_name=_("Created At"))
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("Company"),
    )

    class Meta:
        db_table = "notification_notifications"
        verbose_name = _("Notification")
        verbose_name_plural = _("Notifications")

    def __str__(self):
        return self.title


class EmailTemplate(BaseModel):
    name = models.CharField(
        max_length=100, db_index=True, verbose_name=_("Template Name")
    )
    code = models.CharField(
        max_length=50, unique=True, db_index=True, verbose_name=_("Template Code")
    )
    subject = models.CharField(max_length=255, verbose_name=_("Subject"))
    content = models.TextField(verbose_name=_("Content"))
    is_active = models.BooleanField(default=True, verbose_name=_("Active"))
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("Company"),
    )

    class Meta:
        db_table = "notification_email_templates"
        verbose_name = _("Email template")
        verbose_name_plural = _("Email templates")

    def __str__(self):
        return self.name

    def render_subject(self, context=None):
        template = Template(self.subject)
        context = Context(context or {})
        return template.render(context)

    def render_content(self, context=None):
        template = Template(self.content)
        context = Context(context or {})
        return template.render(context)


class NotificationLog(BaseModel):
    notification = models.ForeignKey(
        Notification,
        on_delete=models.SET_NULL,
        null=True,
        related_name="logs",
        db_index=True,
        verbose_name=_("Notification"),
    )
    status = models.CharField(
        max_length=20,
        choices=StatusChoicesNotificationLog.choices(),
        db_index=True,
        verbose_name=_("Status"),
    )
    error = models.TextField(null=True, blank=True, verbose_name=_("Error"))
    message = models.TextField(null=True, blank=True, verbose_name=_("Message"))
    metadata = models.JSONField(default=dict, verbose_name=_("Meta Data"))
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("Company"),
    )

    class Meta:
        db_table = "notification_logs"
        ordering = ["-created_at"]
        verbose_name = _("Notification log")
        verbose_name_plural = _("Notification logs")

    def __str__(self):
        return f"{self.notification.title} - {self.status}"
