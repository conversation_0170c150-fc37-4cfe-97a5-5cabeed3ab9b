from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from apps.notification.models import NotificationPreference

User = get_user_model()

class Command(BaseCommand):
    help = 'Create default notification preferences for all users'

    def handle(self, *args, **kwargs):
        users_without_preferences = User.objects.filter(
            notification_preference__isnull=True
        )
        for user in users_without_preferences:
            NotificationPreference.objects.create(user=user)
            self.stdout.write(self.style.SUCCESS(
                f'Created preferences for {user.email}'
            ))

        self.stdout.write(self.style.SUCCESS(
            'Default notification preferences created for all users.'
        ))
