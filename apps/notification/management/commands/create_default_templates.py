from django.core.management.base import BaseCommand
from apps.notification.models import EmailTemplate
from common.constants import DEFAULT_TEMPLATES


class Command(BaseCommand):
    help = 'Create default email templates'

    def handle(self, *args, **kwargs):
        for code, template in DEFAULT_TEMPLATES.items():
            EmailTemplate.objects.get_or_create(
                code=code,
                defaults={
                    'name': template['name'],
                    'subject': template['subject'],
                    'content': template['content']
                }
            )
