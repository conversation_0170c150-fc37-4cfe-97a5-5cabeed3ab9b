from django.core.management.base import BaseCommand
from apps.notification.models import NotificationType


class Command(BaseCommand):
    help = 'Add default notification types'

    def handle(self, *args, **kwargs):
        types = [
            {"name": "Email Verification", "code": "email_verification"},
            {"name": "Password Reset", "code": "password_reset"},
            {"name": "Account Creation", "code": "account_creation"},
        ]

        for t in types:
            NotificationType.objects.get_or_create(
                name=t["name"], code=t["code"]
            )

        self.stdout.write(self.style.SUCCESS(
            'Notification types added successfully.'
        ))
