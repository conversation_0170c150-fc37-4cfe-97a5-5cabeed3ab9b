from django.core.management.base import BaseCommand
from apps.notification.models import NotificationType, NotificationTemplate


class Command(BaseCommand):
    help = 'Add default notification templates'

    def handle(self, *args, **kwargs):
        templates = [
            {
                "type_code": "email_verification",
                "subject": "Verify Your Email",
                "body": "Your verification code is {{ otp }}.",
            },
            {
                "type_code": "password_reset",
                "subject": "Reset Your Password",
                "body": "Click the link to reset your password.",
            },
            {
                "type_code": "account_creation",
                "subject": "Welcome to the Platform",
                "body": "Your account has been successfully created.",
            },
        ]

        for t in templates:
            try:
                notification_type = NotificationType.objects.get(
                    code=t["type_code"]
                )
                NotificationTemplate.objects.get_or_create(
                    type=notification_type,
                    subject=t["subject"],
                    body=t["body"],
                )
            except NotificationType.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(
                        f"NotificationType '{t['type_code']}' not found."
                    )
                )

        self.stdout.write(
            self.style.SUCCESS('Notification templates added successfully.')
        )
