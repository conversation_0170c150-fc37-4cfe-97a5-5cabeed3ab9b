from celery import shared_task
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from .models import Notification


@shared_task
def send_notification_task(notification_id):
    """ Background task to send notifications """
    notification = Notification.objects.get(id=notification_id)
    channel_layer = get_channel_layer()
    group_name = f'user_{notification.user.pk}'

    # Send the notification through WebSocket
    async_to_sync(channel_layer.group_send)(
        group_name,
        {
            'type': 'send_notification',
            'message': notification.message,
        }
    )
