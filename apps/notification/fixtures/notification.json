[{"model": "notification.notificationtype", "pk": 1, "fields": {"name": "System Update", "code": "SYSTEM_UPDATE", "created_at": "2024-01-30T08:00:00Z"}}, {"model": "notification.notificationtype", "pk": 2, "fields": {"name": "Account Security", "code": "ACCOUNT_SECURITY", "created_at": "2024-01-30T08:00:00Z"}}, {"model": "notification.notificationpreference", "pk": 1, "fields": {"user": 1, "notification_type": 1, "email": true, "push": true, "sms": false, "company": 1}}, {"model": "notification.notificationtemplate", "pk": 1, "fields": {"type": 1, "subject": "System Update: Important Announcement", "body": "Dear User,\n\nA new system update is available.", "company": 1}}, {"model": "notification.notification", "pk": 1, "fields": {"user": 1, "type": 1, "template": 1, "title": "New Feature Release", "message": "We've released exciting new features!", "is_read": false, "company": 1}}, {"model": "notification.emailtemplate", "pk": 1, "fields": {"name": "Welcome Email", "code": "WELCOME_EMAIL", "subject": "Welcome to Our Platform", "content": "Dear User,\n\nWelcome to our platform!", "company": 1, "created_at": "2025-01-31T08:50:35.424272Z", "updated_at": "2025-01-31T08:50:35.424272Z"}}, {"model": "notification.notificationlog", "pk": 1, "fields": {"notification": 1, "status": "SENT", "error": null, "message": "Notification sent successfully", "metadata": {"delivery_method": "email", "sent_at": "2024-01-30T08:00:00Z"}, "company": 1, "created_at": "2025-01-31T08:50:35.424272Z", "updated_at": "2025-01-31T08:50:35.424272Z"}}]