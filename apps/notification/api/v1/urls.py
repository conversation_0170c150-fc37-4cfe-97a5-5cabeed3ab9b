from django.urls import path
from . import views

urlpatterns = [
    # Notifications
    path('notifications/', views.NotificationView.as_view(), name='notification-list'),
    path('notifications/<int:notification_id>/', views.NotificationDetailView.as_view(), name='notification-detail'),

    # Notification Preferences
    path('notifications/preferences/', views.NotificationPreferenceView.as_view(), name='notification-preferences'),

    # Email Templates
    path('notifications/templates/', views.EmailTemplateListView.as_view(), name='email-template-list'),
    path('notifications/templates/<int:template_id>/', views.EmailTemplateDetailView.as_view(), name='email-template-detail'),
    path('notifications/templates/preview/', views.EmailTemplatePreviewView.as_view(), name='email-template-preview'),
]
