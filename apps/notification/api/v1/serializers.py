import re
from rest_framework import serializers
from common.serializers import BaseModelSerializer
from apps.notification.models import (
    NotificationType, NotificationTemplate, Notification, EmailTemplate,
    NotificationLog, NotificationPreference
)


class NotificationTypeSerializer(BaseModelSerializer):
    class Meta:
        model = NotificationType
        fields = ('id', 'name', 'code', 'channels', 'created_at', 'updated_at')
        read_only_fields = ('id', 'created_at', 'updated_at')


class NotificationTemplateSerializer(BaseModelSerializer):
    class Meta:
        model = NotificationTemplate
        fields = (
            'id', 'type', 'name', 'subject',
            'body', 'created_at', 'updated_at'
        )
        read_only_fields = ('id', 'created_at', 'updated_at')


class NotificationSerializer(BaseModelSerializer):
    class Meta:
        model = Notification
        fields = (
            'id', 'user', 'type', 'template', 'title',
            'message', 'is_read', 'created_at'
        )
        read_only_fields = ('id', 'created_at')


class EmailTemplateSerializer(BaseModelSerializer):
    variables = serializers.SerializerMethodField()

    class Meta:
        model = EmailTemplate
        fields = [
            'id',
            'name',
            'code',
            'subject',
            'content',
            'is_active',
            'variables'
        ]

    def get_variables(self, obj):
        """
        Extract variables from the content field using regex.
        Variables are placeholders wrapped
        in double curly braces (e.g., {{ otp }}).
        """
        return re.findall(r'{{\s*(\w+)\s*}}', obj.content)


class NotificationLogSerializer(BaseModelSerializer):
    class Meta:
        model = NotificationLog
        fields = (
            'id', 'notification', 'status', 'error',
            'message', 'metadata', 'created_at'
        )
        read_only_fields = ('id', 'created_at')


class NotificationPreferenceSerializer(BaseModelSerializer):
    class Meta:
        model = NotificationPreference
        fields = ['user', 'notification_type', 'email', 'push', 'sms']
