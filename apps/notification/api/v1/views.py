from rest_framework.response import Response
from rest_framework.permissions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IsA<PERSON><PERSON><PERSON><PERSON>
from django.shortcuts import get_object_or_404

from common.api_view import Base<PERSON>piView
from apps.notification.models import EmailTemplate
from apps.notification.api.v1.serializers import EmailTemplateSerializer
from apps.notification.services import NotificationService

from common.logger import Logger
logger = Logger(__name__)


# Notification Preferences
class NotificationPreferenceView(BaseApiView):
    def get(self, request):
        preferences = NotificationService.get_preferences(request.user)
        return Response(
            message="Notification preferences retrieved",
            data=preferences
        )

    def put(self, request):
        updated_preferences = NotificationService.update_preferences(
            request.user, request.data
        )
        return Response(
            message="Notification preferences updated",
            data=updated_preferences
        )


# Notifications List and Create
class NotificationView(BaseApiView):
    def get(self, request):
        notifications = NotificationService.get_user_notifications(
            request.user
        )
        # Ensure data is always a list, even if it's empty
        return Response(
            message="Notifications retrieved successfully",
            data=notifications if notifications else []
        )

    def post(self, request):
        type_code = request.data.get("type_code")
        context = request.data.get("context", {})

        if not type_code:
            return Response(
                message="Type code is required"
            )

        notification, error = NotificationService.create_notification(
            user=request.user,
            type_code=type_code,
            context=context
        )

        if error:
            return Response(
                message="Notification creation failed",
                errors=[error]
            )

        return Response(
            message="Notification created successfully",
            data={
                "id": notification.id,
                "title": notification.title,
                "message": notification.message,
                "created_at": notification.created_at.strftime(
                    '%Y-%m-%d %H:%M:%S'
                ),
            },
            status=201
        )


# Notification Detail (Mark as Read, Delete)
class NotificationDetailView(BaseApiView):
    def put(self, request, notification_id):
        success = NotificationService.mark_as_read(
            notification_id=notification_id,
            user=request.user  # Pass the user object here
        )

        if success:
            return Response(
                message="Notification marked as read"
            )
        else:
            return Response(
                message="Notification not found",
                status=404
            )

    def delete(self, request, notification_id):
        success = NotificationService.delete_notifications(
            [notification_id], request.user  # ✅ Pass as positional arguments
        )

        if success:
            return Response(
                message="Notification deleted successfully"
            )

        return Response(
            message="Failed to delete notification",
            status=404
        )


# Email Templates List and Create
class EmailTemplateListView(BaseApiView):
    permission_classes = [IsAdminUser]

    def get(self, request):
        templates = EmailTemplate.objects.all()
        serializer = EmailTemplateSerializer(templates, many=True)
        return Response(
            message="Templates retrieved successfully",
            data=serializer.data
        )

    def post(self, request):
        serializer = EmailTemplateSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                message="Template created successfully",
                data=serializer.data
            )
        return Response(
            message="Validation error",
            errors=serializer.errors
        )


# Email Template Detail
class EmailTemplateDetailView(BaseApiView):
    permission_classes = [IsAdminUser]

    def get(self, request, template_id):
        template = get_object_or_404(EmailTemplate, id=template_id)
        serializer = EmailTemplateSerializer(template)
        return Response(
            message="Template retrieved successfully",
            data=serializer.data
        )


# Email Template Preview
class EmailTemplatePreviewView(BaseApiView):
    permission_classes = [IsAdminUser]

    def post(self, request):
        preview, error = NotificationService.preview_email_template(
            template_id=request.data.get('template_id'),
            context=request.data.get('context', {})
        )
        if error:
            return Response(
                message="Error generating preview",
                errors=error
            )
        return Response(
            message="Preview generated successfully",
            data=preview
        )
