# Deploy to aws
name: Deploy to AWS

# Controls when the action will run.
on:
  workflow_dispatch: # Allows you to run this workflow manually
  push:
    branches: [main]

env:
  SERVER_IP: **********
  USERNAME: ec2-user
  WORK_DIR: /home/<USER>/app

  # Docker settings
  GID: 1001
  UID: 1001
  DOCKER_IMAGE_NAME: "dxhltd/pioo-backend"
  DOCKER_IMAGE_TAG: ${{ github.sha }}
  DOCKER_USERNAME: devxhublimited
  DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}

  # Django settings
  DJANGO_ENV: production
  DJANGO_SETTINGS_MODULE: "config.settings.production"
  DJANGO_SECRET_KEY: "akr2icmg1n8%z^3fe3c+)5d0(t^cy-2_25rrl35a7@!scna^1#"
  DJANGO_DEBUG: False
  DJANGO_ALLOWED_HOSTS: "app.pioo.ai,dev.app.pioo.ai,pioo.ai,dev.pioo.ai,localhost,127.0.0.1,0.0.0.0"
  DJANGO_TRUSTED_CORS_ORIGINS: "https://pioo.ai,https://dev.pioo.ai,https://app.pioo.ai,https://dev.app.pioo.ai,http://localhost:8000,http://localhost:3000"
  DJANGO_CSRF_TRUSTED_ORIGINS: "https://pioo.ai,https://dev.pioo.ai,https://app.pioo.ai,https://dev.app.pioo.ai,http://localhost:8000,http://localhost:3000"
  DJANGO_DEFAULT_FILE_STORAGE: s3

  # Database settings
  DB_NAME: pioo
  DB_USER: pioo
  DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
  DATABASE_URL: "postgres://pioo:${{ secrets.DB_PASSWORD }}@postgres:5432/pioo"

  # AWS Settings
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_DEFAULT_REGION: ap-south-1

  # AWS S3 Settings
  AWS_STORAGE_BUCKET_NAME: pioo
  AWS_S3_REGION_NAME: ap-south-1
  AWS_S3_CUSTOM_DOMAIN: cdn.pioo.ai

  # Sentry Settings
  SENTRY_ENV: production
  SENTRY_DSN: https://<EMAIL>/4508760783192064

  # Email Settings
  DJANGO_DEFAULT_FROM_EMAIL: "Pioo AI <<EMAIL>>"
  DJANGO_DEFAULT_EMAIL_SERVICE: amazon_ses
  AWS_SES_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SES_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_SES_REGION_NAME: us-east-1

  # Social Auth Settings
  SOCIAL_AUTH_GOOGLE_OAUTH2_KEY: 571139434979-cgg2clr48a3n8k7qbplr12eo5480dhp4.apps.googleusercontent.com
  SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET: 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
  SOCIAL_AUTH_FACEBOOK_KEY: 
  SOCIAL_AUTH_FACEBOOK_SECRET: 
  GOOGLE_CALLBACK_URL: https://pioo.ai/oauth/google/callback
  FACEBOOK_CALLBACK_URL: https://pioo.ai/oauth/facebook/callback

  # Valkey and Celery Settings
  VALKEY_HOST: valkey
  VALKEY_PORT: 6379
  VALKEY_URL: valkey://valkey:6379/0

  CELERY_BROKER_URL: redis://valkey:6379/0
  CELERY_RESULT_BACKEND: redis://valkey:6379/0

  # AI Settings
  OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

  FRONTEND_BASE_URL: https://pioo.ai

  RECAPTCHA_ENABLE: True
  RECAPTCHA_BASE_URL: https://recaptchaenterprise.googleapis.com/v1/projects
  RECAPTCHA_PROJECT_ID: pioo-ai
  RECAPTCHA_SITE_KEY: 6LfHOtcqAAAAAPMh0TySxVHU08UDCcGDvzV3hMBb
  GOOGLE_RECAPTCHA_API_KEY: AIzaSyBRj6qE9CrVqwNBWftTAqWNWpyTCGAYBFI

  # Rate limit
  ANONIMUS_API_RATE_LIMIT: 5/minute
  AUTHENTICATED_API_RATE_LIMIT: 1000/minute

  # Customer Support Settings
  CUSTOMER_SUPPORT_EMAIL: <EMAIL>

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  build:
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_KEY }}
          known_hosts: ${{ env.SERVER_IP }}

      - name: Adding Known hosts
        run: ssh-keyscan -H ${{ env.SERVER_IP }} >> ~/.ssh/known_hosts

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ env.DOCKER_USERNAME }}
          password: ${{ env.DOCKER_PASSWORD }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Create a .env file
        run: |
          cat > .env <<EOF
          DOCKER_IMAGE_TAG=${{ env.DOCKER_IMAGE_TAG }}
          DJANGO_ENV=${{ env.DJANGO_ENV }}
          DJANGO_SETTINGS_MODULE=${{ env.DJANGO_SETTINGS_MODULE }}
          DJANGO_SECRET_KEY=${{ env.DJANGO_SECRET_KEY }}
          DJANGO_DEBUG=${{ env.DJANGO_DEBUG }}
          DJANGO_ALLOWED_HOSTS=${{ env.DJANGO_ALLOWED_HOSTS }}
          DJANGO_TRUSTED_CORS_ORIGINS=${{ env.DJANGO_TRUSTED_CORS_ORIGINS }}
          DJANGO_CSRF_TRUSTED_ORIGINS=${{ env.DJANGO_CSRF_TRUSTED_ORIGINS }}
          DJANGO_DEFAULT_FILE_STORAGE=${{ env.DJANGO_DEFAULT_FILE_STORAGE }}
          DB_NAME=${{ env.DB_NAME }}
          DB_USER=${{ env.DB_USER }}
          DB_PASSWORD=${{ env.DB_PASSWORD }}
          DATABASE_URL=${{ env.DATABASE_URL }}
          DJANGO_DEFAULT_EMAIL_SERVICE=${{ env.DJANGO_DEFAULT_EMAIL_SERVICE }}
          DJANGO_DEFAULT_FROM_EMAIL=${{ env.DJANGO_DEFAULT_FROM_EMAIL }}
          AWS_SES_REGION_NAME=${{ env.AWS_SES_REGION_NAME }}
          AWS_SES_ACCESS_KEY_ID=${{ env.AWS_SES_ACCESS_KEY_ID }}
          AWS_SES_SECRET_ACCESS_KEY=${{ env.AWS_SES_SECRET_ACCESS_KEY }}
          AWS_ACCESS_KEY_ID=${{ env.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY=${{ env.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION=${{ env.AWS_DEFAULT_REGION }}
          AWS_STORAGE_BUCKET_NAME=${{ env.AWS_STORAGE_BUCKET_NAME }}
          AWS_S3_REGION_NAME=${{ env.AWS_S3_REGION_NAME }}
          AWS_S3_CUSTOM_DOMAIN=${{ env.AWS_S3_CUSTOM_DOMAIN }}
          SENTRY_ENV=${{ env.SENTRY_ENV }}
          SENTRY_DSN=${{ env.SENTRY_DSN }}
          GOOGLE_RECAPTCHA_API_KEY=${{ env.GOOGLE_RECAPTCHA_API_KEY }}
          RECAPTCHA_ENABLE=${{ env.RECAPTCHA_ENABLE }}
          RECAPTCHA_BASE_URL=${{ env.RECAPTCHA_BASE_URL }}
          RECAPTCHA_PROJECT_ID=${{ env.RECAPTCHA_PROJECT_ID }}
          RECAPTCHA_SITE_KEY=${{ env.RECAPTCHA_SITE_KEY }}
          SOCIAL_AUTH_GOOGLE_OAUTH2_KEY=${{ env.SOCIAL_AUTH_GOOGLE_OAUTH2_KEY }}
          SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET=${{ env.SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET }}
          SOCIAL_AUTH_FACEBOOK_KEY=${{ env.SOCIAL_AUTH_FACEBOOK_KEY }}
          SOCIAL_AUTH_FACEBOOK_SECRET=${{ env.SOCIAL_AUTH_FACEBOOK_SECRET }}
          GOOGLE_CALLBACK_URL=${{ env.GOOGLE_CALLBACK_URL }}
          FACEBOOK_CALLBACK_URL=${{ env.FACEBOOK_CALLBACK_URL }}
          VALKEY_HOST=${{ env.VALKEY_HOST }}
          VALKEY_PORT=${{ env.VALKEY_PORT }}
          VALKEY_URL=${{ env.VALKEY_URL }}
          CELERY_BROKER_URL=${{ env.CELERY_BROKER_URL }}
          CELERY_RESULT_BACKEND=${{ env.CELERY_RESULT_BACKEND }}
          OPENAI_API_KEY=${{ env.OPENAI_API_KEY }}
          FRONTEND_BASE_URL=${{ env.FRONTEND_BASE_URL }}
          ANONIMUS_API_RATE_LIMIT=${{ env.ANONIMUS_API_RATE_LIMIT }}
          AUTHENTICATED_API_RATE_LIMIT=${{ env.AUTHENTICATED_API_RATE_LIMIT }}
          CUSTOMER_SUPPORT_EMAIL=${{ env.CUSTOMER_SUPPORT_EMAIL }}
          EOF

      - name: Rsync app files
        run: |
          rsync -avz --filter="merge rsync.txt" ./ ${{ env.USERNAME }}@${{ env.SERVER_IP }}:${{ env.WORK_DIR }}

      - name: Build & Push Docker image
        uses: docker/build-push-action@v6
        with:
          file: infra/docker/Dockerfile.prod
          context: .
          push: true
          platforms: linux/arm64
          provenance: true
          sbom: true
          cache-from: type=registry,ref=${{ env.DOCKER_IMAGE_NAME }}:cache
          cache-to: type=registry,ref=${{ env.DOCKER_IMAGE_NAME }}:cache,mode=max
          tags: ${{ env.DOCKER_IMAGE_NAME }}:${{ env.DOCKER_IMAGE_TAG }}

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Execute remote commands
        uses: appleboy/ssh-action@v1.2.0
        with:
          host: ${{ env.SERVER_IP }}
          username: ${{ env.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd ${{ env.WORK_DIR }}
            echo "Starting deployment..."
            set -e
            echo "${{ env.DOCKER_PASSWORD }}" | docker login -u ${{ env.DOCKER_USERNAME }} --password-stdin
            make prod || { echo "Deployment failed! Rolling back..."; exit 1; }
