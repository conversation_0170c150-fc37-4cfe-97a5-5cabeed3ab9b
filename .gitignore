# Current Project Ignores
.ruff_cache/

# Operating System files
*.DS_Store

# Python compiled files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Build and Packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Testing and Coverage
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translation files
*.mo
*.pot

# Django specific
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Static and Media files
staticfiles/
media/

# Flask specific
instance/
.webassets-cache

# Scrapy
.scrapy

# Documentation builds
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Virtual environments
.env
.env.production
.env.development
.env.test
.env.local
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Pyenv
.python-version

# Pipenv
# Uncomment the line below if you do not wish to include Pipfile.lock in version control
# Pipfile.lock

# Poetry
# Uncomment the line below if you do not wish to include poetry.lock in version control
# poetry.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid
celerybeat-schedule-shm
celerybeat-schedule-wal

# SageMath
*.sage.py

# IDEs and editors
.idea/
.vscode/

# Type checking tools
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
.pytype/

# Cython debug symbols
cython_debug/