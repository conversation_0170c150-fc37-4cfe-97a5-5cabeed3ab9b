import os
import boto3
from botocore.exceptions import NoCredentialsError
from dotenv import load_dotenv

def should_use_s3(env_path='.env'):
    """
    Load .env file and check if DJANGO_DEFAULT_FILE_STORAGE is set to 's3'.
    """
    load_dotenv(dotenv_path=env_path)
    storage_backend = os.getenv('DJANGO_DEFAULT_FILE_STORAGE')
    return storage_backend == 's3'

def upload_folder_to_s3(local_folder, bucket_name, s3_folder=''):
    """
    Upload all files from a local folder to a given S3 bucket.
    """
    s3_client = boto3.client('s3')

    for root, dirs, files in os.walk(local_folder):
        for file in files:
            local_path = os.path.join(root, file)
            relative_path = os.path.relpath(local_path, local_folder)
            s3_path = os.path.join(s3_folder, relative_path).replace("\\", "/")  # Windows fix

            try:
                print(f"Uploading {local_path} to s3://{bucket_name}/{s3_path}")
                s3_client.upload_file(local_path, bucket_name, s3_path)
            except NoCredentialsError:
                print("AWS credentials not found. Please configure them.")
                return False
    return True
