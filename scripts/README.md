# 📦 Pioo Backend Scripts

This project contains utility scripts for managing file uploads to AWS S3, using environment-based configuration.

---

## 📂 Project Structure

```
pioo-backend/
├── scripts/
│   ├── __init__.py
│   ├── s3_utils.py
│   └── upload_static.py
├── .env
├── README.md
```

- **scripts/s3_utils.py** — Common functions for S3 uploads and environment validation.
- **scripts/upload_static.py** — <PERSON><PERSON><PERSON> to upload a local folder to AWS S3 based on environment settings.
- **.env** — Environment configuration file (must include `DJANGO_DEFAULT_FILE_STORAGE`).

---

## ⚙️ Requirements

Install the dependencies:

```bash
pip install boto3 python-dotenv
```

---

## 🏗 Environment Setup

Create a `.env` file in the project root with the following variable:

```env
DJANGO_DEFAULT_FILE_STORAGE=s3
```

If this value is **not** set to `s3`, the upload script will **skip uploading** automatically.

Make sure your AWS credentials are also properly configured via:

- `aws configure`, or
- Environment variables: `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`, etc.

---

## 🚀 Running the Scripts

Always run scripts from the project root using the `-m` flag:

```bash
python -m scripts.upload_static
```

✅ This ensures Python correctly resolves the `scripts` package.

If `DJANGO_DEFAULT_FILE_STORAGE` is set to `s3`, it will upload your local folder to the specified S3 bucket.

---

## 🧹 Best Practices

- Always run with `python -m scripts.<script_name>`.
- Keep `.env` file at the **project root**.
- Add `__init__.py` inside the `scripts/` folder to make it a valid Python package.

---

## 🛠 Example Upload Script

Inside `scripts/upload_static.py`, you can configure:

```python
LOCAL_FOLDER = './static'
BUCKET_NAME = 'pioo'
S3_FOLDER = 'static'
```
