import os
from dotenv import load_dotenv

from scripts.s3_utils import should_use_s3, upload_folder_to_s3

def main():
    if not should_use_s3():
        print("Skipping upload: DJANGO_DEFAULT_FILE_STORAGE is not set to 's3'.")
        return

    load_dotenv(dotenv_path='.env')
    LOCAL_FOLDER = './static'  # Local folder to upload
    BUCKET_NAME = os.getenv('AWS_STORAGE_BUCKET_NAME')  # S3 bucket name
    S3_FOLDER = 'static'  # Optional

    success = upload_folder_to_s3(LOCAL_FOLDER, BUCKET_NAME, S3_FOLDER)
    if success:
        print("Upload completed successfully.")
    else:
        print("Upload failed.")

if __name__ == "__main__":
    main()
