import os
import sys

def create_project_structure(project_name, modules):
    BASE_DIR = os.path.join("packages", project_name)
    SUBDIRS = ["tests"] + modules
    TEST_FILES = {"tests/__init__.py": ""}

    for module in modules:
        TEST_FILES[f"tests/test_{module}.py"] = f'''import unittest

class Test{module.capitalize()}Integration(unittest.TestCase):
    def test_sample(self):
        self.assertTrue(True)
'''

    FILES = {
        "__init__.py": "",
        "__version__.py": '__version__ = "1.0.0"\n',
        "README.md": f"# {project_name}\n\nA Django-based package for various integrations.",
        "LICENSE": "MIT License",
        "requirements.txt": "django\n",
        "pyproject.toml": '''[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"''',
        "setup.py": f'''import os
from setuptools import setup, find_packages

BASE_DIR = os.path.abspath(os.path.dirname(__file__))

def read_file(filepath, default=""):
    full_path = os.path.join(BASE_DIR, filepath)
    if os.path.exists(full_path):
        with open(full_path, encoding="utf-8") as f:
            return f.read().strip()
    return default

long_description = read_file("README.md", "A Django integration package.")

version = {{}}
version_file = os.path.join(BASE_DIR, "__version__.py")
if os.path.exists(version_file):
    with open(version_file) as f:
        exec(f.read(), version)
else:
    version["__version__"] = "1.0.0"

install_requires = [
    line.strip() for line in read_file("requirements.txt").splitlines() if line.strip() and not line.startswith("#")
]

setup(
    name="{project_name}",
    version=version["__version__"],
    description="A Django-based integration package.",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Devxhub Limited",
    author_email="<EMAIL>",
    url="https://github.com/devxhub/{project_name}",
    license="MIT",
    packages=find_packages(exclude=["tests"]),
    install_requires=install_requires,
    include_package_data=True,
    classifiers=[
        "Framework :: Django",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Operating System :: OS Independent",
        "License :: OSI Approved :: MIT License",
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    keywords="django integrations",
    python_requires=">=3.8",
)'''
    }

    os.makedirs(BASE_DIR, exist_ok=True)

    for subdir in SUBDIRS:
        os.makedirs(os.path.join(BASE_DIR, subdir), exist_ok=True)
        if subdir in modules:
            with open(os.path.join(BASE_DIR, subdir, "__init__.py"), "w", encoding="utf-8") as f:
                f.write("")

    for filename, content in FILES.items():
        with open(os.path.join(BASE_DIR, filename), "w", encoding="utf-8") as f:
            f.write(content)

    for filename, content in TEST_FILES.items():
        with open(os.path.join(BASE_DIR, filename), "w", encoding="utf-8") as f:
            f.write(content)

    print(f"Package '{BASE_DIR}' created successfully with modules: {', '.join(modules)}.")


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python create_package.py <project_name> [module1 module2 module3 ...]")
        sys.exit(1)

    project_name = sys.argv[1]
    modules = sys.argv[2:] if len(sys.argv) > 2 else ["module1"]

    create_project_structure(project_name, modules)
