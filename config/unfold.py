from django.contrib import admin
from django_celery_beat.models import PeriodicTask
from django_celery_beat.admin import (
    PeriodicTaskForm,
    TaskSelectWidget,
    PeriodicTaskAdmin as BasePeriodicTaskAdmin,
)
from dxh_libraries.unfold import UnfoldAdminTextInputWidget, UnfoldAdminSelectWidget
from dxh_common.admin import BaseAdmin


class UnfoldTaskSelectWidget(UnfoldAdminSelectWidget, TaskSelectWidget):
    pass


class UnfoldPeriodicTaskForm(PeriodicTaskForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["task"].widget = UnfoldAdminTextInputWidget()
        self.fields["regtask"].widget = UnfoldTaskSelectWidget()

# Celery Beat


@admin.register(PeriodicTask)
class PeriodicTaskAdmin(BasePeriodicTaskAdmin, BaseAdmin):
    form = UnfoldPeriodicTaskForm
