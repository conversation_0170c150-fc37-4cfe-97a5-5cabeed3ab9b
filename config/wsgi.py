"""
WSGI config for the config project.

This file exposes the WSGI callable as a module-level variable named ``application``.

For more information, visit:
https://docs.djangoproject.com/en/4.0/howto/deployment/wsgi/
"""

from django.core.wsgi import get_wsgi_application
from config.django_settings_module import setup_environment

# Set the default settings module for the Django project
setup_environment()

# Initialize the WSGI application
application = get_wsgi_application()
