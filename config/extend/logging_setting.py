
import logging
import logging.config
from config.env import env

ENV = env("DJANGO_ENV", default="development")


LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "filters": {
        "correlation_id": {
            "()": "django_guid.log_filters.CorrelationId",
        },
    },
    "formatters": {
        "standard": {
            "format": "%(asctime)s [%(levelname)s] [%(correlation_id)s] %(name)s: %(message)s",
        },
        "json": {
            "format": '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "correlation_id": "%(correlation_id)s", "logger": "%(name)s", "message": "%(message)s"}',  # noqa: E501
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG" if ENV == "development" else "WARNING",
            "class": "logging.StreamHandler",
            "formatter": "standard" if ENV == "development" else "json",
            "filters": ["correlation_id"],
        },
        "file": {
            "level": "DEBUG" if ENV == "development" else "INFO",
            "class": "logging.FileHandler",
            "filename": env("LOG_FILE", "logs/application.log"),
            "formatter": "json",
            "filters": ["correlation_id"],
        },
    },
    "loggers": {
        "django": {
            "handlers": ["console", "file"],
            "level": "DEBUG" if ENV == "development" else "INFO",
            "propagate": True,
        },
        "app_logger": {
            "handlers": ["console", "file"],
            "level": "DEBUG" if ENV == "development" else "INFO",
            "propagate": False,
        },
        "django_guid": {
            "handlers": ["console"],
            "level": "WARNING",
            "propagate": False,
        },
    },
}

logging.config.dictConfig(LOGGING)
logger = logging.getLogger("app_logger")
