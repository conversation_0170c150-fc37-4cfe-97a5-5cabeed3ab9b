from __future__ import absolute_import, unicode_literals

from celery import Celery
from config.django_settings_module import setup_environment
from config.celery_config import BEAT_SCHEDULE


# Set default Django settings for Celery
setup_environment()

# Initialize the Celery app
app = Celery("config")

# Load configuration from Django settings with CELERY namespace
app.config_from_object("django.conf:settings", namespace="CELERY")

# Enable retry on broker connection startup (for Celery 6.0+)
app.conf.broker_connection_retry_on_startup = True

# Auto-discover tasks in installed apps
app.autodiscover_tasks()

# Set the Celery beat schedule
app.conf.beat_schedule = BEAT_SCHEDULE
