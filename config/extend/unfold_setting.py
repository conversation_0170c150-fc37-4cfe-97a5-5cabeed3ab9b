from django.templatetags.static import static
from django.urls import reverse_lazy
from dxh_libraries.translation import gettext_lazy as _
from config.env import env
from django.conf import settings

DJANGO_ENV = env.str("DJANGO_ENV", default="development")
DJANGO_ADMIN_URL = env("DJANGO_ADMIN_URL", default="admin/")


UNFOLD_CONFIG = {
    "SITE_TITLE": "PiooAI - The Back Office",
    "SITE_HEADER": "PiooAI Back Office",
    "SITE_URL": DJANGO_ADMIN_URL,
    "SITE_FAVICONS": [
        {
            "rel": "icon",
            "sizes": "32x32",
            "type": "image/svg+xml",
            "href": lambda request: settings.STATIC_URL+"image/favicon.svg",
        },
    ],
    "STYLES": [
        lambda request: settings.STATIC_URL+"css/unfold_admin.css",
    ],
    "SCRIPTS": [
        lambda request: settings.STATIC_URL+"js/unfold_admin.js",
    ],

    "SHOW_LANGUAGES": True,
    "ENVIRONMENT": "config.extend.unfold_setting.environment_callback",
    "SIDEBAR": {
        "show_search": True,  # Search in applications and models names
        "show_all_applications": True,  # Dropdown with all applications and models
        "navigation": [
            # Navigation items sorted alphabetically for easier access
            {
                "title": _("Accounts"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Email addresses"),
                        "icon": "email",
                        "link": reverse_lazy("admin:account_emailaddress_changelist"),
                    },
                ],
            },
            {
                "title": _("Auth Token"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Tokens"),
                        "icon": "vpn_key",
                        "link": reverse_lazy("admin:authtoken_tokenproxy_changelist"),
                    },
                ],
            },
            {
                "title": _("Core"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Addresses"),
                        "icon": "location_on",
                        "link": reverse_lazy("admin:core_address_changelist"),
                    },
                    {
                        "title": _("Companies"),
                        "icon": "business",
                        "link": reverse_lazy("admin:core_company_changelist"),
                    },
                    {
                        "title": _("Contact infos"),
                        "icon": "contact_phone",
                        "link": reverse_lazy("admin:core_contactinfo_changelist"),
                    },
                    {
                        "title": _("Countries"),
                        "icon": "public",
                        "link": reverse_lazy("admin:core_country_changelist"),
                    },
                    {
                        "title": _("Elements"),
                        "icon": "widgets",
                        "link": reverse_lazy("admin:core_element_changelist"),
                    },
                    {
                        "title": _("States"),
                        "icon": "map",
                        "link": reverse_lazy("admin:core_state_changelist"),
                    },
                    {
                        "title": _("System settings"),
                        "icon": "tune",
                        "link": reverse_lazy("admin:core_systemsetting_changelist"),
                    },
                    {
                        "title": _("Timezones"),
                        "icon": "schedule",
                        "link": reverse_lazy("admin:core_timezone_changelist"),
                    }
                ],
            },
            {
                "title": _("Easy Audit Application"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("CRUD Event"),
                        "icon": "edit",
                        "link": reverse_lazy("admin:easyaudit_crudevent_changelist"),
                    },
                    {
                        "title": _("Login Event"),
                        "icon": "login",
                        "link": reverse_lazy("admin:easyaudit_loginevent_changelist"),
                    },
                    {
                        "title": _("Request Event"),
                        "icon": "request_page",
                        "link": reverse_lazy("admin:easyaudit_requestevent_changelist"),
                    },
                ],
            },
            {
                "title": _("File manager"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("File uploads"),
                        "icon": "upload",
                        "link": reverse_lazy("admin:file_manager_fileupload_changelist"),
                    },
                ],
            },
            {
                "title": _("Group"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Groups"),
                        "icon": "group",
                        "link": reverse_lazy("admin:auth_group_changelist"),
                    },
                ],
            },
            {
                "title": _("Identity"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("MFA Setups"),
                        "icon": "security",
                        "link": reverse_lazy("admin:identity_mfasetup_changelist"),
                    },
                    {
                        "title": _("User Sessions"),
                        "icon": "schedule",
                        "link": reverse_lazy("admin:identity_usersession_changelist"),
                    },
                    {
                        "title": _("User verification codes"),
                        "icon": "verified_user",
                        "link": reverse_lazy("admin:identity_verificationcode_changelist"),
                    },
                    {
                        "title": _("User verification tokens"),
                        "icon": "verified_user",
                        "link": reverse_lazy("admin:identity_verificationtoken_changelist"),
                    },
                    {
                        "title": _("Users"),
                        "icon": "person",
                        "link": reverse_lazy("admin:identity_user_changelist"),
                    }
                ],
            },
            {
                "title": _("Integration"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Integration Providers"),
                        "icon": "extension",
                        "link": reverse_lazy("admin:integration_integrationprovider_changelist"),
                    },
                    {
                        "title": _("App Integrations"),
                        "icon": "apps",
                        "link": reverse_lazy("admin:integration_appintegration_changelist"),
                    },
                    {
                        "title": _("User Integrations"),
                        "icon": "person_add",
                        "link": reverse_lazy("admin:integration_userintegration_changelist"),
                    },
                ],
            },
            {
                "title": _("Llm Manager"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Conversation tags"),
                        "icon": "tag",
                        "link": reverse_lazy("admin:llm_manager_conversationtag_changelist"),
                    },
                    {
                        "title": _("Conversations"),
                        "icon": "chat",
                        "link": reverse_lazy("admin:llm_manager_conversation_changelist"),
                    },
                    {
                        "title": _("Interaction History"),
                        "icon": "history",
                        "link": reverse_lazy("admin:llm_manager_interactionhistory_changelist"),
                    },
                    {
                        "title": _("LLMModels"),
                        "icon": "model_training",
                        "link": reverse_lazy("admin:llm_manager_llmmodels_changelist"),
                    },
                    {
                        "title": _("Messages"),
                        "icon": "message",
                        "link": reverse_lazy("admin:llm_manager_message_changelist"),
                    },
                    {
                        "title": _("Projects"),
                        "icon": "folder",
                        "link": reverse_lazy("admin:llm_manager_project_changelist"),
                    },
                ],
            },
            {
                "title": _("Miscellaneous"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Site contents"),
                        "icon": "article",
                        "link": reverse_lazy("admin:miscellaneous_sitecontent_changelist"),
                    },
                ],
            },
            {
                "title": _("Notification"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Email templates"),
                        "icon": "email",
                        "link": reverse_lazy("admin:notification_emailtemplate_changelist"),
                    },
                    {
                        "title": _("Notification logs"),
                        "icon": "receipt_long",
                        "link": reverse_lazy("admin:notification_notificationlog_changelist"),
                    },
                    {
                        "title": _("Notification preferences"),
                        "icon": "tune",
                        "link": reverse_lazy("admin:notification_notificationpreference_changelist"),
                    },
                    {
                        "title": _("Notification templates"),
                        "icon": "description",
                        "link": reverse_lazy("admin:notification_notificationtemplate_changelist"),
                    },
                    {
                        "title": _("Notification types"),
                        "icon": "category",
                        "link": reverse_lazy("admin:notification_notificationtype_changelist"),
                    },
                    {
                        "title": _("Notifications"),
                        "icon": "notifications",
                        "link": reverse_lazy("admin:notification_notification_changelist"),
                    },
                ],
            },
            {
                "title": _("Payment"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Features"),
                        "icon": "star",
                        "link": reverse_lazy("admin:payment_feature_changelist"),
                    },
                    {
                        "title": _("Manual Transactions"),
                        "icon": "receipt",
                        "link": reverse_lazy("admin:payment_manualtransaction_changelist"),
                    },
                    # {
                    #     "title": _("Payment logs"),
                    #     "link": reverse_lazy("admin:payment_paymentlog_changelist"),
                    # },
                    # {
                    #     "title": _("Payment methods"),
                    #     "link": reverse_lazy("admin:payment_paymentmethod_changelist"),
                    # },
                    # {
                    #     "title": _("Payments"),
                    #     "link": reverse_lazy("admin:payment_payment_changelist"),
                    # },
                    {
                        "title": _("Plans"),
                        "icon": "assignment",
                        "link": reverse_lazy("admin:payment_plan_changelist"),
                    },
                    {
                        "title": _("Payment Method Details"),
                        "icon": "credit_card",
                        "link": reverse_lazy("admin:payment_paymentmethoddetails_changelist"),
                    },
                    {
                        "title": _("Refunds"),
                        "icon": "money_off",
                        "link": reverse_lazy("admin:payment_refund_changelist"),
                    },
                    # {
                    #     "title": _("Stripe Customers"),
                    #     "link": reverse_lazy("admin:payment_stripecustomer_changelist"),
                    # },
                    # {
                    #     "title": _("Stripe Webhook Logs"),
                    #     "link": reverse_lazy("admin:payment_stripewebhooklog_changelist"),
                    # },
                    {
                        "title": _("Subscriptions"),
                        "icon": "subscriptions",
                        "link": reverse_lazy("admin:payment_subscription_changelist"),
                    },
                    {
                        "title": _("Subscription Requests"),
                        "icon": "request_page",
                        "link": reverse_lazy("admin:payment_subscriptionrequest_changelist"),
                    },
                ],
            },
            {
                "title": _("Periodic Task"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Clocked"),
                        "icon": "schedule",
                        "link": reverse_lazy("admin:django_celery_beat_clockedschedule_changelist"),
                    },
                    {
                        "title": _("Crontabs"),
                        "icon": "calendar_today",
                        "link": reverse_lazy("admin:django_celery_beat_crontabschedule_changelist"),
                    },
                    {
                        "title": _("Intervals"),
                        "icon": "timelapse",
                        "link": reverse_lazy("admin:django_celery_beat_intervalschedule_changelist"),
                    },
                    {
                        "title": _("Periodic Tasks"),
                        "icon": "repeat",
                        "link": reverse_lazy("admin:django_celery_beat_periodictask_changelist"),
                    },
                    {
                        "title": _("Solars"),
                        "icon": "wb_sunny",
                        "link": reverse_lazy("admin:django_celery_beat_solarschedule_changelist"),
                    },
                ],
            },
            {
                "title": _("Python Social Auth"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Associations"),
                        "icon": "link",
                        "link": reverse_lazy("admin:social_django_association_changelist"),
                    },
                    {
                        "title": _("Nonces"),
                        "icon": "hourglass_empty",
                        "link": reverse_lazy("admin:social_django_nonce_changelist"),
                    },
                    {
                        "title": _("User Social Auths"),
                        "icon": "person",
                        "link": reverse_lazy("admin:social_django_usersocialauth_changelist"),
                    },
                ],
            },
            {
                "title": _("Sites"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Sites"),
                        "icon": "public",
                        "link": reverse_lazy("admin:sites_site_changelist"),
                    },
                ],
            },
            {
                "title": _("Social Accounts"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Social Accounts"),
                        "icon": "group",
                        "link": reverse_lazy("admin:socialaccount_socialaccount_changelist"),
                    },
                    {
                        "title": _("Social Apps"),
                        "icon": "apps",
                        "link": reverse_lazy("admin:socialaccount_socialapp_changelist"),
                    },
                    {
                        "title": _("Social Tokens"),
                        "icon": "vpn_key",
                        "link": reverse_lazy("admin:socialaccount_socialtoken_changelist"),
                    },
                ],
            },
            {
                "title": _("Token Blacklist"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Blacklisted Tokens"),
                        "icon": "block",
                        "link": reverse_lazy("admin:token_blacklist_blacklistedtoken_changelist"),
                    },
                    {
                        "title": _("Outstanding Tokens"),
                        "icon": "check_circle",
                        "link": reverse_lazy("admin:token_blacklist_outstandingtoken_changelist"),
                    },
                ],
            },
            {
                "title": _("User"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Connected apps"),
                        "icon": "phonelink",
                        "link": reverse_lazy("admin:user_connectedapp_changelist"),
                    },
                    {
                        "title": _("Daily Message Usages"),
                        "icon": "chat_bubble_outline",
                        "link": reverse_lazy("admin:user_dailymessageusage_changelist"),
                    },
                    {
                        "title": _("Daily Token Usages"),
                        "icon": "data_usage",
                        "link": reverse_lazy("admin:user_dailytokenusages_changelist"),
                    },
                    {
                        "title": _("User Action Logs"),
                        "icon": "list_alt",
                        "link": reverse_lazy("admin:user_useractionlog_changelist"),
                    },
                    {
                        "title": _("User preferences"),
                        "icon": "tune",
                        "link": reverse_lazy("admin:user_userpreference_changelist"),
                    },
                    {
                        "title": _("User role mappings"),
                        "icon": "swap_horiz",
                        "link": reverse_lazy("admin:user_userrole_changelist"),
                    },
                    {
                        "title": _("User roles"),
                        "icon": "badge",
                        "link": reverse_lazy("admin:user_role_changelist"),
                    }
                ],
            }
        ]},

}


def environment_callback(request):
    """
    Callback has to return a list of two values represeting text value and the color
    type of the label displayed in top right corner.
    """
    return [_(f"{DJANGO_ENV}"), "warning"]
