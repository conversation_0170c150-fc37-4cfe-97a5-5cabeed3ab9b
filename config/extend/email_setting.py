
from config.env import env


DJANGO_DEFAULT_EMAIL_SERVICE = env(
    "DJANGO_DEFAULT_EMAIL_SERVICE", default="smtp")

DEFAULT_FROM_EMAIL = env(
    "DJANGO_DEFAULT_FROM_EMAIL", default="devxhub <<EMAIL>>")

EMAIL_APPS = []

# Email Configuration based on DJANGO_DEFAULT_EMAIL_SERVICE
if DJANGO_DEFAULT_EMAIL_SERVICE == "smtp":
    EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
    EMAIL_HOST = env("DJANGO_EMAIL_HOST", default="mailpit")
    EMAIL_PORT= env.int("DJANGO_EMAIL_PORT", default=1025)
    EMAIL_USE_TLS = env.bool("DJANGO_EMAIL_USE_TLS", default=False)
    EMAIL_USE_SSL = env.bool("DJANGO_EMAIL_USE_SSL", default=False) 
    EMAIL_HOST_USER = env("DJANGO_EMAIL_HOST_USER", default="")
    EMAIL_HOST_PASSWORD = env("DJANGO_EMAIL_HOST_PASSWORD", default="")

elif DJANGO_DEFAULT_EMAIL_SERVICE == "sendgrid":
    # Email Configuration for production based on Sendgrid
    EMAIL_BACKEND = "anymail.backends.sendgrid.EmailBackend"
    EMAIL_APPS = ["anymail"]
    ANYMAIL = {
        "SENDGRID_API_KEY": env("SENDGRID_API_KEY"),
        "SENDGRID_GENERATE_MESSAGE_ID": env("SENDGRID_GENERATE_MESSAGE_ID"),
        "SENDGRID_MERGE_FIELD_FORMAT": env("SENDGRID_MERGE_FIELD_FORMAT"),
        "SENDGRID_API_URL": env("SENDGRID_API_URL", default="https://api.sendgrid.com/v3/"),
    }
    if not env("SENDGRID_API_KEY"):
        raise ValueError(
            "Sendgrid API key is missing. Please set SENDGRID_API_KEY.")

elif DJANGO_DEFAULT_EMAIL_SERVICE == "amazon_ses":
    # Email Configuration for production based on Amazon SES
    EMAIL_BACKEND = "anymail.backends.amazon_ses.EmailBackend"
    EMAIL_APPS = ["anymail"]
    ANYMAIL = {
        "AMAZON_SES_CLIENT_PARAMS": {  # type: ignore
            "aws_access_key_id": env("AWS_SES_ACCESS_KEY_ID"),
            "aws_secret_access_key": env("AWS_SES_SECRET_ACCESS_KEY"),
            "region_name": env("AWS_SES_REGION_NAME", default="us-east-1"),
        },
    }
    if not env("AWS_SES_ACCESS_KEY_ID"):
        raise ValueError(
            "AWS SES access key is missing. Please set AWS_SES_ACCESS_KEY_ID.")

elif DJANGO_DEFAULT_EMAIL_SERVICE == "mailgun":
    # Email Configuration for production based on Mailgun
    EMAIL_BACKEND = "anymail.backends.mailgun.EmailBackend"
    EMAIL_APPS = ["anymail"]
    ANYMAIL = {
        "MAILGUN_API_KEY": env("MAILGUN_API_KEY"),
        "MAILGUN_API_URL": env("MAILGUN_API_URL", default="https://api.mailgun.net/v3"),
        "MAILGUN_SENDER_DOMAIN": env("MAILGUN_SENDER_DOMAIN", default="devxhub.com"),
        "MAILGUN_SEND_DEFAULTS": {
            "tracking": False,
        },
    }
    if not env("MAILGUN_API_KEY"):
        raise ValueError(
            "Mailgun API key is missing. Please set MAILGUN_API_KEY.")

CUSTOMER_SUPPORT_EMAIL = env(
    "CUSTOMER_SUPPORT_EMAIL", default="<EMAIL>")
