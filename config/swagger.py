from django.urls import path
from dxh_libraries.drf_yasg import get_schema_view, openapi
from dxh_common.permissions import AllowAny


# Define the OpenAPI schema information
api_info = openapi.Info(
    title="Admin API",
    default_version="v1",
    description="API documentation for version 1 of the admin project.",
    terms_of_service="https://devxhub.com/terms-of-use",
    contact=openapi.Contact(email="<EMAIL>"),
    license=openapi.License(name="GNU GPLv3"),
)

# Conditional permission for Swagger views
schema_view = get_schema_view(
    api_info,
    public=True,
    permission_classes=(AllowAny,),
)

# Swagger URL patterns
swagger_urlpatterns = [
    path("v1/swagger.json", schema_view.without_ui(cache_timeout=3600),
         name="schema-json"),
    path("v1/swagger.yaml", schema_view.without_ui(cache_timeout=3600),
         name="schema-yaml"),
    path("v1/swagger/", schema_view.with_ui("swagger",
         cache_timeout=3600), name="schema-swagger-ui"),
    path("v1/redoc/", schema_view.with_ui("redoc",
         cache_timeout=3600), name="schema-redoc"),
]
