"""
Development Django settings for config project.
"""

import socket
from config.env import env
from config.settings.base import *


DEBUG = env.bool("DJANGO_DEBUG", default=True)

# Local Database
DATABASES = {"default": env.db("DATABASE_URL")}

# Valkey and Celery
VALKEY_URL = env("VALKEY_URL", default="valkey://valkey:6379/0")
CELERY_BROKER_URL = env("CELERY_BROKER_URL", default="redis://valkey:6379/0")
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_valkey.core.ValkeyChannelLayer",
        "CONFIG": {
            "hosts": [VALKEY_URL],
        },
    },
}

# django-debug-toolbar
ENABLE_DEBUG_TOOLBAR = env.bool("ENABLE_DEBUG_TOOLBAR", default=True)

if ENABLE_DEBUG_TOOLBAR:
    INTERNAL_IPS = ["127.0.0.1", "********"]
    if env("USE_DOCKER"):

        hostname, _, ips = socket.gethostbyname_ex(socket.gethostname())
        INTERNAL_IPS += [".".join(ip.split(".")[:-1] + ["1"]) for ip in ips]

    INSTALLED_APPS += ["debug_toolbar"]  # noqa: F405
    MIDDLEWARE += ["debug_toolbar.middleware.DebugToolbarMiddleware"]  # noqa: F405
    DEBUG_TOOLBAR_CONFIG = {
        "DISABLE_PANELS": [
            "debug_toolbar.panels.redirects.RedirectsPanel",
            "debug_toolbar.panels.profiling.ProfilingPanel"
        ],
        "SHOW_TEMPLATE_CONTEXT": True,
    }
