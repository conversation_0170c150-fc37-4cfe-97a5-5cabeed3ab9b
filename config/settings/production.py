"""
Production Django settings for config project.
"""
from config.env import env
from config.settings.base import *
from config.extend.email_setting import *

DEBUG = env.bool("DJANGO_DEBUG", default=False)

# Database
DATABASES = {"default": env.db("DATABASE_URL")}


# Valkey and Celery
VALKEY_URL = env("VALKEY_URL", default="valkey://valkey:6379/0")
CELERY_BROKER_URL = env("CELERY_BROKER_URL", default="redis://valkey:6379/0")
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_valkey.core.ValkeyChannelLayer",
        "CONFIG": {
            "hosts": [VALKEY_URL],
        },
    },
}


# # Security
# SECURE_SSL_REDIRECT = True
# SESSION_COOKIE_SECURE = True
# CSRF_COOKIE_SECURE = True
# SECURE_HSTS_SECONDS = 31536000
# SECURE_HSTS_INCLUDE_SUBDOMAINS = True
# SECURE_HSTS_PRELOAD = True
# SECURE_HSTS_SECONDS = 31536000
# SECURE_HSTS_INCLUDE_SUBDOMAINS = True
# SECURE_CONTENT_TYPE_NOSNIFF = True
# SECURE_BROWSER_XSS_FILTER = True
# SESSION_COOKIE_SECURE = True
# CSRF_COOKIE_SECURE = True

# Email Configuration for production based
INSTALLED_APPS += EMAIL_APPS  # noqa: F405
