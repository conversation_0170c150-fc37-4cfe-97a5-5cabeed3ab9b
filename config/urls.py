from django.contrib import admin
from django.urls import path, include
from django.conf.urls.static import static
from django.conf.urls.i18n import i18n_patterns

from config.swagger import swagger_urlpatterns
from config import settings

# Admin routes
admin_patterns = [
    path(settings.ADMIN_URL, admin.site.urls),
]

# API routes
api_patterns = [
    path("auth/", include("apps.identity.urls", namespace="identity")),
    path("accounts/", include("allauth.urls")),
    path("users/", include("apps.user.urls", namespace="users")),
    path("core/", include("apps.core.urls", namespace="core")),
    path("llm-manager/", include("apps.llm_manager.urls", namespace="llm_manager")),
    path("file-manager/", include("apps.file_manager.urls", namespace="file_manager")),
    path("notifications/", include("apps.notification.urls", namespace="notifications")),
    path("payments/", include("apps.payment.urls", namespace="payments")),
    path("miscellaneous/", include("apps.miscellaneous.urls", namespace="miscellaneous")),
    path("integration/", include("apps.integration.urls", namespace="integration")),
]

# Combine URL patterns
urlpatterns = [
    path("i18n/", include("django.conf.urls.i18n")),
    path("api/", include(api_patterns)),
    path('', include('django_prometheus.urls')),
]

# Add admin routes with i18n
urlpatterns += i18n_patterns(*admin_patterns)

# Swagger routes (conditionally include for non-production)
if settings.DEBUG or settings.ENABLE_SWAGGER:
    urlpatterns += swagger_urlpatterns

# Serve media and static files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

    # django-debug-toolbar
    if settings.ENABLE_DEBUG_TOOLBAR and "debug_toolbar" in settings.INSTALLED_APPS:
        from debug_toolbar.toolbar import debug_toolbar_urls
        urlpatterns += debug_toolbar_urls()
