from celery.schedules import crontab
from config.env import env


BEAT_SCHEDULE = {
    # # Daily reminder emails at 9:00 AM
    # "daily-reminder-email-task": {
    #     "task": "notifications.tasks.send_notification_task",
    #     "schedule": crontab(
    #         hour=int(os.getenv("REMINDER_TASK_HOUR", 9)),
    #         minute=int(os.getenv("REMINDER_TASK_MINUTE", 0))
    #     ),
    #     "args": [],
    #     "options": {"queue": "email_queue"},
    # },
    "delete-users-after-30-days": {
        "task": "apps.user.tasks.delete_users_after_30_days",
        "schedule": crontab(hour=0, minute=0),
    },
    "send-subscription-renewal-reminders": {
        "task": "apps.payment.tasks.send_subscription_renewal_reminders",
        "schedule": crontab(hour=9, minute=0),  # Run once daily at 9:00 AM   
    },
    # Add more tasks here as needed
}
